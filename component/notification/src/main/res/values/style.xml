<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Notification_Button_Container">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginEnd">@dimen/dp12</item>
        <item name="android:layout_marginTop">@dimen/dp12</item>
    </style>

    <style name="Notification_Button" parent="couiTextAppearanceSmallButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">@dimen/notification_btn_height</item>
        <item name="android:paddingVertical">@dimen/dp2</item>
        <item name="android:minWidth">@dimen/notification_btn_min_width</item>
        <item name="android:maxWidth">@dimen/notification_btn_max_width</item>
        <item name="android:textSize">@dimen/sp14</item>
        <item name="android:gravity">center</item>
        <item name="android:lines">1</item>
        <item name="android:lineSpacingExtra">@dimen/sp4</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingHorizontal">@dimen/notification_btn_content_padding_horizontal</item>
    </style>
</resources>