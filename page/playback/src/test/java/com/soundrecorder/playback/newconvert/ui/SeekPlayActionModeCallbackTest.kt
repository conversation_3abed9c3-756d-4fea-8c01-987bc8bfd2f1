/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SeekPlayActionModeCallbackTest
 Description:
 Version: 1.0
 Date: 2023/05/25 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/25 1.0 create
 */

package com.soundrecorder.playback.newconvert.ui

import android.content.Context
import android.os.Build
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.newconvert.view.BackgroundTextView
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import io.mockk.mockk
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowFeatureOption::class, ShadowOS12FeatureUtil::class]
)
class SeekPlayActionModeCallbackTest {

    private var context: Context? = null
    private var mActivity: AppCompatActivity? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(AppCompatActivity::class.java).get()
    }

    @After
    fun tearDown() {
        context = null
        mActivity = null
    }

    @Test
    fun should_contains_when_onCreateActionMode() {
        val callback = Mockito.mock(SeekPlayActionModeCallback::class.java)
        val menu = Mockito.mock(Menu::class.java)
        val menuItem = Mockito.mock(MenuItem::class.java)
        Mockito.`when`(menu.findItem(android.R.id.copy)).thenReturn(menuItem)
        Mockito.`when`(menu.findItem(android.R.id.shareText)).thenReturn(menuItem)
        Mockito.`when`(menu.findItem(android.R.id.selectAll)).thenReturn(menuItem)
        callback.onCreateActionMode(null, menu)
    }

    @Test
    fun should_contains_when_onPrepareActionMode() {
        val callback = Mockito.mock(SeekPlayActionModeCallback::class.java)
        val menu = Mockito.mock(Menu::class.java)
        val menuItem = Mockito.mock(MenuItem::class.java)
        Mockito.`when`(menu.findItem(android.R.id.copy)).thenReturn(menuItem)
        Mockito.`when`(menu.findItem(android.R.id.shareText)).thenReturn(menuItem)
        Mockito.`when`(menu.findItem(android.R.id.selectAll)).thenReturn(menuItem)
        callback.onPrepareActionMode(null, menu)
    }

    @Test
    fun should_contains_when_onActionItemClicked() {
        val callback = Mockito.mock(SeekPlayActionModeCallback::class.java)
        callback.mItemTextView = BackgroundTextView(context!!)
        val copyMenuItem = Mockito.mock(MenuItem::class.java)
        Mockito.`when`(copyMenuItem.itemId).thenReturn(android.R.id.copy)
        callback.onActionItemClicked(null, copyMenuItem)
        val shareTextMenuItem = Mockito.mock(MenuItem::class.java)
        Mockito.`when`(shareTextMenuItem.itemId).thenReturn(android.R.id.shareText)
        callback.onActionItemClicked(null, shareTextMenuItem)
        val selectAllMenuItem = Mockito.mock(MenuItem::class.java)
        Mockito.`when`(selectAllMenuItem.itemId).thenReturn(android.R.id.selectAll)
        callback.onActionItemClicked(null, shareTextMenuItem)
    }

    @Test
    fun should_contains_when_genStartPlayTime() {
        val callback = Mockito.mock(SeekPlayActionModeCallback::class.java)
        val contentItem = mockk<ConvertContentItem>()
        callback.genStartPlayTime(contentItem, 1)
    }
}