/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ShadowOS12FeatureUtil
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

// OPLUS Java File Skip Rule:RegexpOnFilename
package com.soundrecorder.record.shadows;

import com.soundrecorder.base.utils.OS12FeatureUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(OS12FeatureUtil.class)
public class ShadowOS12FeatureUtil {

    @Implementation
    public static boolean isSuperSoundRecorderEpicEffective() {
        return true;
    }


    @Implementation
    public static boolean readFeatureByOplusFeature() {
        return true;
    }


    @Implementation
    public static boolean isFindX4AndNotConfidential() {
        return true;
    }


    @Implementation
    public static boolean isColorOs12() {
        return false;
    }

    @Implementation
    public static boolean isColorOS15OrLater() {
        return true;
    }
}
