import app from '@system.app'
import device from '@system.device'

import { reportDataToClouds } from './api'

export const CARD_TYPE = {
  SMALL: '2x2',
  MEDIUM: '4x2',
  LARGE: '4x4'
}

export const EVENT_TYPE = {
  EXP: '曝光',
  CLICK: '点击'
}

class DataReport {
  constructor() {
    this.appInfo = app.getInfo() || {}
    this.platform = device.platform || {}
    this.host = device.host || {}
  }

  /**
   * 需要上报的基础数据信息，以下信息可按需上报
   * @return {String} 返回基础信息对象
   */
  get _baseInfo() {
    return {
      versionName: this.appInfo.versionName, // 快应用版本名称
      versionCode: this.appInfo.versionCode, // 快应用版本号
      platformVersionName: this.platform.versionName, // 运行平台版本名称
      platformVersionCode: this.platform.versionCode, // 运行平台版本号
      hostPackage: this.host.package, // 宿主的包名, 如调试器的名称是 org.hapjs.debugger
      hostVersionName: this.host.versionName, // 宿主的版本名称
      hostVersionCode: this.host.versionCode // 宿主的版本号
    }
  }

  /**
   * 埋点上报
   * @param cardType 卡片类型: 2x2、4x2、4x4
   * @param data 埋点数据对象
   */
  report(cardType, data) {
    const reportData = {
      ...this._baseInfo,
      cardType,
      ...data
    }
    console.info('埋点上报', JSON.stringify(reportData))
    reportDataToClouds(reportData)
  }
}

const dataReport = new DataReport()

export const report = dataReport.report.bind(dataReport)
