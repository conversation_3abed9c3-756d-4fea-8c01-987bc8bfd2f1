<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.button.COUIButton xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tv_name"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/record_group_label_item_height"
    android:layout_marginStart="@dimen/record_group_label_item_margin_horizontal"
    android:layout_marginEnd="@dimen/record_group_label_item_margin_horizontal"
    android:background="@null"
    android:fontFamily="OPlusSans 3.0"
    android:minWidth="0dp"
    android:paddingStart="@dimen/record_group_label_item_padding_horizontal"
    android:paddingEnd="@dimen/record_group_label_item_padding_horizontal"
    android:textColor="@color/coui_color_label_primary"
    android:textSize="@dimen/record_group_label_item_text_size"
    app:animEnable="true"
    app:drawableColor="@color/coui_color_card"
    app:drawableRadius="@dimen/record_group_label_item_radius"
    app:scaleEnable="true" />