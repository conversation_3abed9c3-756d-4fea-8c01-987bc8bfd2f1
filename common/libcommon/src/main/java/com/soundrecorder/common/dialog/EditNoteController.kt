/******************************************************************
 * Copyright (C), 2020-2021, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - EditNoteController.java
 * Description:
 * Version: 1.0
 * Date :  2020/5/9
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    Yongjiang,Lu      2020/5/9          1.0         build this module
 ********************************************************************/
package com.soundrecorder.common.dialog

import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.R
import com.soundrecorder.common.utils.AudioNameUtils
import com.soundrecorder.common.utils.SubRecorderTextUtils
import com.soundrecorder.common.widget.TalkBackEditTextView

open class EditNoteController {
    var isNameEdit = false
    var title: String? = null
    var textNote: TextView? = null
    var colorEditText: COUIEditText? = null
    var saveEnabledBlock: ((Boolean) -> Unit)? = null
    var mTextWatcher: TextWatcher? = null
    private val TAG = "EditNoteController"
    private var mTextWatcherProxy: TextWatcher? = null

    fun init() {

        isNameEdit = false
        SubRecorderTextUtils.addIllgalFileNameInputFilter(colorEditText)
        SubRecorderTextUtils.addEmojiInputFilter(colorEditText)
        initEditTextWatcher()
        colorEditText?.addTextChangedListener(mTextWatcherProxy)
        colorEditText?.isVerticalScrollBarEnabled = false
        colorEditText?.maxLines = 2
        setNoteTextSize(textNote)

        when {
            SubRecorderTextUtils.containsEmoji(title) -> {
                colorEditText?.setText("")
                showTextNote(R.string.notify_illegal_emoji_new)
            }

            SubRecorderTextUtils.containsIllegalCharFileName(title) -> {
                colorEditText?.setText("")
                showTextNote(R.string.error_strange_name)
            }

            (title?.length ?: 0) > INPUT_MAX_LENGTH -> {
                colorEditText?.setText("")
                showTextNote(R.string.namelength_outofrange)
            }

            else -> try {
                if (title != null) {
                    colorEditText?.setText(title)
                    colorEditText?.setSelection(title?.length ?: 0)
                }
            } catch (e: IndexOutOfBoundsException) {
                DebugUtil.e(TAG, "mDefaultName = $title", e)
                colorEditText?.setText("")
                showTextNote(R.string.notify_illegal_emoji_new)
            }
        }
    }

    private fun setNoteTextSize(note: TextView?) {
        val context = note?.context ?: return
        val spSize = context.resources.getDimension(R.dimen.sp10)
        val fontScale: Float = context.resources.configuration.fontScale
        val textSize = COUIChangeTextUtil.getSuitableFontSize(spSize, fontScale, COUIChangeTextUtil.G2)
        note.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
    }

    open fun initEditTalkBack() {
        val desText = StringBuffer()
        val context = BaseApplication.getAppContext()
        desText.append(context?.getString(R.string.talkback_input_record_name))
        desText.append(colorEditText?.text)
        (colorEditText as? TalkBackEditTextView)?.setAccessibilityTouchHelper(desText.toString())
    }

    open fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        isNameEdit = true
        textNote?.visibility = View.INVISIBLE
        if (SubRecorderTextUtils.isContainEmoji()) {
            showTextNote(R.string.notify_illegal_emoji_new)
        }
        if (SubRecorderTextUtils.isContainIllgalFileName()) {
            showTextNote(R.string.error_strange_name)
        }
        val tmp = s.toString()
        val len = tmp.length
        if (len > AudioNameUtils.MAX_CHAR_LENGTH) {
            if (count - before > 0) {
                colorEditText?.text?.delete(start + before, start + count)
            }
            showTextNote(R.string.namelength_outofrange)
            return
        }
        invokeSaveEnable(!TextUtils.isEmpty(s))
    }


    private fun initEditTextWatcher() {
        mTextWatcherProxy = object : TextWatcher {
            override fun afterTextChanged(p0: Editable?) {
                mTextWatcher?.afterTextChanged(p0)
            }

            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                mTextWatcher?.beforeTextChanged(p0, p1, p2, p3)
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                mTextWatcher?.onTextChanged(s, start, before, count)
                <EMAIL>(s, start, before, count)
            }
        }
    }


    fun showTextNote(resId: Int) {
        textNote?.setText(resId)
        textNote?.visibility = View.VISIBLE
        SubRecorderTextUtils.setContainEmoji(false)
        SubRecorderTextUtils.setContainIllgalFileName(false)
    }


    private fun invokeSaveEnable(enable: Boolean) {
        saveEnabledBlock?.invoke(enable)
    }

    fun release() {
        colorEditText?.removeTextChangedListener(mTextWatcherProxy)
        textNote = null
        mTextWatcher = null
        mTextWatcherProxy = null
        colorEditText = null
        saveEnabledBlock = null
    }

    companion object {
        private const val INPUT_MAX_LENGTH = NumberConstant.NUM_50
    }
}