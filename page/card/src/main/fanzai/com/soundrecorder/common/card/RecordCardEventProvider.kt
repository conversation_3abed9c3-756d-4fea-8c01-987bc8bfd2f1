/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordCardEventProvider
 * * Description :  流体云卡片创建失败的回调 + 流体云卡片按钮点击事件监听call method
 * * Version     : 1.0
 * * Date        : 2023/7/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card

import android.os.Binder
import android.os.Bundle
import android.text.TextUtils
import com.oplus.pantanal.seedling.SeedlingCardEventProvider
import com.oplus.pantanal.seedling.bean.CardCreateErrorBean
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.card.api.SeedlingSdkApi
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction

class RecordCardEventProvider : SeedlingCardEventProvider() {

    companion object {
        private const val TAG = "RecordCardEventProvider"
        private const val FROM_CREATE_SEEDLING_CARD_ERROR = "CreateSeedlingCardError"

        //泛在胶囊流体云卡片按钮点击事件，见RecordSeedlingCardWidgetProvider
        private const val SYSTEM_UI_PKG_NAME = "com.android.systemui"
        private const val LAUNCHER_PKG_NAME = "com.android.launcher"
        private const val ASSISTANT_PKG_NAME = "com.coloros.assistantscreen"
        private const val CALL_METHOD_ADD_LABEL = "addLabel"
        private const val CALL_METHOD_SWITCH_RECORD_STATUS = "pauseOrResume"
        private const val CALL_METHOD_RECORD_SAVE = "saveAudio"
        private const val CALL_METHOD_START_RECORD_SERVICE = "startOrPause"
    }

    /**
     * RecordStatusBarUpdater中registerResultCallBack后，当创建卡片失败会回调该方法
     */
    override fun onCardCreateErrorInfo(cardErrorInfo: CardCreateErrorBean) {
        DebugUtil.d(TAG, "onCardCreateErrorInfo: cardErrorInfo = $cardErrorInfo", true)
        RecorderViewModelAction.showOrHideStatusBar(FROM_CREATE_SEEDLING_CARD_ERROR)
    }

    override fun onServiceClose(serviceId: String?, reason: String?) {
        //do nothing
        DebugUtil.d(TAG, "onServiceClose: serviceId = $serviceId ,reason = $reason")
    }

    @Suppress("TooGenericExceptionThrown")
    override fun call(authority: String, method: String, arg: String?, extras: Bundle?): Bundle? {
        DebugUtil.i(TAG, "---call method = $method, caller = $callingPackage", true)
        if (!checkPackageAuth()) {
            DebugUtil.e(TAG, "package name not match")
            throw RuntimeException("package name not match")
        }
        when (method) {
            CALL_METHOD_ADD_LABEL -> onClickMarkBtn()
            CALL_METHOD_SWITCH_RECORD_STATUS -> onClickRecordBtn()
            CALL_METHOD_RECORD_SAVE -> onClickSaveBtn()
            CALL_METHOD_START_RECORD_SERVICE -> onClickRecordBtnFromSmallCard()
        }
        return super.call(method, arg, extras)
    }

    private fun checkPackageAuth(): Boolean {
        val page = callingPackage
        //桌面/负一屏订阅卡小卡
        return TextUtils.equals(page, SYSTEM_UI_PKG_NAME) || TextUtils.equals(page, ASSISTANT_PKG_NAME)
                || TextUtils.equals(page, LAUNCHER_PKG_NAME)
    }

    private fun onClickRecordBtn() {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }

        if (!RecorderViewModelAction.checkModeCanRecord(true)) {
            DebugUtil.w(TAG, "onClickRecordBtn, but is in call", false)
            return
        }

        RecorderViewModelAction.switchRecorderStatus("")
        SeedlingCardBuryPointUtil.addSeedlingCardFluidRecordBtnClickEvent()
    }


    /**
     * //[OOS-EDIT]接收录音按钮事件
     */
    private fun onClickRecordBtnFromSmallCard() {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }

        if (RecorderViewModelAction.isAlreadyRecording()) {
            RecorderViewModelAction.switchRecorderStatus("")
        } else {
            DebugUtil.e(TAG, "Not Recording, status error.")
        }
    }

    private fun onClickSaveBtn() {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }
        //[OOS-EDIT]传递save到卡片
        if (RecordSeedlingSmallCardWidgetProvider.getSeedlingCard() != null) {
            RecordSeedlingSmallCardWidgetProvider.saveClick = true
        }
        if (!RecorderViewModelAction.hasInitRecorderService()) {
            DebugUtil.w(TAG, "onClickSaveBtn hasInitRecorderService false", false)
            return
        }

        BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD_SEEDLING_CARD)
        //从流体云卡点击保存，强制设为true
        SeedlingSdkApi.osVersion.onSeedlingCardShowChangeWhenSave(true)
        RecorderViewModelAction.saveRecordInfo(saveRecordFromWhere = RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_SEEDLING_CARD)
    }

    private fun onClickMarkBtn() {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }
        if (!RecorderViewModelAction.hasInitRecorderService()) {
            return
        } else if (!RecorderViewModelAction.isAlreadyRecording()) {
            return
        } else if (RecorderViewModelAction.checkMarkDataMoreThanMax()) {
            ToastManager.showShortToast(context, R.string.photo_mark_recommend_mark_limit)
            return
        } else if (!RecorderViewModelAction.isMarkEnabledFull()) {
            return
        } else {
            val token = Binder.clearCallingIdentity()
            RecorderViewModelAction.addMark(MarkMetaData())
            Binder.restoreCallingIdentity(token)
            SeedlingCardBuryPointUtil.addSeedlingCardMarkBtnClickEvent()
        }
    }
}