/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareTextContent
 * * Description: ShareTextContent
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.share

import android.view.View

import com.soundrecorder.common.databean.Record

sealed class ShareType

/**
 * 分享到文档应用
 * @param isShowSwitchInConvertModel 区分是否需要处理RoleName
 */
data class ShareTypeDoc(val isShowSwitchInConvertModel: Boolean) : ShareType()

/**
 * 分享到便签
 * @param hasImg 区分是否包含图片
 */
data class ShareTypeNote(val hasImg: Boolean) : ShareType()

/**
 * 链接分享
 * @param link   分享后查看的web链接
 * @param record 生成链接内容对应的record
 */
data class ShareTypeLink(var link: String? = null, var record: Record? = null) : ShareType()

/**
 * 纯文本分享
 * @param contentString 预览界面拼接好的的文本数据
 */
data class ShareTypeText(val contentString: String) : ShareType() {
    override fun toString(): String {
        return this.javaClass.simpleName
    }
}

/**
 * 复制全文
 */
data object ShareTypeCopy : ShareType()

/**
 * 分享音频文件与转写文本
 * @param anchor 设置弹窗跟手
 */
data class ShareTypeRecorderAndText(val anchor: View?) : ShareType()

/**
 * 复制摘要全文
 */
data class ShareSummaryCopy(val contentString: String) : ShareType()
