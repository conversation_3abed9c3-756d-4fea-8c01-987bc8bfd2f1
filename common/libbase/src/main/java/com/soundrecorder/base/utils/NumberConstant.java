/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: NumberConstant
 * Description:
 * Version: 1.0
 * Date: 2018/5/9
 * Author: <PERSON><PERSON><PERSON>@ROM.CommApp.Mms
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/7/17 1.0 create
 */

package com.soundrecorder.base.utils;

public class NumberConstant {
    public static final int NUM_U1 = -1;
    public static final int NUM_0 = 0;
    public static final int NUM_1 = 1;
    public static final int NUM_2 = 2;
    public static final int NUM_3 = 3;
    public static final int NUM_4 = 4;
    public static final int NUM_5 = 5;
    public static final int NUM_8 = 8;
    public static final int NUM_10 = 10;
    public static final int NUM_16 = 16;
    public static final int NUM_20 = 20;
    public static final int NUM_30 = 30;
    public static final int NUM_50 = 50;
    public static final int NUM_55 = 55;
    public static final int NUM_60 = 60;

    public static final int NUM_90 = 90;
    public static final int NUM_99 = 99;
    public static final int NUM_100 = 100;
    public static final int NUM_120 = 120;
    public static final int NUM_180 = 180;
    public static final int NUM_200 = 200;
    public static final int NUM_240 = 240;
    public static final int NUM_255 = 255;
    public static final int NUM_300 = 300;
    public static final int NUM_500 = 500;
    public static final int NUM_1000 = 1000;

    public static final long NUM_L1000 = 1000L;
    public static final long NUM_L2000 = 2000L;
    public static final int NUM_10000 = 10000;

    public static final float NUM_F0_0 = 0.0f;
    public static final float NUM_F0_1 = 0.1f;
    public static final float NUM_F0_2 = 0.2f;
    public static final float NUM_F0_3 = 0.3f;
    public static final float NUM_F0_15 = 0.15f;
    public static final float NUM_F0_5 = 0.5f;
    public static final float NUM_F0_54 = 0.54F;
    public static final float NUM_F0_8 = 0.8F;
    public static final float NUM_F0_98 = 0.98F;
    public static final float NUM_F1_0 = 1.0f;
    public static final float NUM_F1_15 = 1.15f;
    public static final float NUM_F2_0 = 2.0f;
    public static final float NUM_F2_3 = 2.3f;
    public static final float NUM_F4_0 = 4.0f;
    public static final float NUM_F12_0 = 12.0f;
    public static final float NUM_F16_0 = 16.0f;
    public static final float NUM_F24_0 = 24.0f;
    public static final float NUM_F26_0 = 26.0f;
    public static final float NUM_F36_0 = 36.0f;
    public static final float NUM_F68_0 = 68.0f;
    public static final float NUM_F100 = 100f;
    public static final float NUM_F534_0 = 534.0f;

    public static final int NUMBER_RECORD_AMR_LIMIT = 2048;
    public static final int NUMBER_RECORD_OTHER_LIMIT = 10240;

    public static final Long NUMBER_DURATION_350 = 350L;
    public static final Long NUMBER_L100 = 100L;
    public static final Long NUMBER_L150 = 150L;

    public static final Long NUMBER_L300 = 300L;
    public static final Long NUMBER_L400 = 400L;
    public static final Long NUMBER_L450 = 450L;
}
