/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  DeleteFileDialogUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.fileoperator.delete

import android.app.Activity
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.fileoperator.CheckOperatorWithPermission
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class DeleteFileDialogUtilTest {

    private var mController: ActivityController<Activity>? = null
    private var instance: DeleteFileDialogUtil? = null

    @Before
    fun setUp() {
        mController = Robolectric.buildActivity<Activity>(Activity::class.java)
        instance = DeleteFileDialogUtil(null)
    }

    @After
    fun tearDown() {
        instance = null
        mController = null
    }

    @Test
    fun should_success_when_delete() {
        val deleteList = ArrayList<Record>()
        instance?.delete(mController!!.get()!!, deleteList, true, false)

        deleteList.add(Record())
        instance?.delete(mController!!.get()!!, deleteList, true, false)
        val permission =
            Whitebox.getInternalState<CheckOperatorWithPermission>(instance, "mOperatePermission")
        Assert.assertNotNull(permission)
    }

    @Test
    fun should_equals_when_deleteRecord() {
        val deleteList = ArrayList<Record>()
        deleteList.add(Record().also { it.fileId = "1" })
        val result = Whitebox.invokeMethod<Int>(
            instance, "deleteRecord", mController!!.get()!!, deleteList, true, false
        )
        Assert.assertEquals(1, result)
    }

    @Test
    fun should_equals_when_resetOperating() {
        val operatePermission = CheckOperatorWithPermission(mController?.get())
        Whitebox.setInternalState(instance, "mOperatePermission", operatePermission)
        instance?.resetOperating()
        Assert.assertEquals(false, instance?.getOperating())
    }

    @Test
    fun should_null_when_release() {
        instance?.release()
        val operatePermission =
            Whitebox.getInternalState<CheckOperatorWithPermission>(instance, "mOperatePermission")
        Assert.assertNull(operatePermission)
    }
}