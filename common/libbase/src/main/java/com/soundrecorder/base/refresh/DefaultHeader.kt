/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DefaultHeader.kt
 ** Description: The default refresh header, you can directly copy the view class
 **              in the change header
 **
 ** Version: 1.1
 ** Date: 2019-04-30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2019-04-30   1.1         Convert this demo into Kotlin
 ********************************************************************************/

package com.soundrecorder.base.refresh

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants
import com.coui.appcompat.vibrateutil.VibrateUtils
import com.soundrecorder.base.R
import com.soundrecorder.base.databinding.PullRefreshHeaderDefaultBinding
import com.soundrecorder.base.utils.DebugUtil

class DefaultHeader(context: Context?, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    BaseLoadingView(context!!, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "DefaultHeader"
        private const val DEFAULT_START_ROTATION = 270f
        private const val ROTATION_ANGLE = 90f
        private const val HIDE_MIN_OFFSET_Y = 30f
        private const val FAST_SCROLLER_DISTANCE = 60f
    }

    var mDistanceBeginAnimation = 0

    /*Refresh the state of the header*/
    private var mStatus = 0

    /*Head layout height*/
    override var loadingViewHeight = 0
        private set

    /*Layout offset*/
    private var mTotalOffset = 0f

    /*-----------Refresh header layout view content---------------*/

    private val mIsLinearMotorVersion: Boolean
    private var lastDragY = 0f
    private var binding: PullRefreshHeaderDefaultBinding

    init {
        loadingViewHeight = context?.resources?.getDimensionPixelSize(R.dimen.default_height)!!
        mDistanceBeginAnimation = loadingViewHeight
        mDragDistanceThreshold = loadingViewHeight * 2
        mStatus = HEADER_DRAG
        //Add header content
        val view =
            LayoutInflater.from(context).inflate(R.layout.pull_refresh_header_default, this, false)
        binding = PullRefreshHeaderDefaultBinding.bind(view)
        val lp = LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, loadingViewHeight)
        addView(view, lp)

        mIsLinearMotorVersion = VibrateUtils.isLinearMotorVersion(context)
        COUIDarkModeUtil.setForceDarkAllow(this, false)
        if (COUIDarkModeUtil.isNightMode(context)) {
            binding.pullRefreshLoadingView.apply {
                imageAssetsFolder = "images_night"
                setAnimation("loading_night.json")
            }
        } else {
            binding.pullRefreshLoadingView.apply {
                imageAssetsFolder = "images"
                setAnimation("loading.json")
            }
        }
        binding.pullRefreshLoadingView.rotation = DEFAULT_START_ROTATION
    }

    private fun setTouchFeedback() {
        if (mIsLinearMotorVersion) {
            performHapticFeedback(COUIHapticFeedbackConstants.GRANULAR_SHORT_VIBRATE)
        } else {
            performHapticFeedback(COUIHapticFeedbackConstants.KEYBOARD_TOUCH_FEEDBACK)
        }
    }

    override fun releaseToRefresh() {
        //Start loading animation after letting go.
        binding.pullRefreshLoadingView.postDelayed({
            binding.pullRefreshLoadingView.resumeAnimation()
        }, 100)
    }

    /**
     * Pass the distance of the move
     */
    override fun handleDrag(dragY: Float) {
        mTotalOffset = dragY
        if (mCanTranslation) {
            translationY = dragY
        }
        if (mStatus == HEADER_REFRESHING) {
            // it is refreshing
            return
        }
        binding.pullRefreshLoadingView.apply {
            visibility = if ((dragY <= HIDE_MIN_OFFSET_Y) || (lastDragY - dragY > FAST_SCROLLER_DISTANCE)) {
                if (isAnimating) {
                    pauseAnimation()
                }
                View.INVISIBLE
            } else {
                View.VISIBLE
            }
        }
        //DebugUtil.e(TAG, "handleDrag $dragY  ; $mStatus")
        if (dragY <= 0) {
            // Back to the initial position
            mStatus = HEADER_DRAG
            binding.pullRefreshLoadingView.apply {
                rotation = DEFAULT_START_ROTATION
                if (isAnimating) {
                    pauseAnimation()
                }
                progress = 0f
            }
        }

        if (mStatus == HEADER_DRAG) {
            // Start dragging
            if (dragY > mDistanceBeginAnimation) {
                val totalRotateDistance = mDragDistanceThreshold - mDistanceBeginAnimation
                val rotateDistance = dragY - mDistanceBeginAnimation
                val rotateProportion =
                    (rotateDistance / totalRotateDistance).coerceAtMost(1f).coerceAtLeast(0f)
                val r = rotateProportion * ROTATION_ANGLE + DEFAULT_START_ROTATION
                binding.pullRefreshLoadingView.apply {
                    if (!isAnimating) {
                        rotation = r
                    }
                }
            }

            if (dragY >= mDragDistanceThreshold) {
                // Once the refresh head height is exceeded
                mStatus = HEADER_RELEASE
                setTouchFeedback()
                binding.pullRefreshLoadingView.apply {
                    visibility = View.VISIBLE
                }
            }
        }
        if (mStatus == HEADER_RELEASE) {
            // Has not released and dragged back
            binding.pullRefreshLoadingView.rotation = (dragY - mDragDistanceThreshold) / 2
            if (dragY <= mDragDistanceThreshold) {
                // Once below the refresh head height
                mStatus = HEADER_DRAG
                binding.pullRefreshLoadingView.visibility = View.VISIBLE
            }
        }
        lastDragY = dragY
    }

    override val isRefreshing: Boolean
        get() = mStatus == HEADER_REFRESHING

    override fun doRefresh(): Boolean {
        return mStatus == HEADER_REFRESHING && mTotalOffset == mDragDistanceThreshold.toFloat()
    }

    override fun setParent(parent: ViewGroup?) {
        val lp =
            LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        parent?.addView(this, lp)
    }

    override fun checkRefresh(): Boolean {
        return if ((mStatus == HEADER_RELEASE || mStatus == HEADER_REFRESHING) && mTotalOffset >= loadingViewHeight) {
            mStatus = HEADER_REFRESHING
            binding.pullRefreshLoadingView.clearAnimation()
            true
        } else {
            false
        }
    }

    override fun refreshCompleted() {
        DebugUtil.e(TAG, "refreshCompleted")
        mStatus = HEADER_COMPLETED
        binding.pullRefreshLoadingView.apply {
            clearAnimation()
            visibility = View.VISIBLE
        }
    }

    override fun autoRefresh() {
        DebugUtil.e(TAG, "autoRefresh")
        mStatus = HEADER_REFRESHING
        binding.pullRefreshLoadingView.apply {
            clearAnimation()
            visibility = View.VISIBLE
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        binding.pullRefreshLoadingView.apply {
            if (mStatus == HEADER_REFRESHING) {
                resumeAnimation()
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        binding.pullRefreshLoadingView.apply {
            if (isAnimating) {
                pauseAnimation()
            }
        }
    }

    override fun setRefreshEnable(enable: Boolean) {
        if (enable) {
            if (isRefreshing) {
                binding.pullRefreshLoadingView.visibility = View.VISIBLE
            }
        } else {
            binding.pullRefreshLoadingView.visibility = View.INVISIBLE
        }
    }
}