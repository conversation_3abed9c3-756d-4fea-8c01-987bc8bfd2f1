/************************************************************
 * Copyright 2000-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : RecorderServiceTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LiKun
 * Date           : 2019-08-07
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-08-07, <PERSON><PERSON><PERSON>, create
 ************************************************************/

package com.soundrecorder.recorderservice;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.robolectric.Shadows.shadowOf;

import android.app.Notification;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.os.Build;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.utils.AddonAdapterCompatUtil;
import com.soundrecorder.common.databean.MarkMetaData;
import com.soundrecorder.common.utils.SettingsAdapter;
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant;
import com.soundrecorder.recorderservice.manager.DiskStorageChecker;
import com.soundrecorder.recorderservice.manager.MuteModeOperator;
import com.soundrecorder.recorderservice.manager.RecordExpandController;
import com.soundrecorder.recorderservice.manager.RecordProcessController;
import com.soundrecorder.recorderservice.manager.RecordStatusManager;
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption;
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.recorderservice.shadows.ShadowOplusCompactUtil;
import com.soundrecorder.recorderservice.shadows.ShadowOplusUsbEnvironment;
import com.soundrecorder.recorderservice.shadows.ShadowOppoRecorder;
import com.soundrecorder.recorderservice.shadows.ShadowWakeLockManager;
import com.soundrecorder.wavemark.mark.MarkHelper;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ServiceController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowStatFs;
import org.robolectric.shadows.ShadowToast;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {ShadowOplusUsbEnvironment.class,
                ShadowWakeLockManager.class, ShadowOS12FeatureUtil.class,
                ShadowFeatureOption.class, ShadowOplusCompactUtil.class})
public class RecorderServiceTest {
    private static final String GEN_SAMPLE_CONTENT_VALUES = "genSampleContentValues";
    private static final String DATE_ADDED = "date_added";
    private static final String CONTEXT = "mContext";
    private static final String PATH_OF_FILE = "\\path\\of\\file";
    private static final String SAMPLE_FILE = "sampleFile";
    private static final String MUTE_SETTED = "mMuteSetted";
    private static final String GET_NOTIFICATION_FOR_N = "getNotificationForN";
    private static final String TEST = "test";
    private static final int A_MB = 1 * 1024 * 1024;
    private static final String PAGE_FROM_NAME = "launchFrom";
    private static final String PAGE_FROM_LAUNCHER = "launcher";
    private static final String PAGE_FROM_CALL = "call";
    private static final String PAGE_FROM_SLIDE_BAR = "slideBar";
    private static final String PAGE_FROM_BREENO = "breeno";
    private static final String PAGE_FROM_BREENO_FRONT = "xiaobuzhushou_front";
    private static final String PAGE_FROM_SMALL_CARD = "smallCard";
    private static final String PAGE_FROM_BRACKET_SPACE = "bracketSpace";
    private static final String PAGE_FROM_DRAGON_FLY = "service_from_app_card";

    private RecorderService mRecorderService;
    private Context mContext;
    private RecordProcessController mRecordProcessController = null;
    private RecordExpandController.RecordConfig mRecordConfig = null;
    private RecordExpandController.OtherConfig mOtherConfig = null;
    private MuteModeOperator.MuteConfig mMuteConfig = null;
    private RecordExpandController.RecordFileWraper fileWraper = null;
    private RecordExpandController mController = null;
    private MuteModeOperator mOperator = null;
    private MockedStatic<TextUtils> mMockStaticTextUtils;


    @Before
    public void setUp() {
        mRecorderService = new RecorderService();
        mContext = ApplicationProvider.getApplicationContext();
        mRecordConfig = new RecordExpandController.RecordConfig();
        mOtherConfig = new RecordExpandController.OtherConfig();
        mMuteConfig = new MuteModeOperator.MuteConfig(true, true);
        fileWraper = new RecordExpandController.RecordFileWraper(null, null, null, null, mRecordConfig);
        mController = new RecordExpandController(mRecordConfig, mOtherConfig, mMuteConfig);
        mOperator = new MuteModeOperator(mContext, mMuteConfig, null);
        mRecordProcessController = new RecordProcessController(mRecorderService, mRecordConfig, mOtherConfig, mMuteConfig);
        mRecordProcessController.initArgs();
        mRecordProcessController.initHandlerThread();
        mMockStaticTextUtils = Mockito.mockStatic(TextUtils.class);
        mMockStaticTextUtils.when(() -> TextUtils.isEmpty(anyString())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) {
                CharSequence a = invocation.getArgument(0);
                if (a == null || a.length() == 0) {
                    return true;
                }
                return false;
            }
        });
    }

    @After
    public void release() {
        if (mMockStaticTextUtils != null) {
            mMockStaticTextUtils.close();
            mMockStaticTextUtils = null;
        }
    }

    @Test
    public void should_returnContentValues_when_genSampleContentValues() throws Exception {
        Whitebox.setInternalState(mRecorderService, mRecordProcessController);
        Whitebox.setInternalState(mRecordProcessController, mController);
        Whitebox.setInternalState(mController, fileWraper);
        ContentValues result = (ContentValues) Whitebox.invokeMethod(mController, GEN_SAMPLE_CONTENT_VALUES, fileWraper, "");
        Assert.assertNotNull(result);
        Assert.assertTrue(result.containsKey(DATE_ADDED));
    }

    @Test
    public void should_returnNull_when_getRecordFilePath_with_fileIsNull() {
        String recordFilePath = mRecorderService.getRecordFilePath();
        Assert.assertNull(recordFilePath);
    }

    @Test
    public void should_returnFilePath_when_getRecordFilePath_with_fileIsNotNull() {
        File file = new File(PATH_OF_FILE);
        Whitebox.setInternalState(mRecorderService, mRecordProcessController);
        Whitebox.setInternalState(mRecordProcessController, mController);
        Whitebox.setInternalState(mController, fileWraper);
        Whitebox.setInternalState(fileWraper, SAMPLE_FILE, file);
        String recordFilePath1 = mRecorderService.getRecordFilePath();
        assertEquals(PATH_OF_FILE, recordFilePath1);
    }

    @Test
    public void should_setRingerModeOne_when_setMute_with_muteIsTrue() {
        Whitebox.setInternalState(mRecorderService, CONTEXT, mContext);
        Whitebox.setInternalState(mController, mOperator);
        mOperator.setMute(true);
        AudioManager audioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        boolean muteSetted = Whitebox.getInternalState(mOperator, MUTE_SETTED);
        Assert.assertTrue(muteSetted);
        assertEquals(AudioManager.RINGER_MODE_VIBRATE, audioManager.getRingerMode());
    }

    @Test
    public void should_setRingerModeOne_when_setMute_with_muteIsFalse() {
        Whitebox.setInternalState(mRecorderService, CONTEXT, mContext);
        Whitebox.setInternalState(mController, mOperator);
        mOperator.setMute(false);
        AudioManager audioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        boolean muteSetted = Whitebox.getInternalState(mOperator, MUTE_SETTED);
        Assert.assertFalse(muteSetted);
        assertEquals(AudioManager.RINGER_MODE_NORMAL, audioManager.getRingerMode());
    }

    @Test
    public void should_returnNotification_when_getNotificationForN() throws Exception {
        Whitebox.setInternalState(mRecorderService, CONTEXT, mContext);
        Notification result = Whitebox.invokeMethod(DiskStorageChecker.INSTANCE, GET_NOTIFICATION_FOR_N, TEST, TEST);
        //Assert.assertEquals(R.drawable.ic_launcher_recorder, result.getSmallIcon().getResId());
    }

    @Ignore
    @Test
    public void should_returnFalse_when_resume() {
        try {
            Whitebox.setInternalState(mRecorderService, CONTEXT, mContext);
            Whitebox.invokeMethod(RecordStatusManager.INSTANCE, "setCurrentStatus", RecordStatusManager.PAUSED);
            Whitebox.setInternalState(mRecorderService, mRecordProcessController);
            Whitebox.setInternalState(mRecordProcessController, mController);
            AddonAdapterCompatUtil oplusEnvironment = mock(AddonAdapterCompatUtil.class);
            Whitebox.setInternalState(oplusEnvironment, "isInternalSdMounted", mContext);
            Long lastTime = Whitebox.invokeMethod(DiskStorageChecker.INSTANCE, "checkLastMaxTime");
            RecordResult result = mController.resume();
            assertTrue(lastTime > 0);
            assertNotNull(result);
            assertTrue(result.isSuc());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Ignore
    public void should_returnNotnull_when_dealException() throws Exception {
        ServiceController<RecorderService> controller = Robolectric.buildService(RecorderService.class).create();
        RecorderService recorderService = controller.get();
        Whitebox.setInternalState(recorderService, CONTEXT, mContext);
        recorderService.onCreate();
        Whitebox.setInternalState(mRecorderService, mRecordProcessController);
        Whitebox.setInternalState(mRecordProcessController, mController);
        Whitebox.invokeMethod(mController, "dealException", true);
        long mAppendOldTime = Whitebox.getInternalState(mController, "mAppendOldTime");
        assertNotNull(mAppendOldTime);
    }

    @Test
    public void should_createService_when_onCreate() {
        ServiceController<RecorderService> controller = Robolectric.buildService(RecorderService.class).create();
        RecorderService recorderService = controller.get();
        Assert.assertNotNull(recorderService);
    }

    @Test
    @Ignore
    public void should_unregisterReceivers_when_onDestroy() {
        ServiceController<RecorderService> controller = Robolectric.buildService(RecorderService.class);
        RecorderService service = controller.get();
        MarkHelper markHelper = mock(MarkHelper.class);
        Whitebox.setInternalState(service, "mMarkHelper", markHelper);
        Whitebox.setInternalState(service, "mHasSystemRecorderConflictToast", false);
        service.onCreate();
        service.onDestroy();
        List<String> list = new ArrayList<>();
        List<ShadowApplication.Wrapper> receivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        for (ShadowApplication.Wrapper receiver : receivers) {
            IntentFilter intentFilter = receiver.getIntentFilter();
            for (int i = 0; i < intentFilter.countActions(); i++) {
                list.add(intentFilter.getAction(i));
            }
        }
        Assert.assertFalse(list.contains(Intent.ACTION_MEDIA_EJECT));
        Assert.assertFalse(list.contains(Intent.ACTION_SHUTDOWN));
        assertFalse(list.contains(RecorderDataConstant.ACTION_RECORDER_STOP_RECORDER));
        assertNull(Whitebox.getInternalState(service, "mMarkHelper"));
    }

    @Test
    public void should_showToast_when_onFatalError() {
        Whitebox.setInternalState(mRecorderService, CONTEXT, mContext);
        Whitebox.setInternalState(mRecorderService, mRecordProcessController);
        Whitebox.setInternalState(mRecorderService, mController);
       /* mController.onFatalError(R.string.disk_full);
        ShadowLooper.runUiThreadTasksIncludingDelayedTasks();
        Toast latestToast = ShadowToast.getLatestToast();
        Assert.assertNotNull(latestToast);*/
      /*  CharSequence text = "手机空间不足，无法录音，请清理后重试";
        assertEquals(text, ShadowToast.getTextOfLatestToast());*/
    }

    @Ignore
    @Test
    @Config(shadows = {ShadowOplusUsbEnvironment.class, ShadowOppoRecorder.class})
    public void should_showToast_when_start_with_diskFull() {
        ShadowStatFs.registerStats(SettingsAdapter.getInstance().getStoragePhone(),
                100 * A_MB, 20 * A_MB, 100);
        ServiceController<RecorderService> controller = Robolectric.buildService(RecorderService.class);
        RecorderService service = controller.create().get();
        service.start();
        Toast toast = ShadowToast.getLatestToast();
        String textOfLatestToast = ShadowToast.getTextOfLatestToast();
        Assert.assertNotNull(toast);
        assertEquals("SD卡存储空间已满，自动切换到手机存储", textOfLatestToast);
    }

    @Test
    public void should_notnull_when_getRecordProcessController() {
        Whitebox.setInternalState(mRecorderService, "mRecordProcessController", mRecordProcessController);
        assertNotNull(mRecorderService.getRecordProcessController());
    }

    @Test
    public void should_notnull_when_getRecordConfig() {
        Whitebox.setInternalState(mRecorderService, "mRecordConfig", mRecordConfig);
        assertNotNull(mRecorderService.getRecordConfig());
    }

    @Test
    public void should_notnull_when_getOtherConfig() {
        Whitebox.setInternalState(mRecorderService, "mOtherConfig", mOtherConfig);
        assertNotNull(mRecorderService.getOtherConfig());
    }

    @Test
    public void should_success_when_setNeedStartAfterBind() {
        mRecorderService.setNeedStartAfterBind(true);
        assertTrue(mRecorderService.getNeedStartAfterBind());
    }

    @Test
    public void should_equals_when_getPlayerState() {
        RecordStatusManager.INSTANCE.setCurrentStatus(RecordStatusManager.RECORDING);
        assertFalse(mRecorderService.hasPaused());

        RecordStatusManager.INSTANCE.setCurrentStatus(RecordStatusManager.PAUSED);
        assertTrue(mRecorderService.hasPaused());
    }

    @Test
    public void should_success_when_getDuration() {
        Whitebox.setInternalState(mRecorderService, "mDuration", 1000);
        assertEquals(1000, mRecorderService.getDuration());
    }

    @Test
    public void should_success_when_getCurrentPlayerTime() {
        assertEquals(0, mRecorderService.getCurrentPlayerTime());
    }

    @Test
    @Ignore
    public void should_success_when_getPlayerName() {
        RecorderService recorderService = Mockito.spy(mRecorderService);
        Mockito.when(recorderService.hasPaused()).thenReturn(false, true);
        assertEquals("正在录音", mRecorderService.getPlayerName());
        assertEquals("录音已暂停", mRecorderService.getPlayerName());
    }

    @Test
    @Ignore
    public void should_success_when_addMark() {
        RecorderService recorderService = Mockito.spy(mRecorderService);
        MarkMetaData markMetaData = new MarkMetaData();
        int result = mRecorderService.addMark(false, markMetaData);
        assertEquals(-1, result);

        mRecorderService.onCreate();
        RecordStatusManager.INSTANCE.setCurrentStatus(RecordStatusManager.RECORDING);
        Mockito.when(recorderService.getCurrentPlayerTime()).thenReturn(1000L);
        result = mRecorderService.addMark(false, markMetaData);
        assertEquals(0, result);
    }

    @Test
    public void should_success_when_setMarkEnable() {
        mRecorderService.setMarkEnable(true);
        assertEquals(mRecorderService.getMarkEnable().getValue(), true);
        mRecorderService.setMarkEnable(false);
        assertEquals(mRecorderService.getMarkEnable().getValue(),false);
    }

    @Test
    public void should_success_when_addSourceForNotificationBtnDisabled() throws Exception {
        Whitebox.invokeMethod(mRecorderService, "setBtnDisabled", false);
        assertEquals(mRecorderService.isBtnDisabled.getValue(), false);
        Whitebox.invokeMethod(mRecorderService, "setBtnDisabled", true);
        assertEquals(mRecorderService.isBtnDisabled.getValue(), true);
    }

    @Test
    public void should_success_when_onAudioModeChanged() {
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING);
        mRecordProcessController.onAudioModeChanged(2);
        mRecordProcessController.onAudioModeChanged(0);

        RecordStatusManager.setCurrentStatus(RecordStatusManager.PAUSED);
        mRecordProcessController.onAudioModeChanged(3);
        mRecordProcessController.onAudioModeChanged(0);
    }

    @Test
    public void should_when_initOtherConfig() throws Exception {
        Intent intent = new Intent();
        RecordExpandController.OtherConfig config =  Whitebox.invokeMethod(mRecorderService, "initOtherConfig", intent);
        assertEquals(RecordExpandController.OtherConfig.START_FROM_NORMAL, config.getRecordFromType());

        intent.putExtra(PAGE_FROM_NAME, PAGE_FROM_SLIDE_BAR);
        config =  Whitebox.invokeMethod(mRecorderService, "initOtherConfig", intent);
        assertEquals(RecordExpandController.OtherConfig.START_FROM_SLIDBAR, config.getRecordFromType());

        intent.putExtra(PAGE_FROM_NAME, PAGE_FROM_BREENO);
        config =  Whitebox.invokeMethod(mRecorderService, "initOtherConfig", intent);
        assertEquals(RecordExpandController.OtherConfig.START_FROM_BREENO, config.getRecordFromType());

        intent.putExtra(PAGE_FROM_NAME, PAGE_FROM_BREENO_FRONT);
        config =  Whitebox.invokeMethod(mRecorderService, "initOtherConfig", intent);
        assertEquals(RecordExpandController.OtherConfig.START_FROM_BREENO_FRONT, config.getRecordFromType());

        intent.putExtra(PAGE_FROM_NAME, PAGE_FROM_DRAGON_FLY);
        config =  Whitebox.invokeMethod(mRecorderService, "initOtherConfig", intent);
        assertEquals(RecordExpandController.OtherConfig.START_FROM_APP_CARD, config.getRecordFromType());

        intent.putExtra(PAGE_FROM_NAME, PAGE_FROM_SMALL_CARD);
        config =  Whitebox.invokeMethod(mRecorderService, "initOtherConfig", intent);
        assertEquals(RecordExpandController.OtherConfig.START_FROM_SMALL_CARD, config.getRecordFromType());
    }
}

