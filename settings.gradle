include ':app'

include ':common:RecorderLogX'
include ':common:modulerouter'
include ':common:RecorderLogBase'
include ':common:libcommon'
include ':common:libimageload'
include ':common:libbase'
include ':common:markwon-core'
include ':common:markwon-ext-tasklist'


include ':component:move'
include ':component:movepure'
include ':component:Questionnaire'
include ':component:PhotoViewer'
include ':component:player'
include ':component:notification'
include ':component:smallcardlibrary'
include ':component:wavemark'
include ':component:privacypolicy'
include ':component:dragonFly'
include ':component:cloudkit'
include ':component:ConvertService'
include ':component:sellMode'
include ':component:ConvertService'
include ':component:recorderService'
include ':component:summary'
include ':component:share'

include ':page:setting'
include ':page:browsefile'
include ':page:playback'
include ':page:editRecord'
include ':page:record'
include ':page:card'
include ':page:miniapp'
include ':component:translate'