package com.soundrecorder.common.databean

import android.content.ContentValues
import android.database.Cursor
import com.soundrecorder.common.constant.DatabaseConstant

data class UploadRecord(
    var mId: Int = 0,
    var mOnlyId: String = "",
    var mFileStartRange: Long = 0,
    var mFileEndRange: Long = 0,
    var mETag: String = "",
    var mUrl: String = "",
    var mSeqNumber: Int = 1
) {
    private val TAG = "UploadRecord"

    constructor(cursor: Cursor) : this() {
        var index = cursor.getColumnIndex(DatabaseConstant.UploadColumn._ID)
        if (index >= 0) {
            mId = cursor.getInt(index)
        }
        index = cursor.getColumnIndex(DatabaseConstant.UploadColumn.ONLY_ID)
        if (index >= 0) {
            mOnlyId = cursor.getString(index)
        }
        index = cursor.getColumnIndex(DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START)
        if (index >= 0) {
            mFileStartRange = cursor.getLong(index)
        }
        index = cursor.getColumnIndex(DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_END)
        if (index >= 0) {
            mFileEndRange = cursor.getLong(index)
        }
        index = cursor.getColumnIndex(DatabaseConstant.UploadColumn.UPLOAD_ETAG)
        if (index >= 0) {
            mETag = cursor.getString(index)
        }
        index = cursor.getColumnIndex(DatabaseConstant.UploadColumn.UPLOAD_URL)
        if (index >= 0) {
            mUrl = cursor.getString(index)
        }
        index = cursor.getColumnIndex(DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM)
        if (index >= 0) {
            mSeqNumber = cursor.getInt(index)
        }
    }


    constructor(uploadRecord: UploadRecord) : this() {
        mId = uploadRecord.mId
        mOnlyId = uploadRecord.mOnlyId
        mFileStartRange = uploadRecord.mFileStartRange
        mFileEndRange = uploadRecord.mFileEndRange
        mETag = uploadRecord.mETag
        mUrl = uploadRecord.mUrl
        mSeqNumber = uploadRecord.mSeqNumber
    }


    override fun toString(): String {
        return "uploadRecord: " + "\n" +
                "mId:" + mId + "\n" +
                "mOnlyId:" + mOnlyId + "\n" +
                "mFileStartRange:" + mFileStartRange + "\n" +
                "mFileEndRange:" + mFileEndRange + "\n" +
                "mETag:" + mETag + "\n" +
                "mUrl:" + mUrl + "\n" +
                "mSeqNumber:" + mSeqNumber
    }


    fun getContentValues(): ContentValues {
        val values = ContentValues()
        values.put(DatabaseConstant.UploadColumn._ID, this.mId)
        values.put(DatabaseConstant.UploadColumn.ONLY_ID, this.mOnlyId)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START, this.mFileStartRange)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_END, this.mFileEndRange)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_ETAG, this.mETag)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_URL, this.mUrl)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM, this.mSeqNumber)
        return values
    }


    fun getContentValuesWithoutId(): ContentValues {
        val values = ContentValues()
        values.put(DatabaseConstant.UploadColumn.ONLY_ID, this.mOnlyId)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START, this.mFileStartRange)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_END, this.mFileEndRange)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_ETAG, this.mETag)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_URL, this.mUrl)
        values.put(DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM, this.mSeqNumber)
        return values
    }


    fun getProjections(): Array<String?>? {
        return arrayOf(
                DatabaseConstant.UploadColumn._ID,
                DatabaseConstant.UploadColumn.ONLY_ID,
                DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START,
                DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_END,
                DatabaseConstant.UploadColumn.UPLOAD_ETAG,
                DatabaseConstant.UploadColumn.UPLOAD_URL,
                DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM)
    }
}