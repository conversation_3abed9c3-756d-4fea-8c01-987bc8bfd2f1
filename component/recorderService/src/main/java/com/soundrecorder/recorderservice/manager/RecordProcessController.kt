/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordProcessController
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.Intent
import android.media.AudioManager
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.SmartNameStatisticsUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.common.databean.SmartParamsContent
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.RecordModeUtil.setRecordMode
import com.soundrecorder.modulerouter.SeedlingAction.sendRecordAddEvent
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.asr.IRealTimeAsrController
import com.soundrecorder.recorderservice.asr.IRealTimeAsrResultCallback
import com.soundrecorder.recorderservice.asr.RealTimeRecordController
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver
import com.soundrecorder.recorderservice.controller.observer.WaveObserver
import com.soundrecorder.recorderservice.manager.RecordStatusManager.HALT_ON
import com.soundrecorder.recorderservice.manager.RecordStatusManager.INIT
import com.soundrecorder.recorderservice.manager.RecordStatusManager.PAUSED
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING
import com.soundrecorder.recorderservice.manager.RecordStatusManager.getCurrentStatus
import com.soundrecorder.recorderservice.manager.RecordStatusManager.isAlreadyRecording
import com.soundrecorder.recorderservice.manager.listener.AudioModeChangeListener
import com.soundrecorder.recorderservice.manager.listener.AudioRecordingChangeListener
import com.soundrecorder.recorderservice.manager.listener.RecordResultCallback
import java.util.UUID

class RecordProcessController(
    var service: RecorderService?,
    var recordConfig: RecordExpandController.RecordConfig,
    var otherConfig: RecordExpandController.OtherConfig,
    var muteConfig: MuteModeOperator.MuteConfig = MuteModeOperator.MuteConfig()
) : RecordStatusManager.OnRecordStatusChangeListener, RecordStopExceptionProcessor.IStopEventListener,
    AudioModeChangeListener {

    companion object {
        const val TAG = "RecordProcessController"
        const val MSG_CHECK_DISK = 11
        const val REC_PROCESS_CHECKDISK_DURATION: Long = 5000

        const val MSG_RECORD_ACTION_START = 21
        const val MSG_RECORD_ACTION_PAUSE = 22
        const val MSG_RECORD_ACTION_RESUME = 23
        const val MSG_RECORD_ACTION_STOP = 24
        const val MSG_RECORD_ACTION_SAVERECORD = 25
        const val MSG_RECORD_ACTION_CANCEL = 26

        const val MSG_RECORD_ACTION_RELEASE = 31

        const val MSG_RECORD_ACTION_AUDIO_CHANGE_PAUSE = 32
        const val MSG_RECORD_ACTION_AUDIO_CHANGE_RESUME = 33

        const val MSG_SAVERECORD_BUNDLE_NAME_KEY = "displayName"
        const val MSG_SAVERECORD_BUNDLE_MARKLIST_KEY = "markList"

        const val MSG_ARG1_NEED_STOP_SERVICE = 1
        const val MSG_ARG1_NO_NEED_STOP_SERVICE = 2
    }


    private var mNotificationProcessor: RecordNotificationProcessor = RecordNotificationProcessor()
    private var mHanderThread: HandlerThread? = null
    private var mWorkHandler: Handler? = null

    var mRecorderUIController: RecorderUIController? = null
    var mRecorderExpandController: RecordExpandController? = null
    var mRecordResultCallback: RecordResultCallback? = null
    var asrController: IRealTimeAsrController? = null

    // 记录开始录制时智能命名开关是否打开，用于判断是否保存实时ASR的转写结果
    private var mIsSmartNameOpen: Boolean = false

    fun initArgs() {
        DebugUtil.i(TAG, "initArgs")
        mRecorderExpandController = RecordExpandController(recordConfig, otherConfig, muteConfig).apply {
            audioFocusListener = object : MuteModeOperator.AudioFocuseChangeListener {
                override fun onFocuseChanged(audioFoucesChanged: Int) {
                    when (audioFoucesChanged) {
                        AudioManager.AUDIOFOCUS_LOSS, AudioManager.AUDIOFOCUS_LOSS_TRANSIENT, AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                            DebugUtil.d(TAG, "AudioFocus: received AUDIOFOCUS_LOSS")
                            asrController?.pauseRecord()
                        }
                        AudioManager.AUDIOFOCUS_GAIN -> DebugUtil.d(TAG, "AudioFocus: received AUDIOFOCUS_GAIN ")

                        else -> DebugUtil.d(TAG, "Unknown audio focus change code")
                    }
                }
            }
        }
        mRecorderUIController = RecorderUIController(mRecorderExpandController)
        initPcmRecorderController()
        RecordStatusManager.registerChangeListener(this)
        RecordStopExceptionProcessor.registerStopEventListener(this)
        AudioModeChangeManager.audioManagerModeChange()
        AudioModeChangeManager.registerChangeListener(this)
        /*支持定向录音，增加音源变化监听*/
        if (RecorderViewModel.getInstance().isSupportDirectRecording()) {
             AudioModeChangeManager.initAudioSourceType()
             AudioModeChangeManager.audioSourceChangeListener = object : AudioRecordingChangeListener {
                 override fun onAudioRecordingSourceChange(isMic: Boolean) {
                     // 设置定向功能enable
                     service?.setDirectionalRecordEnable(isMic)
                     if (!isMic && service?.directionalRecordOn?.value == true) {
                         // 非手机麦克风且定向开启，则关闭定向开关
                         service?.setDirectionalRecordOn(false)
                         RecorderViewModel.getInstance().onDirectRecordingOffByMicChanged()
                     }
                 }
             }
        }
    }

    fun updateRecordConfig(newRecordConfig: RecordExpandController.RecordConfig) {
        recordConfig = newRecordConfig
        mRecorderExpandController?.updateRecordConfig(newRecordConfig)
    }

    fun updateOtherConfig(newOtherConfig: RecordExpandController.OtherConfig) {
        otherConfig = newOtherConfig
        mRecorderExpandController?.updateOtherConfig(newOtherConfig)
    }

    fun initHandlerThread() {
        DebugUtil.i(TAG, "initHandlerThread")
        mHanderThread = HandlerThread("ServiceTime")
        mHanderThread?.start()
        mHanderThread?.looper?.let {
            mWorkHandler = object : Handler(it) {
                // 消息处理的操作
                override fun handleMessage(msg: Message) {
                    //设置了两种消息处理操作,通过msg来进行识别
                    when (msg.what) {
                        //检查磁盘的MSG
                        MSG_CHECK_DISK -> {
                            if (getCurrentStatus() == RECORDING) {
                                DiskStorageChecker.checkDiskWhenRunning()
                                val msg = Message.obtain(mWorkHandler, MSG_CHECK_DISK)
                                mWorkHandler?.sendMessageDelayed(
                                    msg,
                                    REC_PROCESS_CHECKDISK_DURATION
                                )
                            } else {
                                mWorkHandler?.removeMessages(MSG_CHECK_DISK)
                            }
                        }
                        //用户主动调用录音的4个操作，开始录音
                        MSG_RECORD_ACTION_START -> {
                            DebugUtil.i(TAG, "handle record action start")
                            val result = mRecorderExpandController?.start()
                            if (result != null) {
                                mRecordResultCallback?.onRecordResultArrived(result)
                            }
                            DebugUtil.i(TAG, "handle record action start result $result")
                        }
                        //用户主动调用录音的4个操作，暂停录音
                        MSG_RECORD_ACTION_PAUSE -> {
                            DebugUtil.i(TAG, "handle record action pause")
                            val result = mRecorderExpandController?.pause()
                            if (result != null) {
                                mRecordResultCallback?.onRecordResultArrived(result)
                            }
                            DebugUtil.i(TAG, "handle record action pause result $result")
                        }
                        //用户主动调用录音的4个操作，继续录音
                        MSG_RECORD_ACTION_RESUME -> {
                            DebugUtil.i(TAG, "handle record action resume")
                            val result = mRecorderExpandController?.resume()
                            if (result != null) {
                                mRecordResultCallback?.onRecordResultArrived(result)
                            }
                            DebugUtil.i(TAG, "handle record action resume result $result")
                        }
                        //用户主动调用录音的4个操作，停止录音
                        MSG_RECORD_ACTION_STOP -> {
                            DebugUtil.i(TAG, "handle record action stop")
                            val result = mRecorderExpandController?.stop()
                            asrController?.doStopRecord()
                            if (result != null) {
                                mRecordResultCallback?.onRecordResultArrived(result)
                            }
                            if (msg.arg1 == MSG_ARG1_NEED_STOP_SERVICE) {
                                service?.stopService()
                                DebugUtil.i(TAG, "handle record stop stop RecorderServiceSelf")
                                sendStopRecordBroadCastFromSave(Int.MAX_VALUE)
                            }
                            DebugUtil.i(TAG, "handle record action stop result $result")
                        }
                        MSG_RECORD_ACTION_SAVERECORD -> {
                            DebugUtil.i(TAG, "handle recordSaveMsg")
                            val markList = msg.data.getParcelableArrayList<MarkDataBean>(
                                MSG_SAVERECORD_BUNDLE_MARKLIST_KEY
                            )
                            val disPlayName = msg.data.getString(MSG_SAVERECORD_BUNDLE_NAME_KEY)
                            //自己保存的时候，不需要监听之前的正在录制的音频文件
                            mRecorderExpandController?.setBlockDelete(true)
                            DebugUtil.i(
                                TAG,
                                "handle recordSaveMsg disPlayName $disPlayName, markList $markList, needStopService : ${msg.arg1}"
                            )
                            val trigSyncNow = asrController == null || asrController?.isError() == true
                            val result =
                                    mRecorderUIController?.saveRecordInfo(disPlayName, markList, msg.arg2, trigSyncNow)
                            /*音频保存结束后，执行asr保存，避免大文件保存过程中就执行智能名操作，导致文件变更*/
                            disPlayName?.let { name ->
                                DebugUtil.i(TAG, "saveRecordInfo mIsSmartNameOpen $mIsSmartNameOpen")
                                if (mIsSmartNameOpen) {
                                    asrController?.saveFile(name, mRecorderUIController?.mRecorderController?.controllerObserver?.sampleUri)
                                }
                            }
                            if (result != null) {
                                mRecordResultCallback?.onRecordResultArrived(result)
                            }
                            if (msg.arg1 == MSG_ARG1_NEED_STOP_SERVICE) {
                                service?.stopService()
                                DebugUtil.i(TAG, "handle recordSaveMsg RecorderServiceSelf")
                                //sendStopRecordBroadCastFromSave(msg.arg2)
                            }
                            DebugUtil.i(TAG, "handle record action save")
                            // 发送录音摘要出卡意图
                            sendRecordAddEvent()
                        }
                        MSG_RECORD_ACTION_RELEASE -> {
                            DebugUtil.i(TAG, "handle record action release")
                            //释放mRecorderUIController
                            mRecorderUIController?.stopSample()
                            mRecorderUIController?.unRegisterWaveUIObserver()
                            mRecorderUIController?.setRecorderStateListener(null)

                            //释放mRecorderExpandController
                            mRecorderExpandController?.releaseAll()
                            mRecorderExpandController = null
                            //释放UICallback
                            unregisterUICallback()
                            //handlerThread进行quit
                            releaseHandlerThread()
                            //解除StopExceptionProcessor相关监听
                            RecordStopExceptionProcessor.unRegisterStopEventListener()
                            //RecordStatusManager 录制状态的全局监听
                            RecordStatusManager.unRegisterChangeListener()
                            //把状态置成默认，否则下次进来还是HOLT_ON,录制按钮会默认是暂停状态
                            RecordStatusManager.resetState()
                            AudioModeChangeManager.release()
                            //Fix the type of recording that affects other apps after exiting recording
                            setRecordMode(BaseApplication.getAppContext(), RecordModeUtil.FRAGMENTS_TYPE_NORMAL)
                        }
                        MSG_RECORD_ACTION_CANCEL -> {
                            DebugUtil.i(TAG, "handle record action cancel")
                            mRecorderExpandController?.deleteInvalidRecordDbFile()
                            if (isAlreadyRecording()) {
                                mRecorderExpandController?.stop()
                            }
                            mRecorderExpandController?.onCancelRecord()
                            sendCancelRecordBroadCast()
                        }
                        MSG_RECORD_ACTION_AUDIO_CHANGE_PAUSE -> audioModeChangeNeedPause()
                        MSG_RECORD_ACTION_AUDIO_CHANGE_RESUME -> audioModeChangeNeedResume()
                    }
                }
            }
        }
    }

    fun releaseHandlerThread() {
        mWorkHandler?.removeCallbacksAndMessages(null)
        mHanderThread?.quit()
        mWorkHandler = null
    }


    fun startRecord() {
        mWorkHandler?.sendEmptyMessage(MSG_RECORD_ACTION_START)
        asrController?.startRecord()
        mIsSmartNameOpen = SmartNameAction.isSmartNameSwitchOpen(BaseApplication.getAppContext())
    }

    fun pauseRecord() {
        mWorkHandler?.sendEmptyMessage(MSG_RECORD_ACTION_PAUSE)
        asrController?.pauseRecord()
    }

    fun resumeRecord() {
        mWorkHandler?.sendEmptyMessage(MSG_RECORD_ACTION_RESUME)
        asrController?.resumeRecord()
    }

    fun stopRecord(needStopRecordServer: Boolean = false) {
        mWorkHandler?.obtainMessage(MSG_RECORD_ACTION_STOP)?.run {
            this.arg1 = if (needStopRecordServer) MSG_ARG1_NEED_STOP_SERVICE else MSG_ARG1_NO_NEED_STOP_SERVICE
            this.sendToTarget()
            DebugUtil.i(TAG, "stopRecord send msg")
        }
    }

    fun cancelRecord() {
        mWorkHandler?.sendEmptyMessage(MSG_RECORD_ACTION_CANCEL)
        asrController?.cancelRecord()
    }

    fun release() {
        DebugUtil.i(TAG, "release")
        mWorkHandler?.sendEmptyMessage(MSG_RECORD_ACTION_RELEASE)
        asrController?.releaseAll()
    }

    private fun connectionCall() {
        mWorkHandler?.sendEmptyMessage(MSG_RECORD_ACTION_AUDIO_CHANGE_PAUSE)
    }

    private fun disconnectCall() {
        mWorkHandler?.sendEmptyMessage(MSG_RECORD_ACTION_AUDIO_CHANGE_RESUME)
    }

    fun saveRecordInfo(
        newDisplayName: String = "",
        originalDisplayName: String = "",
        markList: List<MarkDataBean>?,
        needStopRecordServer: Boolean = false,
        saveRecordFromWhere: Int = RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY
    ) {
        val displayName = checkSaveName(newDisplayName)
        DebugUtil.i(TAG, "saveRecordInfo newDisplayName $newDisplayName, displayName $displayName,markList: $markList")
        service?.recorderController?.recordInfoSaveObserver?.updateLoadingCallback(displayName)
        service?.isBtnDisabled?.postValueSafe(true)
        service?.markEnable?.postValueSafe(false)

        val bundle = Bundle()
        bundle.putString(MSG_SAVERECORD_BUNDLE_NAME_KEY, displayName)
        val markArrayList = ArrayList<MarkDataBean>()
        markList?.forEach {
            markArrayList.add(it)
        }
        bundle.putParcelableArrayList(MSG_SAVERECORD_BUNDLE_MARKLIST_KEY, markArrayList)
        val msg = mWorkHandler?.obtainMessage(MSG_RECORD_ACTION_SAVERECORD)?.apply {
            data = bundle
            arg1 = if (needStopRecordServer) MSG_ARG1_NEED_STOP_SERVICE else MSG_ARG1_NO_NEED_STOP_SERVICE
            arg2 = saveRecordFromWhere
        }

        if (msg != null) {
            mWorkHandler?.sendMessage(msg)
            DebugUtil.i(TAG, "saveRecordInfo send msg")
        }
        if (displayName.isNotEmpty() && originalDisplayName.isNotEmpty() && displayName != originalDisplayName) {
            BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD_DIALOG_SAVE_CHANGE_NAME)
        }
    }

    private fun checkSaveName(newDisplayName: String?): String {
        var resultName = newDisplayName ?: ""
        if (TextUtils.isEmpty(newDisplayName)) { // 优先使用sampleName
            resultName = RecorderViewModel.getInstance().getSampleDisplayName(false)
        }
        if (TextUtils.isEmpty(resultName)) { // sampleName为null，则生成newName
            resultName = service?.defaultDisplayName ?: ""
            DebugUtil.w(TAG, "checkSaveName, generate default display name: $resultName")
        }
        return resultName
    }

    override fun onStopEvent(intputEvent: Long) {
        DebugUtil.w(TAG, "onStopEvent $intputEvent")
        when (intputEvent) {
            RecordStopExceptionProcessor.STOP_EVENT_TYPE_CAMERA,
            RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_OVERLIMIT,
            RecordStopExceptionProcessor.STOP_EVENT_TYPE_STORAGE_NOT_ENOUGH -> {
                processNormalStop(intputEvent)
            }
            RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_ERROR_INFO,
            RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_EXCEPTION -> {
                processAbnormalStop()
            }
            RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDINGFILE_DELETE -> {
                processFileDeleteStop()
            }
            RecordStopExceptionProcessor.STOP_EVENT_TYPE_SHUT_DOWN -> {
                processShutDownStop()
            }
        }
    }

    private fun processNormalStop(intputEvent: Long) {
        DebugUtil.i(TAG, "processNormalStop")
        stopRecord()
        saveRecordInfo(newDisplayName = "", originalDisplayName = "", service?.markDatas, true, RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_NORMAL)
        if (intputEvent == RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_OVERLIMIT) {
            val path =
                mRecorderExpandController?.mRecordFileWraper?.relativePath + mRecorderExpandController?.mRecordFileWraper?.sampleFileName
            val str = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.save_as_max, path)
            DebugUtil.w(TAG, "processNormalStop file overlimit save path $str")
            ToastManager.showShortToast(BaseApplication.getAppContext(), str)
        } else if (intputEvent == RecordStopExceptionProcessor.STOP_EVENT_TYPE_CAMERA) {
            //相机开始录制视频后，收到广播后，录音已结束录制，此场景和产品沟通后需要加一个toast
            ToastManager.showShortToast(BaseApplication.getAppContext(), com.soundrecorder.common.R.string.record_pause_tips)
        }
    }

    private fun processAbnormalStop() {
        DebugUtil.i(TAG, "processAbnormalStop")
        //发送广播之前就已经处理了recorder的相关逻辑，这里只需要服务自杀即可
        service?.stopService()
    }

    private fun processFileDeleteStop() {
        DebugUtil.i(TAG, "processFileDeleteStop")
        //删除相关的中间db数据
        mRecorderExpandController?.deleteInvalidRecordDbFile()
        stopRecord(true)
    }

    private fun processShutDownStop() {
        DebugUtil.i(TAG, "processShutDownStop")
        stopRecord(true)
    }

    fun initRecordUIControllerForSlidBar() {
        mRecorderUIController?.onlyInitRecordderControllerAndObserver()
    }

    fun registerUICallback(recorderStateListener: RecorderUIController.RecorderStateListener?) {
        mRecorderUIController?.setRecorderStateListener(recorderStateListener)
    }

    fun unregisterUICallback() {
        mRecorderUIController?.setRecorderStateListener(null)
    }

    fun registerRecorderControlObservers(
        waveObserver: WaveObserver,
        recordInfoSaveObserver: RecordInfoSaveObserver
    ) {
        mRecorderUIController?.registerRecorderControlObservers(
            waveObserver, recordInfoSaveObserver)
    }

    fun updateRecorderControlObservers(
        waveObserver: WaveObserver?,
        recordInfoSaveObserver: RecordInfoSaveObserver?
    ) {
        DebugUtil.i(TAG, "updateRecorderControlObservers")
        mRecorderUIController?.updateRecorderControlObservers(
            waveObserver, recordInfoSaveObserver)
    }

    fun unRegisterWaveUIObserver() {
        DebugUtil.i(TAG, "unRegisterWaveUIObserver")
        mRecorderUIController?.unRegisterWaveUIObserver()
    }

    fun updateNotification() {
        mNotificationProcessor.updateNotification(service, otherConfig.isFromOtherApp)
    }

    fun cancelRecordNotification() {
        mNotificationProcessor.cancelNotificationWhenStop(otherConfig.isFromOtherApp)
    }

    private fun startDiskCheck() {
        mWorkHandler?.removeMessages(MSG_CHECK_DISK)
        mWorkHandler?.sendEmptyMessage(MSG_CHECK_DISK)
    }

    private fun stopDiskCheck() {
        mWorkHandler?.removeMessages(MSG_CHECK_DISK)
    }

    override fun onRecordStatusChange(currentStatus: Int, lastStatus: Int) {
        DebugUtil.i(
            TAG,
            "onRecorsStatusChange currentStatus $currentStatus , lastStatus $lastStatus"
        )
        mRecorderUIController?.mRecorderStateListener?.onRecordStatusChange(currentStatus)
        when (currentStatus) {
            INIT -> DebugUtil.d(TAG, "INIT do nothing")
            RECORDING -> processRecording(currentStatus, lastStatus)
            PAUSED -> processPause(currentStatus)
            HALT_ON -> processStop(currentStatus)
        }
    }

    private fun processRecording(currentStatus: Int, lastStatus: Int) {
        DebugUtil.i(
            TAG,
            "processRecording currentStatus $currentStatus, lastStatus: $lastStatus"
        )
        mRecorderUIController?.startSample()
        startDiskCheck()
    }

    private fun processPause(currentStatus: Int) {
        DebugUtil.i(TAG, "processPause currentStatus $currentStatus")
        mRecorderUIController?.stopSample()
        stopDiskCheck()
    }

    private fun processStop(currentStatus: Int) {
        DebugUtil.i(TAG, "processStop currentStatus $currentStatus")
        mRecorderUIController?.stopSample()
        cancelRecordNotification()
    }

    private fun sendStopRecordBroadCastFromSave(saveRecordFromWhere: Int) {
        val intent = Intent(RecorderDataConstant.ACTION_RECORDER_STOP_RECORDER)
        intent.putExtra(RecorderDataConstant.KEY_SAVE_FROM_WHERE, saveRecordFromWhere)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun sendCancelRecordBroadCast() {
        val intent = Intent(RecorderDataConstant.ACTION_RECORDER_STOP_CANCEL)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    /**
     * Audio mode change
     * 发消息子线程处理
     */
    override fun onAudioModeChanged(audioMode: Int) {
        DebugUtil.d(TAG, "dealModeChange current mode is $audioMode")
        val isConnectionCall =
            ((audioMode == AudioManager.MODE_IN_CALL) || (audioMode == AudioManager.MODE_IN_COMMUNICATION))
        val isRecording = isAlreadyRecording()
        if ((isConnectionCall) && (isRecording)) {
            connectionCall()
        } else if ((audioMode == AudioManager.MODE_NORMAL) && (getCurrentStatus() == PAUSED)) {
            disconnectCall()
        } else {
            DebugUtil.d(TAG, "current mode is not call")
        }
    }

    /**
     * 通话接通,暂停录制,录制界面及通知栏置灰不可操作
     * 1.录制过程中接通——暂停录音
     * 2.录制暂停中接通——保留暂停状态
     */
    private fun audioModeChangeNeedPause() {
        DebugUtil.d(TAG, "current mode is call should pause toast")
        AudioModeChangeManager.changeAudioPause(true)
        if (getCurrentStatus() == RECORDING) {
            //原来是录制状态,需要暂停录制,设置通话结束后自动继续录制,toast提示用户,更新通知栏
            val result = mRecorderExpandController?.pause()
            if (result?.isSuc() == true) {
                AudioModeChangeManager.changeNeedResume(true)
                val context = BaseApplication.getAppContext()
                val str = com.soundrecorder.common.R.string.state_call_record_paused
                ToastManager.showShortToast(context, str)
            }
            asrController?.pauseRecord()
        } else {
            //原本就是paused状态只需要回调PAUSED UI,设置通话结束不需要自动开始录制,更新通知置灰
            AudioModeChangeManager.changeNeedResume(false)
        }
        updatePauseNotification()
        mRecorderUIController?.mRecorderStateListener?.onRecordCallConnected()
    }

    /**
     * 通话挂断,继续录制,录制界面及通知栏恢复操作
     * 1.通话接通时是RECORDING——挂断继续录制
     * 2.通话接通时是PAUSED——挂断保持暂停状态
     * 3.通过isNeedResume判断是否需要继续录音
     */
    private fun audioModeChangeNeedResume() {
        DebugUtil.d(TAG, "current mode is call should resuem toast")
        AudioModeChangeManager.changeAudioPause(false)
        if (AudioModeChangeManager.isNeedResume()) {
            //是否需要继续录制,使用完置回默认
            AudioModeChangeManager.changeNeedResume(false)
            //继续录制
            asrController?.resumeRecord()
            val result = mRecorderExpandController?.resume()
            if (result?.isSuc() == true) {
                //提示用户自动继续录制
                val context = BaseApplication.getAppContext()
                val str = com.soundrecorder.common.R.string.call_record_resuem
                ToastManager.showShortToast(context, str)
            }
        }
        //更新通知栏状态
        updateResumeNotification()
        mRecorderUIController?.mRecorderStateListener?.onRecordCallConnected()
    }

    /**
     * 通话接通时暂停录制,更新通知栏状态
     * 1.保存当前通知栏状态
     * 2.将当前通知栏置灰处理
     */
    private fun updatePauseNotification() {
        AudioModeChangeManager.changeLastBtnDisabled(service?.isBtnDisabled?.value ?: false)
        service?.isBtnDisabled?.postValueSafe<Boolean>(true)
    }

    /**
     * 通话挂断时更新通知栏
     * 1.取出暂停时保存的通知栏状态并设值
     */
    private fun updateResumeNotification() {
        service?.isBtnDisabled?.postValueSafe<Boolean>(AudioModeChangeManager.getLastBtnDisabled())
    }

    /**
     * 若开启了智能标题+下载相关插件
     * 另开启一路recorder，获取pcm流进行实时ASR，实时asr结束后进行智能标题
     */
    private fun initPcmRecorderController(skipSwitchCheck: Boolean = false) {
        if (RecorderViewModel.getInstance().isFromOtherAppOrigin ||
            (!skipSwitchCheck && !SmartNameAction.isSmartNameSwitchOpen(BaseApplication.getAppContext()))
        ) {
            DebugUtil.i(TAG, "initPcmRecorderController return by other process or switch closed")
            return
        }

        if (asrController == null) {
            asrController = RealTimeRecordController()
            asrController?.initHandlerThread()
            asrController?.setAsrResultCallback(object : IRealTimeAsrResultCallback {
                override fun onResult(
                    convert: ConvertRecord,
                    data: List<ConvertContentItem>
                ) {
                    DebugUtil.i(TAG, "asr onResult, data.size = ${data.size}")
                    // 拿到文本结果，去做智能标题
                    doStartSmartName(convert, data)
                }

                override fun onError(originErrorCode: Int, formatErrorCode: Int, data: List<ConvertContentItem>?) { // 出错了，需要toast提示用户
                    when (formatErrorCode) {
                        IRealTimeAsrController.ERROR_CODE_SERVER_ERROR -> {
                            ToastManager.showShortToast(
                                BaseApplication.getAppContext(), com.soundrecorder.wavemark.R.string.smart_title_failure_server_error)
                        }

                        IRealTimeAsrController.ERROR_CODE_NET_ERROR -> {
                            ToastManager.showShortToast(
                                BaseApplication.getAppContext(), com.soundrecorder.wavemark.R.string.no_network_intelligent_name_failure)
                        }

                        else -> DebugUtil.w(TAG, "asr result error code not support $formatErrorCode, origin code = $originErrorCode")
                    }
                }
            })
        }
    }

    private fun doStartSmartName(convert: ConvertRecord, data: List<ConvertContentItem>) {
        if (!PermissionUtils.hasAllFilePermission()) {
            sendSmartNameFilePermissionBroadCast(convert.recordId)
            return
        }
        if (data.isNullOrEmpty()) {
            ToastManager.showShortToast(BaseApplication.getAppContext(),
                com.soundrecorder.wavemark.R.string.less_recorded_content_intelligent_name_failure)
            SmartNameStatisticsUtil.addSmartNameResultEvent(
                SmartNameStatisticsUtil.VALUE_SMART_NAME_RESULT_FAILURE,
                SmartNameStatisticsUtil.VALUE_SMART_NAME_ERROR_CODE_DEFAULT
            )
            return
        }

        if (!RecorderViewModel.getInstance().isFromOtherAppOrigin
            && SmartNameAction.isSmartNameSwitchOpen(BaseApplication.getAppContext())) {
            val language = LanguageUtil.getCurrentLanguageFromSystem()
            val contents: ArrayList<SmartParamsContent> = arrayListOf()
            data.forEachIndexed { index, convertContentItem ->
                val contentId = index + 1
                contents.add(
                    SmartParamsContent(
                        id = contentId,
                        content = convertContentItem.textContent,
                        timemap = System.currentTimeMillis(),
                        language = language
                    )
                )
            }
            val params = SmartNameParam.createSmartNameParam(
                sessionId = UUID.randomUUID().toString(),
                dialogs = contents,
                inputLanguage = language,
                outputLanguage = language
            )
            DebugUtil.d(TAG, "doStartSmartName, jsonParams:$params")
            if (SmartNameAction.checkTaskRunningMaxLimitSize()) {
                ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.smart_name_task_too_many)
            }
            SmartNameAction.startSmartNameByBean(mediaId = convert.recordId, params)
        }
    }

    private fun sendSmartNameFilePermissionBroadCast(mediaId: Long) {
        val intent = Intent(RecorderDataConstant.SMART_NAME_FILE_PERMISSION_ACTION)
        intent.putExtra(RecorderDataConstant.RECORD_MEDIA_ID, mediaId)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    /**
     * 切换实时ASR语言
     */
    fun changeAsrLanguage(language: String) {
        asrController?.changeAsrLanguage(language)
    }

    /**
     * 注册实时ASR监听
     */
    fun registerRtAsrListener(listener: OnRealtimeListener) {
        initPcmRecorderController(true)
        asrController?.registerRtAsrListener(listener)
    }

    /**
     * 反注册实时ASR监听
     */
    fun unregisterRtAsrListener(listener: OnRealtimeListener) {
        asrController?.unregisterRtAsrListener(listener)
    }

    /**
     * 获取实时ASR所支持的语言
     */
    fun startTranslationConfig() {
        asrController?.startTranslationConfig()
    }

    /**
     * 主动获取所有ASR识别的缓存内容
     */
    fun getAllAsrContent(): List<ConvertContentItem> {
        return asrController?.getAllAsrContent() ?: mutableListOf()
    }

    /**
     * 获取实时ASR当前的状态
     */
    fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        return asrController?.getRealtimeAsrStatus() ?: RealTimeAsrStatus.ASR_DEFAULT
    }

    /**
     * 初始化实时ASR
     */
    fun externalInitAsr() {
        initPcmRecorderController(true)
        asrController?.externalInitAsr()
    }
}