/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AIFileAsrClient
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/6 1.0 create
 */

package com.soundrecorder.translate.asr.file

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import com.oplus.ai.asrkit.AiAsrKit
import com.oplus.ai.asrkit.callback.AsrCallback
import com.oplus.ai.asrkit.callback.FileCallback
import com.oplus.ai.asrkit.common.Constants
import com.oplus.ai.asrkit.data.config.BaseConfigData
import com.oplus.ai.asrkit.data.config.InitConfigData
import com.oplus.ai.asrkit.data.request.BaseFileRequest
import com.oplus.ai.asrkit.data.request.SendFileRequest
import com.oplus.ai.asrkit.data.response.AsrAckRsp
import com.oplus.ai.asrkit.data.response.AsrAudioAck
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OpenIdUtils
import com.soundrecorder.translate.asr.file.IFileAsrKit.Companion.ASR_PARAM_CALL_ID
import com.soundrecorder.translate.asr.file.IFileAsrKit.Companion.DEFAULT_VALUE_END_TIMEOUT
import com.soundrecorder.translate.asr.file.IFileAsrKit.Companion.DEFAULT_VALUE_QUERY_DURATION
import com.soundrecorder.translate.asr.file.IFileAsrKit.Companion.DEFAULT_VALUE_REQUEST_DURATION
import com.soundrecorder.translate.asr.listener.IFileAsrListener
import com.soundrecorder.translate.AsrConstants
import com.soundrecorder.translate.AsrConstants.APP_TAG
import com.soundrecorder.translate.AsrConstants.PKG_NAME_AIUNIT
import com.soundrecorder.translate.AsrConstants.SUB_APP_TAG
import com.soundrecorder.translate.util.DeviceInfoUtil
import java.util.concurrent.ConcurrentHashMap

/**
 * 非实时asr
 * https://odocs.myoas.com/docs/wV3VVxNB4MsDKv3y
 */
class AIFileAsrClient(val context: Context) : IFileAsrKit {
    companion object {
        private const val TAG = "AIFileAsrClient"
        const val EXTRA_FILE_RECORD_ID = "record_id"
        const val EXTRA_FILE_DURATION = "file_duration"
        const val EXTRA_FILE_URI = "file_uri"
        const val EXTRA_FILE_PATH = "file_path"

        /*设置心跳时间*/
        const val HEART_BEAT_TIME = 6000L
    }

    private var asrListener: ConcurrentHashMap<Long, IFileAsrListener> = ConcurrentHashMap<Long, IFileAsrListener>()
    private var asrClient: AiAsrKit? = null
    private val deviceDId by lazy {
        OpenIdUtils.INSTANCE.duid.run {
            /*调式中发现重置手机后有概率第一次进入录音，获取到duid为null，这里坐下处理，为null就使用uuid。该字段使用于记录摘要可用次数，若为null当做另外设备*/
            if (isBlank()) {
                DebugUtil.w(TAG, "file asr did is empty")
                return@run OpenIdUtils.INSTANCE.uuid
            }
            return@run this
        }
    }

    /*目前都走云侧*/
    private val asrType = Constants.AsrModelType.CLOUD
    private var fileAsrParser: FileAsrParser? = null

    init {
        asrClient = AiAsrKit(context, asrType)
        fileAsrParser = FileAsrParser()
    }

    override fun registerAsrListener(mediaId: Long, listener: IFileAsrListener?) {
        listener?.let {
            asrListener[mediaId] = it
        }
        DebugUtil.i(TAG, "registerAsrListener,size = ${asrListener.size}")
    }

    override fun unRegisterAsrListener(mediaId: Long, listener: IFileAsrListener?) {
        asrListener.remove(mediaId)
        DebugUtil.i(TAG, "unRegisterAsrListener,size = ${asrListener.size}")
    }

    override fun initAsr(params: Bundle?, initResult: ((result: Boolean) -> Unit)) {
        DebugUtil.i(TAG, "init asr")
        val config = InitConfigData(
            modelType = asrType, channelId = null, // 实时长连接channelID
            callId = params?.getString(ASR_PARAM_CALL_ID), //非实时callId
            markKeywords = true, //非实时是否标记敏感词
            pushDataTime = DEFAULT_VALUE_REQUEST_DURATION, // 非实时发送音频间隔时间
            queryTextTime = DEFAULT_VALUE_QUERY_DURATION, // 非实时查询文本间隔时间
            offline = true, // 非实时是否离线,后续通话需求预留
            lastHttpTimeOut = DEFAULT_VALUE_END_TIMEOUT, //非实时发送超时
            businessType = Constants.AsrBusinessType.NON_REALTIME, appTag = APP_TAG, subAppTag = SUB_APP_TAG)

        asrClient?.initAsr(config, object : AsrCallback {
            override fun onAsr(audioAck: AsrAudioAck) {
                // 这个是实时结果回调，非实时不关心
                DebugUtil.i(TAG, "initAsr onAsr...")
            }

            override fun onError(errorCode: Int, errorMsg: String?, expends: String?) {
                DebugUtil.i(TAG, "initAsr onError,$errorCode-$errorMsg-$expends")
                initResult.invoke(errorCode == 0)
            }

            override fun onResult(result: AsrAckRsp?) {
                DebugUtil.i(TAG, "initAsr-onResult()$result") // 这个是实时状态回调，非实时不关心
            }
        })
    }

    override fun startAsr(extra: Bundle?) {
        val mediaId = extra?.getLong(EXTRA_FILE_RECORD_ID)
        val filePath = extra?.getString(EXTRA_FILE_PATH) ?: ""
        val fileUri = extra?.getParcelable(EXTRA_FILE_URI) as? Uri
        val duration = extra?.getLong(EXTRA_FILE_DURATION)
        if (mediaId == null || fileUri == null || duration == null) {
            DebugUtil.w(TAG, "startAsr param check failure ")
            return
        }
        context.grantUriPermission(PKG_NAME_AIUNIT, fileUri, Intent.FLAG_GRANT_READ_URI_PERMISSION)

        asrClient?.initFile(BaseFileRequest(deviceDId, "$mediaId", null), object : FileCallback {
            override fun onError(errorCode: Int, errorMsg: String?, expends: String?) {
                DebugUtil.i(TAG, "startAsr onError-$mediaId,$errorCode-$errorMsg-$expends")
                onStatus(errorCode, errorMsg, mediaId.toString())
            }

            override fun onFileDealStatus(name: String?, message: String?, recordId: String?) {
                /*name：AI_PHONE_READ_FILE_QUERY_COMPLETE message返回了转文本内容*/
                DebugUtil.d(TAG, "onFileDealStatus callback-$recordId: code = $name")
                onStatus(fileAsrParser?.convertCodeResult(name, message), recordId)
            }

            override fun onFileHeart(recordId: String?) {
                onStatus(AsrConstants.Status.ASR_OFFLINE_HEART_BEAT, recordId)
            }

            override fun onFileResult(jsonResult: String?, recordId: String?) {
                DebugUtil.i(TAG, "startAsr onFileResult- $recordId:textSize ${jsonResult?.length}")
                onAsrResult(jsonResult, recordId)
            }
        })
        asrClient?.sendFileData(
            SendFileRequest(
                deviceModel = DeviceInfoUtil.getDeviceModel(), //机型
                region = DeviceInfoUtil.getRegion(), //区域
                language = "", //指定语言
                enableDetectLanguage = 0, //是否开启语种校验
                enableRisk = 0, //是否开启敏感词检测
                enableSmooth = if (BaseUtil.isEXP()) 0 else 1, //是否开启语气词过率
                filePathUp = fileUri.toString(), //上行录音文件地址
                peeIdUp = "0", //上行peerId
                durationUp = duration, //上行文件时长 单位：s
                heartBeatTime = HEART_BEAT_TIME, //离线asr 心跳时间 单位ms
                queryAsrTime = HEART_BEAT_TIME, //离线asr查询时间 单位ms
                recordId = "$mediaId", duid = deviceDId, extends = null))
    }

    override fun stopAsr(recordId: Long?) {
        DebugUtil.i(TAG, "stopAsr ")
        asrClient?.stopFileData(BaseFileRequest(deviceDId, "$recordId", null))
    }

    override fun retryAsr(recordId: Long) {
        DebugUtil.i(TAG, "retryAsr ")
        asrClient?.retryFile(BaseFileRequest(deviceDId, "$recordId", null))
    }

    override fun release(recordId: String?) {
        DebugUtil.i(TAG, "release,asrListener.size=${asrListener.size} ")
        if (asrListener.isEmpty()) {
            DebugUtil.w(TAG, "release...")
            asrClient?.release(
                BaseConfigData(appTag = APP_TAG, subAppTag = SUB_APP_TAG, businessType = Constants.AsrBusinessType.NON_REALTIME))
        }
    }

    private fun onStatus(code: Int, msg: String?, recordId: String?) {
        val key = recordId?.toLongOrNull()
        if (key != null) {
            asrListener[key]?.onStatus(code, msg)
            return
        }
        asrListener.values.forEach {
            it.onStatus(code, msg)
        }
    }

    private fun onStatus(status: AsrConstants.Status?, recordId: String?) {
        status?.let {
            onStatus(it.code, it.message, recordId)
        }
    }

    private fun onAsrResult(jsonResult: String?, recordId: String?) {
        recordId?.toLongOrNull()?.let {
            asrListener[it]?.onAsrResult(jsonResult)
        }
    }
}