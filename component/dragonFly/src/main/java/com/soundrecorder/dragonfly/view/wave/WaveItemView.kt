/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveItemView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
// OPLUS Java File Skip Rule:MethodLength,MethodComplexity
package com.soundrecorder.dragonfly.view.wave

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.SystemClock
import android.util.AttributeSet
import android.view.View
import com.soundrecorder.dragonfly.utils.AppCardUtils.isRTL
import com.soundrecorder.dragonfly.R
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.DURATION_267
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.DURATION_283
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.DURATION_6
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.FLOAT_0_5
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.FLOAT_32768
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.INT_2
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.INT_3
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.INT_4
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.MAX_SCALE_HEIGHT
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.MID_SCALE_HEIGHT
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.MIN_SCALE_HEIGHT
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.getEnterHeightByTime
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.getLineScale
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.getOneWaveWidth

internal class WaveItemView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, def: Int = 0) : View(context, attrs, def) {
    companion object {
        private const val NUM_FLOAT_25 = 25f
        private const val NUM_INT_160 = 160
    }

    private val wavePaint = Paint().apply {
        color = Color.parseColor("#D9000000")
        isAntiAlias = true
        strokeCap = Paint.Cap.ROUND
    }

    private val dashPaint = Paint().apply {
        color = Color.parseColor("#D9666666")
        isAntiAlias = true
        strokeCap = Paint.Cap.ROUND
    }
    private val minWaveHeight by lazy { context.getOneWaveWidth() }
    private val maxWaveHeight by lazy { resources.getDimension(R.dimen.dp64) }
    private var viewIndex = 0
    private var hasRecording = false
    private var ampsSize = 0
    private var lastAmps = emptyList<Int>()
    private var doEnterAnimationTime = -1L
    fun refreshData(hasRecording: Boolean, ampsSize: Int, lastAmps: List<Int>, doEnterAnimationTime: Long) {
        this.hasRecording = hasRecording
        this.ampsSize = ampsSize
        this.lastAmps = lastAmps
        this.doEnterAnimationTime = doEnterAnimationTime
    }

    fun refreshColor(cardWaveColor: Int, cardDashWaveColor: Int) {
        if (wavePaint.color != cardWaveColor) {
            wavePaint.color = cardWaveColor
        }
        if (dashPaint.color != cardDashWaveColor) {
            dashPaint.color = cardDashWaveColor
        }
    }

    override fun onDraw(canvas: Canvas) {
        if (checkDoAnimator()) {
            if (viewIndex == 0) {
                drawFirstEnterItem(canvas)
            } else {
                drawEnterItem(canvas)
            }
        } else {
            if (viewIndex == 0) {
                drawFirstItem(canvas)
            } else {
                drawItem(viewIndex - 1, canvas)
            }
        }
    }

    private fun checkDoAnimator(): Boolean {
        val totalDelayTime = (halfParentWidth() / context.getOneWaveWidth()).toInt() * DURATION_6
        return SystemClock.elapsedRealtime() - doEnterAnimationTime <= totalDelayTime + DURATION_267 + DURATION_283
    }

    private fun calculateAnimationTime(waveCenterX: Float): Long {
        val centerLineX = halfParentWidth()
        val delayTime = if (waveCenterX < centerLineX) {
            ((centerLineX - waveCenterX) / context.getOneWaveWidth()).toInt() * DURATION_6
        } else {
            ((waveCenterX - centerLineX) / context.getOneWaveWidth()).toInt() * DURATION_6
        }
        val diffTime = SystemClock.elapsedRealtime() - doEnterAnimationTime
        return diffTime + delayTime
    }

    private fun drawFirstItem(canvas: Canvas) {
        val oneWaveWidth = context.getOneWaveWidth()
        val waveWidth = oneWaveWidth / INT_3
        val waveStartY = getStartYByHeight(waveWidth)
        val waveEndY = getEndYByHeight(waveWidth)
        if (isRTL()) {
            var drawStart = 0f
            while (drawStart <= width) {
                val waveLeft = drawStart + waveWidth
                val waveRight = drawStart + oneWaveWidth - waveWidth
                canvas.drawRoundRect(waveLeft, waveStartY, waveRight, waveEndY, waveWidth, waveWidth, dashPaint)
                drawStart += oneWaveWidth
            }
        } else {
            var drawStart = width.toFloat()

            while (drawStart >= 0) {
                val waveLeft = drawStart - waveWidth
                val waveRight = drawStart - oneWaveWidth + waveWidth
                canvas.drawRoundRect(waveLeft, waveStartY, waveRight, waveEndY, waveWidth, waveWidth, dashPaint)
                drawStart -= oneWaveWidth
            }
        }
    }

    private fun drawFirstEnterItem(canvas: Canvas) {
        val oneWaveWidth = context.getOneWaveWidth()
        if (isRTL()) {
            var drawStart = 0f
            val waveWidth = oneWaveWidth / INT_3
            while (drawStart <= width) {
                val waveLeft = drawStart + waveWidth
                val waveRight = drawStart + oneWaveWidth - waveWidth
                val currentAnimationTime = calculateAnimationTime((waveLeft + waveRight) * FLOAT_0_5)
                val waveHeight = getEnterHeightByTime(currentAnimationTime, waveWidth, oneWaveWidth * INT_4)
                val waveStartY = getStartYByHeight(waveHeight)
                val waveEndY = getEndYByHeight(waveHeight)
                canvas.drawRoundRect(waveLeft, waveStartY, waveRight, waveEndY, waveWidth, waveWidth, dashPaint)
                drawStart += oneWaveWidth
            }
        } else {
            var startRight = width.toFloat()
            val waveWidth = oneWaveWidth / INT_3
            while (startRight >= 0) {
                val waveLeft = startRight - waveWidth
                val waveRight = startRight - oneWaveWidth + waveWidth
                val currentAnimationTime = calculateAnimationTime(halfParentWidth() - (waveLeft + waveRight) * FLOAT_0_5)
                val waveHeight = getEnterHeightByTime(currentAnimationTime, waveWidth, oneWaveWidth * INT_4)
                val waveStartY = getStartYByHeight(waveHeight)
                val waveEndY = getEndYByHeight(waveHeight)
                canvas.drawRoundRect(waveLeft, waveStartY, waveRight, waveEndY, waveWidth, waveWidth, dashPaint)
                startRight -= oneWaveWidth
            }
        }
        invalidate()
    }

    private fun drawEnterItem(canvas: Canvas) {
        val oneWaveWidth = context.getOneWaveWidth()
        val waveWidth = oneWaveWidth / INT_3
        val waveCenterX = if (isRTL()) {
            halfParentWidth() - (left + right) * FLOAT_0_5
        } else {
            (left + right) * FLOAT_0_5 - halfParentWidth()
        }
        val currentAnimationTime = calculateAnimationTime(waveCenterX)
        val waveHeight = getEnterHeightByTime(currentAnimationTime, waveWidth, oneWaveWidth * INT_4)
        val waveStartY = getStartYByHeight(waveHeight)
        val waveEndY = getEndYByHeight(waveHeight)
        canvas.drawRoundRect(waveWidth, waveStartY, width - waveWidth, waveEndY, waveWidth, waveWidth, dashPaint)
        invalidate()
    }

    private fun drawItem(viewIndex: Int, canvas: Canvas) {
        val oneWaveWidth = context.getOneWaveWidth()
        val waveWidth = oneWaveWidth / INT_3
        val index = ampsSize - 1 - viewIndex
        if (lastAmps.isNotEmpty() && index in lastAmps.indices) {
            val preAmplitudeValue = lastAmps.getOrNull(index - 1) ?: 0
            val amplitudeValue = lastAmps.getOrNull(index) ?: 0
            var percent = if (isRTL()) {
                (right - halfParentWidth()) / (WaveViewUtil.INT_10 * context.getOneWaveWidth())
            } else {
                (halfParentWidth() - left) / (WaveViewUtil.INT_10 * context.getOneWaveWidth())
            }
            val hasDoAnimator: Boolean
            percent = if (percent < 0f) {
                hasDoAnimator = true
                0f
            } else if (percent > 1f) {
                hasDoAnimator = false
                1f
            } else {
                hasDoAnimator = true
                percent
            }
            var realLineHeight = getWaveLineHeight(preAmplitudeValue, amplitudeValue)
            if (realLineHeight < oneWaveWidth) {
                realLineHeight = oneWaveWidth
            }
            var lineHeight = realLineHeight * getLineScale(percent)
            if (lineHeight < waveWidth) {
                lineHeight = waveWidth
            }
            val waveStartY = getStartYByHeight(lineHeight)
            val waveEndY = getEndYByHeight(lineHeight)

            canvas.drawRoundRect(waveWidth, waveStartY, width - waveWidth, waveEndY, waveWidth, waveWidth, wavePaint)
            if (hasRecording && hasDoAnimator) {
                invalidate()
            }
        } else {
            val waveStartY = getStartYByHeight(waveWidth)
            val waveEndY = getEndYByHeight(waveWidth)
            canvas.drawRoundRect(waveWidth, waveStartY, width - waveWidth, waveEndY, waveWidth, waveWidth, dashPaint)
        }
    }

    /**
     * 根据传递的上一个波形和当前波形，计算出一个相对差距较小的波形值，使整体波形看起来更平滑
     *
     * @param preValue 上一个波形的时间高度
     * @param curValue     当前波形的时间高度
     * @return 重新计算后的当前位置波形高度
     */
    private fun getWaveLineHeight(preValue: Int, curValue: Int): Float {
        val value = (curValue + preValue) / INT_2
        var dScale = value / FLOAT_32768
        if (dScale > 1f) {
            dScale = 1f
        } else if (dScale < 0f) {
            dScale = 0f
        }
        var height: Float = maxWaveHeight * dScale
        if (height.compareTo(maxWaveHeight * MAX_SCALE_HEIGHT) < 0) {
            if (height.compareTo(maxWaveHeight * MIN_SCALE_HEIGHT) < 0) {
                height *= INT_2
            } else if (height.compareTo(maxWaveHeight * MID_SCALE_HEIGHT) < 0) {
                height *= INT_3
            }
        }
        if (height.compareTo(maxWaveHeight * NUM_FLOAT_25 / NUM_INT_160) <= 0) {
            height = minWaveHeight
        }
        return height
    }

    private fun getStartYByHeight(dotLineHeight: Float): Float {
        return (height - dotLineHeight - paddingBottom) / INT_2
    }

    private fun getEndYByHeight(dotLineHeight: Float): Float {
        return (height + dotLineHeight - paddingBottom) / INT_2
    }

    fun halfParentWidth(): Float {
        return ((parent as View).width - context.getOneWaveWidth()) * FLOAT_0_5
    }

    fun setCurViewIndex(index: Int) {
        viewIndex = index
    }
}

