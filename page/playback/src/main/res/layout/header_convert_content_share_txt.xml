<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp24">

    <TextView
        android:id="@+id/tv_play_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:lineSpacingExtra="3sp"
        android:textColor="@color/black_color"
        android:textSize="@dimen/sp14"
        tools:text="@string/export_save_file_name" />

    <TextView
        android:id="@+id/tv_create_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_play_name"
        android:layout_marginTop="@dimen/dp24"
        android:lineSpacingExtra="3sp"
        android:textColor="@color/black_color"
        android:textSize="@dimen/sp14"
        tools:text="@string/time" />

    <TextView
        android:id="@+id/tv_subject"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_create_time"
        android:lineSpacingExtra="3sp"
        android:textColor="@color/black_color"
        android:textSize="@dimen/sp14"
        tools:text="@string/export_content_theme" />
</RelativeLayout>