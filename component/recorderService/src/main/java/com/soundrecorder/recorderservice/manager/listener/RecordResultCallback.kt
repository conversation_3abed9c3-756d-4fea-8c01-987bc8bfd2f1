/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordResultCallback
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager.listener

import com.soundrecorder.recorderservice.RecordResult

interface RecordResultCallback {

    fun onRecordResultArrived(result: RecordResult)
}