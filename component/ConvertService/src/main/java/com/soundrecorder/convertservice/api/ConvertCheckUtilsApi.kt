/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertCheckUtilsApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.api

import android.content.Context
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.modulerouter.convertService.ConvertCheckUtilsAction

@Component(ConvertCheckUtilsAction.COMPONENT_NAME)
object ConvertCheckUtilsApi {

    @Action(ConvertCheckUtilsAction.ACTION_IS_FILE_SIZE_MIN_MET)
    @JvmStatic
    fun isFileSizeMinMet(fileSize: Long): Boolean {
        return ConvertCheckUtils.isFileSizeMinMet(fileSize)
    }

    @Action(ConvertCheckUtilsAction.ACTION_IS_FILE_SIZE_MAX_MET)
    @JvmStatic
    fun isFileSizeMaxMet(fileSize: Long): Boolean {
        return ConvertCheckUtils.isFileSizeMaxMet(fileSize)
    }

    @Action(ConvertCheckUtilsAction.ACTION_IS_FILE_DURATION_MIN_MET)
    @JvmStatic
    fun isFileDurationMinMet(duration: Long): Boolean {
        return ConvertCheckUtils.isFileDurationMinMet(duration)
    }

    @Action(ConvertCheckUtilsAction.ACTION_IS_FILE_DURATION_MAX_MET)
    @JvmStatic
    fun isFileDurationMaxMet(mFileDuration: Long): Boolean {
        return ConvertCheckUtils.isFileDurationMaxMet(mFileDuration)
    }

    @Action(ConvertCheckUtilsAction.ACTION_IS_FILE_FORMAT_MET)
    @JvmStatic
    fun isFileFormatMet(fileFormat: String): Boolean {
        return ConvertCheckUtils.isFileFormatMet(fileFormat)
    }

    @Action(ConvertCheckUtilsAction.GET_ERROR_MSG_BY_STATUS)
    @JvmStatic
    fun getErrorMsgByStatus(
        context: Context,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ): String {
        return ConvertCheckUtils.getErrorMsgByStatus(
            context,
            uploadStatus,
            convertStatus,
            errorMessage
        )
    }
}