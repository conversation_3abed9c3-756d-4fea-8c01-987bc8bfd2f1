apply from: "../../common_build.gradle"
apply plugin: 'obuildplugin'

android {
    namespace 'com.soundrecorder.dragonfly'

    defaultConfig {
        minSdkVersion 26
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    //版本号由模块版本号和后缀组成，固定配置
    version = "${prop_DragonFlyCardVersionName}" + "${versionSuffix}"
    OBuildConfig {
        //可选，如果是独立的SDK模块，可以配置这个参数，配置后编译task必须为  publishAllPublicationsToReleaseRepository
        //standAloneSdk = true
        //debug = true
        groupId = "${prop_archivesGroupName}"
        //配置SDK的artifactId，如果不配置，会使用当前module的目录名为sdkArtifactId
        sdkArtifactId = "DragonFlyCard"
        //执行SDK打包任务，约定为assembleReleaseSplitPixel，执行后，会将SDK推送到SNAPSHOT-maven：并同时发布到JFrog，以便后续正式提测发布；
        //内置assembleOapm，assembleRelease都可以出包；如果配置了 standAloneSdk = true，则task需要变为 publishAllPublicationsToReleaseRepository
//        sdkExecuteTask = "assembleReleaseSplitPixel"
        sdkExecuteTask = "assemblePublishToReleaseRepository"
        moduleDescription = "DragonFlyCard lib sdk, only use for DragonFly Card(n*4)."
    }
}

dependencies {
    implementation 'androidx.transition:transition:1.4.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "com.oplus.smartengine:customlib:1.0.5"
    implementation "com.oplus.smartengine:customannotation:1.0.2"
    implementation "com.google.code.gson:gson:$gson_version"
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'com.google.android.material:material:1.7.0'
    implementation "com.oplus.appcompat:core:${prop_versionName}"
    implementation "com.oplus.appcompat:recyclerview:${prop_versionName}"
}
