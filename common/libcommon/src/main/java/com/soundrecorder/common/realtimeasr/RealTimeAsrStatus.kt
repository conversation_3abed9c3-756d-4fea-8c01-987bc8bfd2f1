/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: OnRealtimeSubtitleUpdateListener
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: W9085798
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9085798 2025/5/18 1.0 create
 */
package com.soundrecorder.common.realtimeasr

/**
 * 实时ASR状态
 */
enum class RealTimeAsrStatus {
    /**
     * 默认状态
     */
    ASR_DEFAULT,

    /**
     * 正在初始化
     */
    ASR_INITIALIZING,

    /**
     * 正在建立通道
     */
    ASR_STARTING,

    /**
     * 已经建连
     */
    ASR_RUNNING,

    /**
     * 正在停止
     */
    ASR_STOPPING,

    /**
     * 正在释放
     */
    ASR_RELEASING;

    fun isStartingUp(): <PERSON><PERSON><PERSON> {
        return when (this) {
            ASR_INITIALIZING, ASR_STARTING, ASR_RUNNING -> true
            else -> false
        }
    }
}