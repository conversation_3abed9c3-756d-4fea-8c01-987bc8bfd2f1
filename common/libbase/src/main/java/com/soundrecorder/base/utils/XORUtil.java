package com.soundrecorder.base.utils;

import java.nio.charset.StandardCharsets;

public class XORUtil {

//    public static String enOrDecrypt(String str, int key) {
//        char[] mwChar = str.toCharArray();
//        for (int i = 0; i < mwChar.length; i++) {
//            mwChar[i] = (char) (mwChar[i] ^ key);
//        }
//        return new String(mwChar);
//    }

    public static String enOrDecrypt(String a, int b) {
        byte c[] = a.getBytes(StandardCharsets.UTF_8);

        for (int i = 0; i < c.length; i++) {
            c[i] = (byte) (c[i] ^ (int) b);//异或运算
        }
        return new String(c, 0, c.length, StandardCharsets.UTF_8);//新的字符串

    }
}
