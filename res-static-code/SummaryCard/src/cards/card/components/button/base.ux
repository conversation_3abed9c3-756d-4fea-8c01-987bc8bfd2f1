<template>
  <div
    class="widgetui-btn {{ isDark ? 'widgetui-btn--dark' : '' }}"
    forcedark="false"
    style="{{ buttonStyle }}"
  >
    <div if="{{ icon }}" class="widgetui-btn-icon">
      <slot name="icon"></slot>
    </div>
    <text class="widgetui-btn-text" style="{{ textStyle }}">
      <slot></slot>
    </text>
    <div class="widgetui-btn-mask"></div>
  </div>
</template>

<script>
/**
 * @file 按钮组件
 */

const buttonWidth = {
  list: 68,
  short: 124,
  medium: 160,
  large: 292,
  double: 140,
}

export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'stretch',
    },
    icon: {
      type: Boolean,
      default: false,
    },
    backgroundColor: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '',
    },
  },

  computed: {
    buttonStyle() {
      return {
        width: this.type === 'stretch' ? '100%' : buttonWidth[this.type] + 'px',
        ...(this.backgroundColor
          ? {
            backgroundColor: this.backgroundColor,
          }
          : {}),
      }
    },

    textStyle() {
      return this.color
        ? {
          color: this.color,
        }
        : {}
    },
  },
}
</script>

<style lang="less">
.widgetui-btn {
  flex-shrink: 0;
  position: relative;
  justify-content: center;
  align-items: center;
  height: 32px;
  border-radius: 16px;
  background-color: #f0f0f0;

  .widgetui-btn-icon {
    margin-right: 8px;
    width: 18px;
    height: 18px;
  }

  .widgetui-btn-text {
    font-size: 12px;
    line-height: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.9);
  }

  .widgetui-btn-mask {
    width: 100%;
    height: 100%;
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
  }

  .widgetui-btn-mask:active {
    background-color: rgba(0, 0, 0, 0.12);
  }
}

.widgetui-btn--dark {
  background-color: #0066ff;

  .widgetui-btn-text {
    color: rgba(255, 255, 255, 1);
  }
}
</style>
