/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ChangeScaleTransition
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.common.transition


import android.animation.Animator
import android.animation.PropertyValuesHolder
import android.animation.ValueAnimator
import android.view.ViewGroup
import androidx.annotation.NonNull
import androidx.transition.Transition
import androidx.transition.TransitionValues
import com.soundrecorder.common.BuildConfig

class ChangeScaleTransition(vararg val ids: Int) : Transition() {
    companion object {
        private const val PROP_NAME_CHANGE_SCALE_X =
            "${BuildConfig.LIBRARY_PACKAGE_NAME}:change:scaleX"
        private const val PROP_NAME_CHANGE_SCALE_Y =
            "${BuildConfig.LIBRARY_PACKAGE_NAME}:change:scaleY "
    }

    override fun captureEndValues(@NonNull transitionValues: TransitionValues) {
        val view = transitionValues.view ?: return
        if (view.id in ids) {
            transitionValues.values[PROP_NAME_CHANGE_SCALE_X] = view.scaleX
            transitionValues.values[PROP_NAME_CHANGE_SCALE_Y] = view.scaleY
        }
    }

    override fun captureStartValues(@NonNull transitionValues: TransitionValues) {
        val view = transitionValues.view ?: return
        if (view.id in ids) {
            transitionValues.values[PROP_NAME_CHANGE_SCALE_X] = view.scaleX
            transitionValues.values[PROP_NAME_CHANGE_SCALE_Y] = view.scaleY
        }
    }

    override fun createAnimator(
        sceneRoot: ViewGroup,
        startValues: TransitionValues?,
        endValues: TransitionValues?
    ): Animator? {
        if (startValues == null || endValues == null || endValues.view == null) {
            return null
        }
        val view = endValues.view
        val startScaleX = startValues.values[PROP_NAME_CHANGE_SCALE_X] as Float
        val endScaleX = endValues.values[PROP_NAME_CHANGE_SCALE_X] as Float

        val startScaleY = startValues.values[PROP_NAME_CHANGE_SCALE_Y] as Float
        val endScaleY = endValues.values[PROP_NAME_CHANGE_SCALE_Y] as Float

        if (startScaleX != endScaleX && startScaleY != endScaleY) {
            val scaleXHolder =
                PropertyValuesHolder.ofFloat(PROP_NAME_CHANGE_SCALE_X, startScaleX, endScaleX)
            val scaleYHolder =
                PropertyValuesHolder.ofFloat(PROP_NAME_CHANGE_SCALE_Y, startScaleY, endScaleY)
            val animator = ValueAnimator.ofPropertyValuesHolder(scaleXHolder, scaleYHolder)
            animator.addUpdateListener { animation ->
                view.scaleX = animation.getAnimatedValue(PROP_NAME_CHANGE_SCALE_X) as Float
                view.scaleY = animation.getAnimatedValue(PROP_NAME_CHANGE_SCALE_Y) as Float
            }
            return animator
        } else if (startScaleX != endScaleX) {
            return createAnimator(startScaleX, endScaleX) {
                view.scaleX = it
            }
        } else if (startScaleY != endScaleY) {
            return createAnimator(startScaleY, endScaleY) {
                view.scaleY = it
            }
        }
        return null
    }

    private fun createAnimator(
        startValue: Float,
        endValue: Float,
        updateFunc: (value: Float) -> Unit
    ): Animator {
        return ValueAnimator.ofFloat(startValue, endValue).apply {
            addUpdateListener { animation ->
                updateFunc.invoke(animation.animatedValue as Float)
            }
        }
    }

    override fun getTransitionProperties() =
        arrayOf(PROP_NAME_CHANGE_SCALE_X, PROP_NAME_CHANGE_SCALE_Y)
}