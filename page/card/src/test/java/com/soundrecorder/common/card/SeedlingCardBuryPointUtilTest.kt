/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingCardBuryPointUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/10/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.card.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SeedlingCardBuryPointUtilTest {

    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication!!.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun check_addSeedlingCardMarkBtnClickEvent() {
        SeedlingCardBuryPointUtil.addSeedlingCardMarkBtnClickEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addSeedlingCardFluidRecordBtnClickEvent() {
        SeedlingCardBuryPointUtil.addSeedlingCardFluidRecordBtnClickEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean()
            )
        }, Mockito.times(1))
    }
}