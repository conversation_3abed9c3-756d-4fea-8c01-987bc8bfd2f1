/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SmallCardManagerTest
 Description:
 Version: 1.0
 Date: 2022/9/22
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/10/14 1.0 create
 */

package com.soundrecorder.record.card.small

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.record.shadows.ShadowClearDataUtils
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.record.shadows.ShadowOplusUsbEnvironment
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import oplus.multimedia.soundrecorder.card.small.SmallCardManager
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.Mockito.spy
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowClearDataUtils::class]
)
class SmallCardManagerTest {

    private val mockStatic = Mockito.mockStatic(PermissionUtils::class.java)
    private var mRecordService: RecorderService? = null

    @Before
    fun setUp() {
        mRecordService = spy(Mockito.mock(RecorderService::class.java))
    }

    @After
    fun release() {
        mockStatic.close()
    }

    @Test
    fun onPack() {
        RecorderViewModel.getInstance().getAmplitudeList()
        mRecordService?.recorderController?.recorderAmplitudeModel?.addOneAmplitudeCache(10)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.INIT)
        SmallCardManager.getCustomData("0&0&0")
        AudioModeChangeManager.changeAudioPause(true)
        AudioModeChangeManager.changeNeedResume(true)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.PAUSED)
        SmallCardManager.getCustomData("0&0&0")
        AudioModeChangeManager.changeAudioPause(false)
        AudioModeChangeManager.changeNeedResume(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING)
        SmallCardManager.getCustomData("0&0&0")
        RecordStatusManager.setCurrentStatus(RecordStatusManager.HALT_ON)
        SmallCardManager.getCustomData("0&0&0")
        RecorderViewModelAction.saveFileState = RecorderViewModelAction.SaveFileState.INIT
        SmallCardManager.getCustomData("0&0&0")
        RecorderViewModelAction.saveFileState = RecorderViewModelAction.SaveFileState.ERROR
        SmallCardManager.getCustomData("0&0&0")
        RecorderViewModelAction.saveFileState = RecorderViewModelAction.SaveFileState.START_LOADING
        SmallCardManager.getCustomData("0&0&0")
        RecorderViewModelAction.saveFileState = RecorderViewModelAction.SaveFileState.SHOW_LOADING_DIALOG
        SmallCardManager.getCustomData("0&0&0")
        RecorderViewModelAction.saveFileState = RecorderViewModelAction.SaveFileState.SUCCESS
        SmallCardManager.getCustomData("0&0&0")
        mRecordService?.otherConfig?.isFromOtherApp = true
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING)
        SmallCardManager.getCustomData("0&0&0")
    }

    @Test
    fun parseMethod() {
        mockStatic.`when`<Boolean> { PermissionUtils.hasReadAudioPermission() }.thenReturn(true, true, false)
        mockStatic.`when`<Boolean> { PermissionUtils.hasRecordAudioPermission() }.thenReturn(true, true, false)
        SmallCardManager.parseMethod("pull", "0&0&0")
        SmallCardManager.parseMethod("start_recorder_service", "0&0&0")
        SmallCardManager.parseMethod("switch_recorder_status", "0&0&0")
        SmallCardManager.parseMethod("add_text_mark", "0&0&0")
        SmallCardManager.parseMethod("save_recorder_file", "0&0&0")
    }

    @Test
    fun onReadyService() {
        SmallCardManager.onReadyService()
    }

    @Test
    fun onCloseService() {
        SmallCardManager.onCloseService()
    }

    @Test
    fun onWaveStateChange() {
        SmallCardManager.onWaveStateChange(RecorderViewModelAction.WaveState.START)
        SmallCardManager.onWaveStateChange(RecorderViewModelAction.WaveState.UPDATE)
    }

    @Test
    fun onSaveFileStateChange() {
        SmallCardManager.onSaveFileStateChange(RecorderViewModelAction.SaveFileState.SHOW_LOADING_DIALOG)
        SmallCardManager.onSaveFileStateChange(RecorderViewModelAction.SaveFileState.SUCCESS)
        SmallCardManager.onSaveFileStateChange(RecorderViewModelAction.SaveFileState.INIT)
    }

    @Test
    fun onRecordStatusChange() {
        SmallCardManager.onRecordStatusChange(RecordStatusManager.RECORDING)
    }

    @Test
    fun onRecordCallConnected() {
        SmallCardManager.onRecordCallConnected()
    }

    @Test
    fun onMarkDataChange() {
        SmallCardManager.onMarkDataChange(RecorderViewModelAction.MarkAction.SINGLE_ADD, -1)
    }

    @Test
    fun onRequestPermissionCallBack() {
        SmallCardManager.onRequestPermissionCallBack(isMethodStartService = false, hasPermission = true)
        var needRunMarkAndSaveAnimation = Whitebox.getInternalState<Boolean>(SmallCardManager.javaClass, "needRunMarkAndSaveAnimation")
        Assert.assertFalse(needRunMarkAndSaveAnimation)

        SmallCardManager.onRequestPermissionCallBack(isMethodStartService = true, hasPermission = false)
        needRunMarkAndSaveAnimation = Whitebox.getInternalState<Boolean>(SmallCardManager.javaClass, "needRunMarkAndSaveAnimation")
        Assert.assertFalse(needRunMarkAndSaveAnimation)

        SmallCardManager.onRequestPermissionCallBack(isMethodStartService = true, hasPermission = true)
        needRunMarkAndSaveAnimation = Whitebox.getInternalState<Boolean>(SmallCardManager.javaClass, "needRunMarkAndSaveAnimation")
        Assert.assertTrue(needRunMarkAndSaveAnimation)
    }
}