/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryStyleHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.content.Context
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.R
import com.soundrecorder.summary.data.SummaryDataParser
import java.util.regex.Pattern

@Suppress("StringTemplate")
object SummaryStyleHelper {
    private const val TAG = "SummaryStyleHelper"
    private const val LIST_SYMBOL = "\n   - "
    private const val LIST_SYMBOL_REPLACE = "\n * "
    private const val TASK_SYMBOL = "- [ ] "
    private const val TASK_DONE_SYMBOL = "- [x] "
    private const val LINE_BREAKS = "\n"

    private val taskListPattern = Pattern.compile("^- \\[([xX\\s])]\\s+.*")

    @JvmStatic
    fun formatSummaryContent(
        context: Context,
        originText: String,
        trace: List<SummaryTrace>,
        entities: List<SummaryEntity>,
        agents: List<SummaryAgentEvent>,
    ): String {
        val newTextReplaceListSymbol = formatSummaryContentByList(originText)
        val newTextWithTaskList =
            formatSummaryContentByTask(context, newTextReplaceListSymbol, agents)
        val newTextWithTrace = formatSummaryContentByTrace(newTextWithTaskList, trace)
        val newTextWithSummaryEntity =
            formatSummaryContentBySummaryEntity(newTextWithTrace, entities)
        DebugUtil.i(
            TAG,
            "formatSummaryContent newTextWithSummaryEntity = $newTextWithSummaryEntity"
        )
        return newTextWithSummaryEntity
    }

    @JvmStatic
    private fun formatSummaryContentByTrace(
        originText: String,
        traces: List<SummaryTrace>
    ): String {
        val replacements = traces.map {
            it.traceText to traceFormat(it)
        }
        var result = originText
        for ((target, append) in replacements) {
            result = result.replace(target, target + append)
        }
        return result
    }

    @JvmStatic
    private fun traceFormat(trace: SummaryTrace): String {
        val time = trace.startTime.durationInMsFormatTimeExclusive()
        val entityObject = GsonUtil.getGson().toJson(trace)
        return "[`$time`](#$entityObject)"
    }

    @JvmStatic
    private fun formatSummaryContentBySummaryEntity(
        originText: String,
        entities: List<SummaryEntity>
    ): String {
        val taskListItemPattern = taskListPattern
        val patterns = entities.map { linkFormat(it) to Pattern.compile(Pattern.quote(it.name)) }
        val lines = originText.split(LINE_BREAKS)
        val transformedLines = lines.map { line ->
            val matcher = taskListItemPattern.matcher(line)
            if (matcher.find()) {
                line
            } else {
                var transformedLine = line
                for (pattern in patterns) {
                    if (pattern.second.matcher(transformedLine).find()) {
                        transformedLine =
                            pattern.second.matcher(transformedLine).replaceFirst(pattern.first)
                        break
                    }
                }
                transformedLine
            }
        }
        return transformedLines.joinToString("\n")
    }

    @JvmStatic
    private fun linkFormat(entity: SummaryEntity): String {
        val entityObject = GsonUtil.getGson().toJson(entity)
        return "[${entity.name}]($entityObject)"
    }

    @JvmStatic
    private fun formatSummaryContentByList(originText: String): String {
        return originText.replace(LIST_SYMBOL, LIST_SYMBOL_REPLACE)
    }

    @JvmStatic
    private fun formatSummaryContentByTask(
        context: Context,
        originText: String,
        agents: List<SummaryAgentEvent>
    ): String {
        var agentStart: String
        val agentEnd: String
        if (BaseUtil.isEXP()) {
            agentStart = context.getString(R.string.summary_agent_export)
            agentEnd = context.getString(R.string.summary_agent_export_end)
        } else {
            agentStart = context.getString(R.string.summary_agent)
            val index = originText.indexOf(agentStart)
            if (index == -1) {
                agentStart = context.getString(R.string.summary_agent_action)
            }
            agentEnd = context.getString(R.string.summary_agent_end)
        }
        val startIndex = originText.indexOf(agentStart)
        if (startIndex == -1) {
            return originText
        }
        val endIndex = originText.indexOf(agentEnd, startIndex + agentStart.length)
        val subStringB = if (endIndex == -1) {
            originText.substring(startIndex + agentStart.length)
        } else {
            originText.substring(startIndex + agentStart.length, endIndex)
        }

        val lines = subStringB.split(LINE_BREAKS)
        val processedLines = lines.mapNotNull { line ->
            val cleanedLine = line.trim().replace(Regex("^[^a-zA-Z\\u4e00-\\u9fa5]+"), "")
            if (cleanedLine.isEmpty()) {
                null
            } else {
                val agent = agents.singleOrNull {
                    val formatAgent = SummaryDataParser.replaceEntity(cleanedLine)
                    it.agent == formatAgent
                }
                agent?.let { formatTask(agent.isDone, cleanedLine) } ?: run { null }
            }
        }
        val result =
            originText.substring(
                0,
                startIndex
            ) + agentStart + LINE_BREAKS + processedLines.joinToString(LINE_BREAKS)
        return result
    }

    @JvmStatic
    private fun formatTask(isDone: Boolean, agent: String): String {
        return if (isDone) {
            "$TASK_DONE_SYMBOL$agent"
        } else {
            "$TASK_SYMBOL$agent"
        }
    }

    @JvmStatic
    fun updateStyleAfterTaskStateChange(originText: String, isDone: Boolean, task: String): String {
        val checkboxUnchecked = "$TASK_SYMBOL$task"
        val checkboxChecked = "$TASK_DONE_SYMBOL$task"
        val updated = if (isDone && originText.contains(checkboxUnchecked)) {
            checkboxChecked
        } else if (isDone.not() && originText.contains(checkboxChecked)) {
            checkboxUnchecked
        } else {
            task
        }
        return originText.replace(checkboxUnchecked, updated).replace(checkboxChecked, updated)
    }
}