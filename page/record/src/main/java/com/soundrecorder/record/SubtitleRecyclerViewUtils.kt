/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleRecyclerViewUtils.kt
 * Description:
 *     The helper class for inserting ui height.
 *
 * Version: 1.0
 * Date: 2025-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-06-04   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

object SubtitleRecyclerViewUtils {
    @JvmStatic
    fun getVisibleHeight(recyclerView: RecyclerView): Int {
        return recyclerView.height - recyclerView.paddingTop - recyclerView.paddingBottom
    }

    @JvmStatic
    fun estimateTotalHeight(recyclerView: RecyclerView): Int {
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return 0
        var totalHeight = 0
        for (i in 0 until layoutManager.itemCount) {
            val view = layoutManager.findViewByPosition(i) ?: continue
            totalHeight += view.height
        }
        return totalHeight
    }
}