/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderViewModelAction
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.modulerouter.recorder

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import org.json.JSONObject

object RecorderViewModelAction {

    /**
     * MarkAction
     * WaveState
     * SaveFileState
     * 与RecorderViewModel里面保持一致
     */
    object MarkAction {
        const val SINGLE_ADD = 0
        const val MULTI_ADD = 1
        const val DELETE = 2
        const val RENAME = 3
    }

    object WaveState {
        const val START = 0
        const val STOP = 1
        const val UPDATE = 2
    }

    object SaveFileState {
        const val INIT = -1
        const val START_LOADING = 0
        const val SHOW_LOADING_DIALOG = 1
        const val SUCCESS = 2
        const val ERROR = 3
    }


    const val INIT = -1
    const val HALT_ON = 0
    const val RECORDING = 1
    const val PAUSED = 2

    const val COMPONENT_NAME = "RecorderViewModelAction"
    const val GET_MARK_DATA = "getMarkData"
    const val GET_LAST_MARK_TIME = "getLastMarkTime"
    const val GET_RECORD_TYPE = "getRecordType"
    const val HAS_INIT_AMPLITUDE = "hasInitAmplitude"
    const val GET_AMPLITUDE_CURRENT_TIME = "getAmplitudeCurrentTime"
    const val GET_AMPLITUDE_LIST = "getAmplitudeList"
    const val GET_LATEST_AMPLITUDE = "getLatestAmplitude"
    const val GET_MAX_AMPLITUDE = "getMaxAmplitude"
    const val GET_MARK_ENABLED_LIVE_DATA = "getMarkEnabledLiveData"
    const val IS_MARK_ENABLED_FULL = "isMarkEnabledFull"
    const val CHECK_MARK_DATA_MORE_THAN_MAX = "checkMarkDataMoreThanMax"
    const val IS_NEED_SHOW_NOTIFICATION_PERMISSION_DENIED_SNACKBAR = "isNeedShowNotificationPermissionDeniedSnackBar"
    const val RESET_NEED_SHOW_NOTIFICATION_PERMISSION_DENIED_SNACKBAR = "resetNeedShowNotificationPermissionDeniedSnackBar"
    const val START = "start"
    const val RESUME = "resume"
    const val PAUSE = "pause"
    const val CANCEL = "cancel"
    const val STOP = "stop"
    const val CANCEL_RECORD_NOTIFICATION = "cancelRecordNotification"
    const val GET_SAMPLE_URI = "getSampleUri"
    const val GET_SUFFIX = "getSuffix"
    const val GET_RECORD_FILE_PATH = "getRecordFilePath"
    const val GET_RELATIVE_PATH = "getRelativePath"
    const val GET_DEFAULT_DISPLAY_NAME = "getDefaultDisplayName"
    const val GET_RECORD_MODE_NAME = "getRecordModeName"
    const val GET_FILE_BEING_RECORDED = "getFileBeingRecorded"
    const val IS_QUICK_RECORD = "isQuickRecord"
    const val SAVE_RECORD_INFO = "saveRecordInfo"
    const val HAS_INIT_RECORDER_SERVICE = "hasInitRecorderService"
    const val STOP_SERVICE = "stopService"
    const val SWITCH_RECORDER_STATUS = "switchRecorderStatus"
    const val ADD_MARK = "addMark"
    const val ADD_MULTI_PICTURE_MARK = "addMultiPictureMark"
    const val REMOVE_MARK = "removeMark"
    const val RENAME_MARK = "renameMark"
    const val IS_FROM_SLID_BAR = "isFromSlidBar"
    const val IS_FROM_MINI_APP = "isFromMiniApp"
    const val IS_FROM_APP_CARD = "isFromAppCard"
    const val IS_FROM_SMALL_CARD = "isFromSmallCard"
    const val IS_FROM_OTHER_APP = "isFromOtherApp"
    const val IS_FROM_BRENO = "isFromBreno"
    const val IS_FROM_CUBE_BUTTON_OR_LOCK_SCREEN = "isFromCubeButtonOrLockScreen"

    const val ADD_SOURCE_FOR_NOTIFICATION_BTN_DISABLED = "addSourceForNotificationBtnDisabled"
    const val ADD_LISTENER = "addListener"
    const val REMOVE_LISTENER = "removeListener"
    const val START_RECORDER_SERVICE = "startRecorderService"
    const val SET_DO_MULTI_PICTURE_MARK_LOADING = "setDoMultiPictureMarkLoading"
    const val IS_START_SERVICE_FROM_OTHER_APP = "isStartServiceFromOtherApp"

    const val GET_CURRENT_STATUS = "getCurrentStatus"
    const val GET_RECORD_STATUS_BEFORE_SAVING = "getRecordStatusBeforeSaving"
    const val GET_LAST_STATUS = "getLastStatus"
    const val IS_RECORD_SAVING = "isRecordSaving"
    const val IS_ALREADY_RECORDING = "isAlreadyRecording"
    const val CHECK_MODE_CAN_RECORD = "checkModeCanRecord"
    const val IS_AUDIO_MODE_CHANGE_PAUSE = "isAudioModeChangePause"
    const val IS_NEED_RESUME = "isNeedResume"
    const val CHECK_DIST_BEFORE_START_RECORD = "checkDistBeforeStartRecord"
    const val RECORD_STATUS_BAR_FORCE_HIDE = "recordStatusBarForceHide"
    const val STATUS_BAR_SEEDLING_DATA = "statusBarSeedlingData"
    const val GET_SAVE_PROGRESS_VALUE = "getSaveProgressValue"
    const val SHOW_OR_HIDE_STATUS_BAR = "showOrHideStatusBar"
    const val ON_SEEDLING_CARD_STATE_CHANGED = "onSeedlingCardStateChanged"
    const val FROM_SWITCH_RECORD_STATUS_NORMAL = "record_status_normal"
    const val FROM_SWITCH_RECORD_STATUS_SMALL_CARD = "record_status_small_card"
    const val FROM_SWITCH_RECORD_STATUS_MINI = "record_status_mini"
    const val FROM_SWITCH_RECORD_STATUS_APP_CARD = "record_status_app_card"
    const val CHANGE_ASR_LANGUAGE = "change_asr_language"
    const val REGISTER_REALTIME_ASR_LISTENER = "register_realtime_asr_listener"
    const val UNREGISTER_REALTIME_ASR_LISTENER = "unregister_realtime_asr_listener"
    const val START_TRANSLATION_CONFIG = "start_translation_config"
    const val GET_ALL_ASR_CONTENT = "get_all_asr_content"
    const val GET_REALTIME_ASR_STATUS = "get_realtime_asr_status"
    const val EXTERNAL_INIT_ASR = "external_init_asr"

    const val ON_FLUID_CARD_DISMISS = "fluidCardDismiss"

    const val GET_CUR_SELECTED_LANGUAGE = "getCurSelectedLanguage"
    const val SET_CUR_SELECTED_LANGUAGE = "setCurSelectedLanguage"
    const val GET_SUPPORT_LANGUAGE_LIST = "getSupportLanguageList"

    var saveFileState = SaveFileState.INIT

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun startRecorderService(function: Intent.() -> Unit) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                START_RECORDER_SERVICE
            ).param(function).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun getLastMarkTime(): Long {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_LAST_MARK_TIME
            ).build()
            OStitch.execute<Long>(apiRequest).result ?: 0L
        } else {
            0L
        }
    }

    @JvmStatic
    fun getRecordType(): Int {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_RECORD_TYPE
            ).build()
            OStitch.execute<Int>(apiRequest).result ?: 0
        } else {
            0
        }
    }

    @JvmStatic
    fun getAmplitudeList(): List<Int> {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_AMPLITUDE_LIST
            ).build()
            OStitch.execute<List<Int>>(apiRequest).result ?: listOf()
        } else {
            listOf()
        }
    }

    @JvmStatic
    fun getLatestAmplitude(): Int {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_LATEST_AMPLITUDE
            ).build()
            OStitch.execute<Int>(apiRequest).result ?: 0
        } else {
            0
        }
    }

    @JvmStatic
    fun getMaxAmplitude(): Int {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_MAX_AMPLITUDE
            ).build()
            OStitch.execute<Int>(apiRequest).result ?: 0
        } else {
            0
        }
    }

    @JvmStatic
    fun getMarkEnabledLiveData(): LiveData<Boolean>? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_MARK_ENABLED_LIVE_DATA
            ).build()
            OStitch.execute<LiveData<Boolean>?>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun isMarkEnabledFull(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_MARK_ENABLED_FULL
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun checkMarkDataMoreThanMax(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                CHECK_MARK_DATA_MORE_THAN_MAX
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isNeedShowNotificationPermissionDeniedSnackBar(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_NEED_SHOW_NOTIFICATION_PERMISSION_DENIED_SNACKBAR
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun resetNeedShowNotificationPermissionDeniedSnackBar() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                RESET_NEED_SHOW_NOTIFICATION_PERMISSION_DENIED_SNACKBAR
            ).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun start() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                START
            ).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun resume() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                RESUME
            ).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun pause() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                PAUSE
            ).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun cancel() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                CANCEL
            ).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun stop(): String? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                STOP
            ).build()
            OStitch.execute<String>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun cancelRecordNotification() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                CANCEL_RECORD_NOTIFICATION
            ).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun getSampleUri(): Uri? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_SAMPLE_URI
            ).build()
            OStitch.execute<Uri>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun getSuffix(): String? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_SUFFIX
            ).build()
            OStitch.execute<String>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun getRecordFilePath(): String? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_RECORD_FILE_PATH
            ).build()
            OStitch.execute<String>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun getRelativePath(): String? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_RELATIVE_PATH
            ).build()
            OStitch.execute<String>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun getSampleDisplayName(genNewNameWhenSampleNull: Boolean = true): String {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_DEFAULT_DISPLAY_NAME
            ).param(genNewNameWhenSampleNull).build()
            OStitch.execute<String>(apiRequest).result ?: ""
        } else {
            ""
        }
    }

    @JvmStatic
    fun getRecordModeName(): String {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME, GET_RECORD_MODE_NAME).param().build()
            OStitch.execute<String>(apiRequest).result ?: ""
        } else {
            ""
        }
    }

    @JvmStatic
    fun getFileBeingRecorded(): String? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_FILE_BEING_RECORDED
            ).build()
            OStitch.execute<String>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun isQuickRecord(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_QUICK_RECORD
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun saveRecordInfo(
        displayName: String? = null,
        originalDisplayName: String? = null,
        saveRecordFromWhere: Int,
        needStopService: Boolean = true
    ) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                SAVE_RECORD_INFO
            ).param(displayName, originalDisplayName, saveRecordFromWhere, needStopService).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun hasInitRecorderService(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                HAS_INIT_RECORDER_SERVICE
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun stopService() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                STOP_SERVICE
            ).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun switchRecorderStatus(from: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                SWITCH_RECORDER_STATUS
            ).param(from).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun removeMark(index: Int) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                REMOVE_MARK
            ).param(index).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun renameMark(newText: String, index: Int): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                RENAME_MARK
            ).param(newText, index).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isFromSlidBar(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_FROM_SLID_BAR
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isFromAppCard(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_FROM_APP_CARD
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isFromMiniCard(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_FROM_MINI_APP
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isFromSmallCard(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_FROM_SMALL_CARD
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isFromOtherApp(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_FROM_OTHER_APP
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isFromBreno(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_FROM_BRENO
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isFromCubeButtonOrLockScreen(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_FROM_CUBE_BUTTON_OR_LOCK_SCREEN
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun <T> getMarkData(): List<T> {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_MARK_DATA
            ).build()
            OStitch.execute<List<T>>(apiRequest).result ?: mutableListOf()
        } else {
            mutableListOf()
        }
    }

    @JvmStatic
    fun <T> addListener(listener: T) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                ADD_LISTENER
            ).param(listener).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun <T> removeListener(listener: T) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                REMOVE_LISTENER
            ).param(listener).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun <T> addMark(mark: T) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                ADD_MARK
            ).param(mark).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun <T> addMultiPictureMark(marks: ArrayList<T>): Int {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                ADD_MULTI_PICTURE_MARK
            ).param(marks).build()
            OStitch.execute<Int>(apiRequest).result ?: -1
        } else {
            -1
        }
    }

    @JvmStatic
    fun addSourceForNotificationBtnDisabled(addPictureMarking: MutableLiveData<Boolean>) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                ADD_SOURCE_FOR_NOTIFICATION_BTN_DISABLED
            ).param(addPictureMarking).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun setDoMultiPictureMarkLoading(doMultiPictureMarkLoading: Boolean) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                SET_DO_MULTI_PICTURE_MARK_LOADING
            ).param(doMultiPictureMarkLoading).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun isStartServiceFromOtherApp(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_START_SERVICE_FROM_OTHER_APP
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun getCurrentStatus(): Int {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_CURRENT_STATUS
            ).build()
            OStitch.execute<Int>(apiRequest).result ?: -1
        } else {
            -1
        }
    }

    @JvmStatic
    fun getRecordStatusBeforeSaving(): Int? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_RECORD_STATUS_BEFORE_SAVING
            ).build()
            OStitch.execute<Int>(apiRequest).result
        } else {
           null
        }
    }

    @JvmStatic
    fun getLastStatus(): Int {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_LAST_STATUS
            ).build()
            OStitch.execute<Int>(apiRequest).result ?: -1
        } else {
            -1
        }
    }

    @JvmStatic
    fun isAlreadyRecording(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_ALREADY_RECORDING
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isRecordSaving(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_RECORD_SAVING
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun checkModeCanRecord(needToast: Boolean): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                CHECK_MODE_CAN_RECORD
            ).param(needToast).build()
            OStitch.execute<Boolean>(apiRequest).result ?: true
        } else {
            true
        }
    }

    @JvmStatic
    fun isAudioModeChangePause(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_AUDIO_MODE_CHANGE_PAUSE
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun isNeedResume(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_NEED_RESUME
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun checkDistBeforeStartRecord(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                CHECK_DIST_BEFORE_START_RECORD
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: true
        } else {
            true
        }
    }

    @JvmStatic
    fun hasInitAmplitude(): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                HAS_INIT_AMPLITUDE
            ).build()
            OStitch.execute<Boolean>(apiRequest).result ?: true
        } else {
            true
        }
    }

    @JvmStatic
    fun getAmplitudeCurrentTime(): Long {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_AMPLITUDE_CURRENT_TIME
            ).build()
            OStitch.execute<Long>(apiRequest).result ?: 0L
        } else {
            0L
        }
    }

    @JvmStatic
    fun forceHideRecordStatusBar(from: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                RECORD_STATUS_BAR_FORCE_HIDE
            ).param(from).build()
            OStitch.execute<Unit>(apiRequest).result
        }
    }

    @JvmStatic
    fun getSeedlingData(): JSONObject? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                STATUS_BAR_SEEDLING_DATA
            ).build()
            OStitch.execute<JSONObject>(apiRequest).result
        } else null
    }

    @JvmStatic
    fun showOrHideStatusBar(from: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SHOW_OR_HIDE_STATUS_BAR).param(from).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun onSeedlingCardStateChanged(isShow: Boolean) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_SEEDLING_CARD_STATE_CHANGED).param(isShow).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun fluidCardDismiss(from: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_FLUID_CARD_DISMISS).param(from).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun getSaveProgressValue(): Int {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_SAVE_PROGRESS_VALUE
            ).build()
            OStitch.execute<Int>(apiRequest).result ?: 0
        } else 0
    }

    @JvmStatic
    fun getCurSelectedLanguage(): String {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_CUR_SELECTED_LANGUAGE
            ).build()
            OStitch.execute<String>(apiRequest).result ?: ""
        } else ""
    }

    @JvmStatic
    fun setCurSelectedLanguage(language: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                SET_CUR_SELECTED_LANGUAGE
            ).param(language).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun getSupportLanguageList(callback: (List<String>) -> Unit) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_SUPPORT_LANGUAGE_LIST
            ).param(callback).build()
            OStitch.execute<Void>(apiRequest)
        }
    }
}