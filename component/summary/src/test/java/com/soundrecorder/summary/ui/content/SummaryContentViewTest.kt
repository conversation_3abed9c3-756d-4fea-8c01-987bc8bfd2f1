package com.soundrecorder.summary.ui.content

import android.content.Context
import android.content.res.Resources
import io.mockk.every
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import com.soundrecorder.summary.R
import io.mockk.mockk

class SummaryContentViewTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk()
        val resource = mockk<Resources>()
        every { context.resources } returns resource
        // 初始化模拟对象
        every { resource.getString(R.string.summary_agent) } returns "\\n\\n**待办事项**\\n"
        every { resource.getString(R.string.summary_agent_export) } returns "\\n\\n## To Do\\n"
        every { resource.getString(com.soundrecorder.common.R.string.summary_add_agent) } returns " 添加待办"
    }

    @Test
    fun testUpdateAgent_include_agent_char() {
        // 测试逻辑：输入字符串包含 "代理:"
        // 预期结果：返回更新后的字符串，每一行末尾添加 "添加代理"
        val input = "\\n\\n**待办事项**\\n1.任务1\n2.任务2\n3.任务3\n"
        val expectedOutput = "\\n\\n**待办事项**\\n1.任务1 添加待办\n2.任务2 添加待办\n3.任务3 添加待办"
        val result = SummaryContentView(context).updateAgent(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun testUpdateAgent_none_include_agent_char() {
        // 测试逻辑：输入字符串不包含 "代理:" 或 "导出代理:"
        // 预期结果：返回原始字符串
        val input = "任务列表:\n任务1\n任务2"
        val expectedOutput = "任务列表:\n任务1\n任务2"
        val result = SummaryContentView(context).updateAgent(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun testUpdateAgent_last_is_not_enter() {
        // 测试逻辑：输入字符串中代理字符串后面的任务内容有多行
        // 预期结果：返回更新后的字符串，每一行末尾添加 "添加代理"
        val input = "\\n\\n**待办事项**\\n1.任务1\n2.任务2\n3.任务3"
        val expectedOutput = "\\n\\n**待办事项**\\n1.任务1 添加待办\n2.任务2 添加待办\n3.任务3 添加待办"
        val result = SummaryContentView(context).updateAgent(input)
        assertEquals(expectedOutput, result)
    }
}