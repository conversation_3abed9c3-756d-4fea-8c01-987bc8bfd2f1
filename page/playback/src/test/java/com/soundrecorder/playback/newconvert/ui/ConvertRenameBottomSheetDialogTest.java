/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;

import com.coui.appcompat.edittext.COUIEditText;
import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.playback.shadows.ShadowRecorderLogger;

import static org.mockito.Mockito.spy;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowRecorderLogger.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ConvertRenameBottomSheetDialogTest {
    private PlaybackActivity mActivity;
    private PlaybackActivity mSpyActivity;

    @Before
    public void setUp() {
        mActivity = Robolectric.buildActivity(PlaybackActivity.class).get();
        mSpyActivity = spy(mActivity);
    }

    @Test
    public void should_notNull_when_init() {
        ConvertRenameBottomSheetDialog dialog = new ConvertRenameBottomSheetDialog(mSpyActivity);
    }

    @After
    public void tearDown() {
        mActivity = null;
        mSpyActivity = null;
    }
}
