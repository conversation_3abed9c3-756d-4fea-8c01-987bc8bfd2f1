<import name="generate-abstract" src="./components/abstract/index.ux"></import>
<import name="error-page" src="./components/error-page/index.ux"></import>
  
<template>
  <div class="space-between-wrapper {{ isDark ? 'dark-wrapper' : 'light-wrapper' }}">
    <generate-abstract if="!errorState" is-dark="{{ isDark }}"></generate-abstract>
    <error-page else></error-page>
  </div>
</template>

<script>
/**
 * @file 空白模板 - 内容模块
 */
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return{
      errorState: ''
    }
  },
  computed: {
    componentName() {
      // 获取状态，渲染对应卡片
      return 'generate-abstract'
    }
  }
}
</script>

<style lang="less">
.space-between-wrapper {
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
}
</style>
