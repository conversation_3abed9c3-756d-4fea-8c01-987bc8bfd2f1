package com.soundrecorder.wavemark.wave.id3tool;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class ID3v1TagTest {
    private static final String TAG = "TAG";

    @Test
    public void should_returnNotnull_when_toBytes() {
        ID3v1Tag id3v1Tag = new ID3v1Tag();
        Assert.assertNotNull(id3v1Tag.toBytes());
        id3v1Tag.setTrack("Test");
        Assert.assertNotNull(id3v1Tag.getTrack());
        Assert.assertNotNull(id3v1Tag.toBytes());
    }

    @Test
    public void should_returnNotnull_when_hashCode() throws NoSuchTagException {
        byte[] bytes = new byte[128];
        bytes[125] = 0;
        bytes[126] = 0;
        MockedStatic<BufferTools> mocked = Mockito.mockStatic(BufferTools.class);
        mocked.when(() -> BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 0, TAG.length())).thenReturn(TAG);
        ID3v1Tag id3v1Tag = new ID3v1Tag(bytes);
        Assert.assertNotNull(id3v1Tag.hashCode());
        mocked.close();
    }
}