/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackFragmentAction
 * Description:
 * Version: 1.0
 * Date: 2023/1/5
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/1/5 1.0 create
 */

package com.soundrecorder.modulerouter.playback

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object PlaybackFragmentAction {
    const val COMPONENT_NAME = "PlaybackFragmentAction"

    const val ACTION_ONSAVEINSTANCESTATE = "onSaveInstanceState"
    const val ACTION_ONRESTOREINSTANCESTATE = "onRestoreInstanceState"
    const val ACTION_ON_NEWINTENT = "onNewIntent"
    const val ACTION_ON_SET_REQUEST_CODE = "setRequestCodeX"
    const val ACTION_ON_PRIVACY_POLICY_SUCCESS = "onPrivacyPolicySuccess"
    const val ACTION_ON_PERMISSION_GRANTED = "onPermissionGranted"
    const val ACTION_NEW_PLAYBACK_FRAGMENT = "newPlaybackFragment"
    const val ACTION_NEW_PLAYBACK_EMPTY_FRAGMENT = "newPlaybackEmptyFragment"
    const val ACTION_PAUSE_PLAY = "pausePlay"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun newPlaybackFragment(showLoading: Boolean = true): Fragment? {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_NEW_PLAYBACK_FRAGMENT)
                .param(showLoading)
                .build()
            return OStitch.execute<Fragment>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun newPlaybackEmptyFragment(): Fragment? {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_NEW_PLAYBACK_EMPTY_FRAGMENT)
                .build()
            return OStitch.execute<Fragment>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun onPermissionGranted(fragment: Fragment?) {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_ON_PERMISSION_GRANTED)
                .param(fragment).build()
            OStitch.execute<Intent>(apiRequest)
        }
    }

    @JvmStatic
    fun onSaveInstanceState(fragment: Fragment?, outState: Bundle) {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_ONSAVEINSTANCESTATE)
                .param(fragment, outState).build()
            OStitch.execute<Intent>(apiRequest)
        }
    }

    @JvmStatic
    fun onRestoreInstanceState(fragment: Fragment?, saveState: Bundle) {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_ONRESTOREINSTANCESTATE)
                .param(fragment, saveState).build()
            OStitch.execute<Intent>(apiRequest)
        }
    }

    @JvmStatic
    fun onNewIntent(fragment: Fragment?, intent: Intent?) {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_ON_NEWINTENT)
                .param(fragment, intent).build()
            OStitch.execute<Intent>(apiRequest)
        }
    }

    @JvmStatic
    fun setRequestCodeX(fragment: Fragment?, code: Int) {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_ON_SET_REQUEST_CODE)
                .param(fragment, code).build()
            OStitch.execute<Intent>(apiRequest)
        }
    }

    @JvmStatic
    fun onPrivacyPolicySuccess(fragment: Fragment?, type: Int) {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_ON_PRIVACY_POLICY_SUCCESS)
                .param(fragment, type).build()
            OStitch.execute<Intent>(apiRequest)
        }
    }
    @JvmStatic
    fun pausePlay(fragment: Fragment?, clearNotification: Boolean) {
        if (PlaybackAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_PAUSE_PLAY)
                .param(fragment, clearNotification).build()
            OStitch.execute<Intent>(apiRequest)
        }
    }
}