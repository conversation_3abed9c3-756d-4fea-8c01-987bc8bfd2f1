apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'obuildplugin'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility prop_targetCompatibility
        targetCompatibility prop_targetCompatibility
    }
    kotlinOptions {
        jvmTarget = "${prop_targetCompatibility}"
    }
    dataBinding {
        enabled true
    }
    buildFeatures {
        viewBinding true
    }
}
