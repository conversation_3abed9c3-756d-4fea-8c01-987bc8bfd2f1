/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IConvertProcess
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

interface IConvertProcess {

    /**
     * input mediaId : input MediaId corresponding the AudioFile
     * output boolean :
     */
    fun startOrResumeConvert(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): Boolean

    /**
     *
     */
    fun cancelConvert(mediaId: Long): Boolean


    fun releaseConvert(mediaId: Long)


    fun registerCallback(mediaId: Long, callback: IConvertCallback)


    fun unregisterCallback(mediaId: Long, callback: IConvertCallback)
}