/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/2/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter

import androidx.fragment.app.FragmentActivity
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object FeedBackAction {
    const val COMPONENT_NAME = "FeedBack"
    const val LAUNCH_FEED_BACK = "LAUNCH_FEED_BACK"
    const val ACTION_SET_THEME_COLOR = "setThemeColor"

    const val GET_FEED_BACK_REQUEST_DATA = "getFeedbackRequestData"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun getFeedbackRequestData(callback: ((String?) -> Unit)) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_FEED_BACK_REQUEST_DATA)
                    .param(callback).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun launchFeedBack(activity: FragmentActivity, id: String, color: Int) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, LAUNCH_FEED_BACK)
                    .param(activity, id, color).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    /**
     * 设置FeedBack主题色
     */
    @JvmStatic
    fun setFeedbackThemeColor(color: Int) {
        if (mHasComponent) {
            val apiRequest =
                    ApiRequest.Builder(COMPONENT_NAME, ACTION_SET_THEME_COLOR)
                            .param(color).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }
}