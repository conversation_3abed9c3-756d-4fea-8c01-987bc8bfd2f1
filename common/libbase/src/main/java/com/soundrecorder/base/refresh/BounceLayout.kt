/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - BounceLayout.kt
 ** Description: Implement bounce effect
 **
 ** Version: 1.1
 ** Date: 2019-04-30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2019-05-20   1.1         Convert this module into Kotlin
 ********************************************************************************/

package com.soundrecorder.base.refresh

import android.content.Context
import android.util.AttributeSet
import android.view.*
import android.view.animation.PathInterpolator
import android.widget.FrameLayout
import android.widget.Scroller
import com.soundrecorder.base.utils.DebugUtil
import kotlin.math.abs

class BounceLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "BounceLayout"
        const val PULL_REFRESH_DELAY = 100
        const val MSG_REFRESH_DELAY = 300
        private const val VALID_MIN_OFFSET_Y = 5f
        private const val SCROLL_DURATION = 417
    }

    private var mScroller: Scroller? = null
    private var mTouchSlop = 0

    /*Y position of the finger press*/
    private var mYDown = 0f

    /*The y-axis position of the last mobile phone movement is also the last y before the interception event.*/
    private var mYLastMove = 0f

    /*Last moved x value*/
    private var mXDown = 0f

    /*The real-time coordinates of the y-axis when the finger is constantly moving, regardless of any finger, it is always the decision value of the layout movement*/
    private var mYMove = 0f

    /*Record the total offset when multi-touching*/
    private var mTotalOffsetY = 0f

    /*When the movement is in progress, the corresponding finger at this time*/
    private var mCurrentPointer = 0

    /*When the move is in progress, the corresponding y of the multi-finger is processed.*/
    private var mCurrentY = 0f

    /*Finger index change*/
    private var mPointerChange = false

    /*Make sure you can start sliding at any time by pressing*/
    private var mForceDrag = false

    /*Height of layout*/
    private var mHeight = 0f

    /*Damping coefficient*/
    private var mDampingCoefficient = 2.5f

    /*The handler that controls the content pull-up and pull-down can define it by itself*/
    private var mIBounceHandler: IBounceHandler? = null

    /*Rolling child*/
    private var mChildContent: View? = null

    /*Head layout*/
    private var mLoadingView: BaseLoadingView? = null

    private var mIsFooter: Boolean = false

    /*Once the child gets the event, she will not return it to her father.*/
    private var mAlwaysDispatch = false

    /*Whether to suspend rebound*/
    private var mLockBoolean = false

    /*use refresh lock to prevent multiple refresh operations from being called back at the same time*/
    private var mDisallowBounce = false
    private var mActionDownInRefreshing = false
    private var mEnable = true

    /*When the sliding distance exceeds this value, it will enter the refreshing state after letting go*/
    var mDragDistanceThreshold = 0
        set(value) {
            field = value
            mLoadingView?.mDragDistanceThreshold = value
        }

    /*When the absolute value of scrollY of this Layout is greater than mMaxDragDistance, it cannot continue to drag downwards*/
    var mMaxDragDistance = 0

    init {
        mScroller = Scroller(context, PathInterpolator(0.3f, 0f, 0.1f, 1f))
        val configuration = ViewConfiguration.get(context)
        mTouchSlop = configuration.scaledPagingTouchSlop
    }

    /**
     * Use the event dispatch method to process the logic to prevent the child from intercepting the event, causing the child view to never get the event in this touch event, so it is no longer processed in onInterceptTouchEvent.
     *
     * @param ev
     * @return
     */
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        bounceCallBack?.touchEventCallBack(ev)

        if (!mEnable && mTotalOffsetY == 0f) {
            return super.dispatchTouchEvent(ev)
        }

        if (forwardingHelper == null || mIBounceHandler == null || mChildContent == null) {
            //The three are empty and directly believe that they will not be intercepted, resulting in a conflicting place that will not appear to refresh the head
            return super.dispatchTouchEvent(ev)
        }
        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                mAlwaysDispatch = false
                mCurrentPointer = 0
                mYDown = ev.y
                mXDown = ev.x
                mYLastMove = ev.y
                //This is done to initialize mYLastMove
                mCurrentY = mYDown
                mLoadingView?.let {
                    mActionDownInRefreshing = it.isRefreshing
                }
            }
            MotionEvent.ACTION_MOVE -> {
                mYMove = ev.getY(mCurrentPointer)
                if (mPointerChange) {
                    //Change the value before the finger changes, otherwise it will cause jitter
                    mCurrentY = mYMove
                }
                if (abs(scrollY) >= mMaxDragDistance) {
                    if (mIsFooter && mCurrentY >= ev.getY(mCurrentPointer)) {
                        if (mPointerChange) {
                            mPointerChange = false
                        }
                        return true
                    } else if (!mIsFooter && mCurrentY <= ev.getY(mCurrentPointer)) {
                        if (mPointerChange) {
                            mPointerChange = false
                        }
                        return true
                    }
                }
                return if (forwardingHelper!!.notForwarding(mXDown,
                        mYDown,
                        ev.x,
                        ev.y) && !mAlwaysDispatch || mForceDrag) {
                    //notForwarding Do the interception judgment of the first step
                    if (dispatchToChild(ev.getY(mCurrentPointer))) {
                        //dispatch to childView
                        mCurrentY = mYMove
                        mPointerChange = false
                        super.dispatchTouchEvent(ev)
                    } else {
                        moving()
                        mCurrentY = mYMove
                        super.dispatchTouchEvent(ev)
                    }
                } else {
                    //parent dispatch to child
                    mCurrentY = mYMove
                    super.dispatchTouchEvent(ev)
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> run {
                mForceDrag = false
                mAlwaysDispatch = false
                // When you raise your finger, you need to judge whether the footer or header is displayed.
                // (Before the footer and header may be pulled out, you need to refresh or load more.)
                if (mLoadingView != null) {
                    if (mLoadingView!!.checkRefresh()) {
                        if (!mEnable) {
                            setRefreshCompleted()
                        } else if (mDisallowBounce) {
                            if (!mLockBoolean) {
                                bounceCallBack?.startRefresh()
                                mLockBoolean = true
                            }
                        } else {
                            mLoadingView?.releaseToRefresh()
                            if (mIsFooter) {
                                mScroller?.startScroll(0,
                                    scrollY,
                                    0,
                                    -(scrollY - mDragDistanceThreshold),
                                    SCROLL_DURATION)
                            } else {
                                mScroller?.startScroll(0,
                                    scrollY,
                                    0,
                                    -(scrollY + mDragDistanceThreshold),
                                    SCROLL_DURATION)
                            }
                            invalidate()
                        }
                        return@run
                    } else {
                        setRefreshCompleted()
                    }
                }
                mScroller?.startScroll(0, scrollY, 0, -scrollY, SCROLL_DURATION)
                invalidate()
            }
            MotionEvent.ACTION_POINTER_DOWN -> {
                mCurrentPointer = ev.actionIndex
                mCurrentY = ev.getY(mCurrentPointer)
                mYDown = mCurrentY
                mPointerChange = true
            }
            MotionEvent.ACTION_POINTER_UP -> {
                mPointerChange = true
                if (ev.pointerCount == 2) {
                    //The description will only have one finger, the event is subject to the first finger.
                    mCurrentPointer = 0
                    mCurrentY = mYLastMove
                } else {
                    if (mCurrentPointer == ev.actionIndex) {
                        //Leaving the nearest finger, the event is based on the first finger
                        mCurrentPointer = 0
                        mCurrentY = mYLastMove
                    } else {
                        //The event is subject to the last finger
                        mCurrentPointer = ev.pointerCount - 1 - 1
                    }
                }
            }
            else -> {
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (!mEnable) {
            if (mTotalOffsetY == 0f || !mDisallowBounce) {
                return false
            }
        }

        when (ev.action) {
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> return if (mTotalOffsetY != 0f) {
                //Currently in the pull out state
                true
            } else {
                super.onInterceptTouchEvent(ev)
            }
        }
        return super.onInterceptTouchEvent(ev)
    }

    /**
     * Vertically determine if you need to forward the event to your child
     *
     * @return
     */
    private fun dispatchToChild(movingY: Float): Boolean {
        val moveDown = mCurrentY < movingY
        if (scrollY != 0) {
            return false
        }
        if (moveDown && mIBounceHandler!!.canChildDrag(mChildContent!!)) {
            return mIsFooter
        }
        if (!moveDown && mIBounceHandler!!.canChildPull(mChildContent!!)) {
            return !mIsFooter
        }
        if (mCurrentY == movingY) { //change finger
            return false
        }
        return !(mDisallowBounce && mTotalOffsetY != 0f)
    }

    /**
     * The layout really starts moving, not the scrolling of the view
     *
     */
    private fun moving() {
        mForceDrag = true
        var scrollY = mYMove - mCurrentY
        var p = abs(mTotalOffsetY / mHeight)
        if (p == 1f) {
            //Guaranteed that it can never be pulled to a state where the layout is not visible
            p = 1 - Int.MIN_VALUE.toFloat()
        }
        scrollY = if (mIsFooter) {
            if (mActionDownInRefreshing && scrollY > 0) {
                scrollY / (1.0f / (1 - p))
            } else {
                scrollY / (mDampingCoefficient * (1.0f / (1 - p)))
            }
        } else {
            if (mActionDownInRefreshing && scrollY < 0) {
                scrollY / (1.0f / (1 - p))
            } else {
                scrollY / (mDampingCoefficient * (1.0f / (1 - p)))
            }
        }
        val offsetY = mTotalOffsetY + scrollY
        mTotalOffsetY = if (offsetY * mTotalOffsetY < 0) {
            //Critical point
            0f
        } else {
            offsetY.coerceAtMost(mMaxDragDistance.toFloat())
        }
        if (!mDisallowBounce) {
            //Not allowed to pull
            scrollTo(0, (-mTotalOffsetY).toInt())
        }
        mPointerChange = false
        //Be sure to pass the pull value to the header when the layout is pulled.
        mLoadingView?.handleDrag(mTotalOffsetY)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        isClickable = true
        for (i in 0 until childCount) {
            if (getChildAt(i) != null && !getChildAt(i).isClickable) {
                getChildAt(i).isClickable = true
            }
        }
        mHeight = h.toFloat()
    }

    /**
     * The loaded rebound cannot be blocked
     */
    override fun computeScroll() {
        if (mForceDrag) {
            return
        }
        if (mScroller!!.computeScrollOffset()) {
            val lastOffsetY = mTotalOffsetY
            mTotalOffsetY = -mScroller!!.currY.toFloat()
            if ((lastOffsetY > VALID_MIN_OFFSET_Y) && (mTotalOffsetY == 0f)) {
                mTotalOffsetY = lastOffsetY
            }
            scrollTo(0, mScroller!!.currY)
            invalidate()
            if (mLoadingView != null) {
                mLoadingView!!.handleDrag(mTotalOffsetY)
                if (mLoadingView!!.doRefresh()) {
                    //Refresh the header and start to officially refresh
                    if (!mLockBoolean) {
                        bounceCallBack?.startRefresh()
                        mForceDrag = true
                        mLockBoolean = true
                    }
                }
            }
        }
    }

    fun setBounceHandler(IBounceHandler: IBounceHandler?, v: View?) {
        mIBounceHandler = IBounceHandler
        mChildContent = v
    }

    fun setEventForwardingHelper(forwardingHelper: EventForwardingHelper?) {
        this.forwardingHelper = forwardingHelper
    }

    private var forwardingHelper: EventForwardingHelper? = null
    fun setHeaderView(loadingView: BaseLoadingView?, parent: ViewGroup?) {
        mLoadingView = loadingView
        if (loadingView != null) {
            loadingView.setParent(parent)
            if (mDisallowBounce) {
                loadingView.setCanTranslation(false)
            }
        }
    }

    fun setFooterView(loadingView: BaseLoadingView?, parent: ViewGroup?) {
        mLoadingView = loadingView
        setIsFooter(true)
        if (loadingView != null) {
            loadingView.setParent(parent)
            if (mDisallowBounce) {
                loadingView.setCanTranslation(false)
            }
        }
    }

    fun setIsFooter(isFooter: Boolean) {
        mIsFooter = isFooter
    }

    private var bounceCallBack: BounceCallBack? = null
    fun setBounceCallBack(bounceCallBack: BounceCallBack?): BounceCallBack? {
        this.bounceCallBack = bounceCallBack
        return this.bounceCallBack
    }

    fun setRefreshCompleted() {
        mLoadingView?.refreshCompleted()
        mLockBoolean = false
        mForceDrag = false
        if (mDisallowBounce) {
            mTotalOffsetY = 0f
            mLoadingView?.handleDrag(0f)
        } else {
            mLoadingView?.post {
                mScroller?.startScroll(0, scrollY, 0, -scrollY, 500)
                invalidate()
                bounceCallBack?.refreshCompleted()
            }
        }
    }

    fun setmDampingCoefficient(mDampingCoefficient: Float) {
        this.mDampingCoefficient = mDampingCoefficient
    }

    fun setmDisallowBounce(mDisallowBounce: Boolean) {
        this.mDisallowBounce = mDisallowBounce
    }

    fun setRefreshEnable(enable: Boolean) {
        DebugUtil.i(TAG, " setRefreshEnable $enable")
        this.mEnable = enable
        mLoadingView?.setRefreshEnable(mEnable)
    }

    fun isRefreshing(): Boolean {
        return mLoadingView?.isRefreshing ?: false
    }
}