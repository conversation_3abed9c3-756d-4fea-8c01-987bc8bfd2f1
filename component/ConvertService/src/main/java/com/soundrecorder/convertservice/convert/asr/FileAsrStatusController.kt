/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FileAsrUiHelper
 * Description:
 * Version: 1.0
 * Date: 2025/4/7
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/4/7 1.0 create
 */

package com.soundrecorder.convertservice.convert.asr

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.common.databean.Record
import com.soundrecorder.convertservice.convert.IConvertCallback
import com.soundrecorder.convertservice.convert.IConvertTextRunnableProgress

class FileAsrStatusController(
    val mediaId: Long,
    var convertUiCallback: IConvertCallback?,
    var runCallback: IConvertTextRunnableProgress?
) {
    companion object {
        private const val MSG_PROGRESS = 0X1
        private const val MSG_HEART_BEAT = 0X2
        private const val MSG_GENERATE_TIMEOUT = 0X3
        private const val ASR_TASK_OVER_TIME = 8000L // 心跳6s
        private const val TAG = "FileAsrStatusController"
    }

    private var progressValue = 0
    private var generateTimeout = -1L

    /*progress小于这个值，会隔一秒自增*/
    private var autoIncreaseMaxProgress = 0
    var timeOutListener: (() -> Unit)? = null

    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_PROGRESS -> handelUiProgress()
                MSG_HEART_BEAT -> onAsrTimeout(false)
                MSG_GENERATE_TIMEOUT -> onAsrTimeout(true)
                else -> DebugUtil.w(TAG, "handleMessage not support ${msg.what}")
            }
        }
    }

    private fun handelUiProgress() {
        convertUiCallback?.onConvertProgressChanged(mediaId, -1, progressValue, ConvertDbUtil.SERVER_PLAN_ASR)
        if (progressValue < autoIncreaseMaxProgress) {
            progressValue++
        }
        handler.removeMessages(MSG_PROGRESS)
        handler.sendEmptyMessageDelayed(MSG_PROGRESS, NumberConstant.NUM_1000.toLong())
    }

    private fun scheduleAsrConvertTimeout() {
        if (generateTimeout < 0) return
        DebugUtil.i(TAG, "scheduleAsrConvertTimeout")
        handler.removeMessages(MSG_GENERATE_TIMEOUT)
        handler.sendEmptyMessageDelayed(MSG_GENERATE_TIMEOUT, generateTimeout)
    }

    private fun cancelAsrConvertTimeout() {
        generateTimeout = -1L
        handler.removeMessages(MSG_GENERATE_TIMEOUT)
    }

    /**
     * 开始asr，ui的进度条从3-》20每隔一秒+1
     */
    fun onAsrStart() {
        onConvertStatusChange(ConvertStatus.UPLOAD_STATUS_UPLOADING)
        handler.removeMessages(MSG_PROGRESS)
        cancelAsrConvertTimeout()
        progressValue = NumberConstant.NUM_3
        autoIncreaseMaxProgress = NumberConstant.NUM_20
        handler.sendEmptyMessage(MSG_PROGRESS)
        onHeartBeat()
    }

    fun onSpecifyRecord(record: Record) {
        generateTimeout = AsrTimeoutStrategy.calculateAsrConvertTimeout(record.duration)
        DebugUtil.i(TAG, "onSpecifyRecord: generateTimeout=$generateTimeout")
    }

    /**
     * asr文件上传成功，ui进度条从20-90，每隔一秒—+1
     */
    fun onAsrFileUploadSuccess() {
        DebugUtil.i(TAG, "onAsrFileUploadSuccess")
        scheduleAsrConvertTimeout()
        onConvertStatusChange(ConvertStatus.UPLOAD_STATUS_UPLOAD_SUC)
        progressValue = NumberConstant.NUM_20
        autoIncreaseMaxProgress = NumberConstant.NUM_90
        handelUiProgress()
    }

    /**
     * 收到asr结果，ui进度条 100
     */
    fun onAsrResultSuccess(mediaId: Long, convertAiTitle: Boolean, showSwitch: Boolean, convertText: BeanConvertText) {
        DebugUtil.i(TAG, "onAsrResultSuccess")
        cancelAsrConvertTimeout()
        convertUiCallback?.onConvertTextReceived(mediaId, ConvertSupportManager.CONVERT_AI_CONVERT, convertText, showSwitch, convertAiTitle)
        handler.removeMessages(MSG_PROGRESS)
        progressValue = NumberConstant.NUM_100
        handelUiProgress()
        runCallback?.postConvertEnd(mediaId, convertAiTitle)
    }

    /**
     * 取消asr
     */
    fun onAsrCancel(convertAiTitle: Boolean) {
        DebugUtil.i(TAG, "onAsrCancel")
        cancelAsrConvertTimeout()
        handler.removeCallbacksAndMessages(null)
        runCallback?.postCancelConvert(mediaId, convertAiTitle)
        onConvertStatusChange(ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC)
    }

    fun onConvertStatusChange(convertStatus: Int) {
        DebugUtil.i(TAG, "onConvertStatusChange,convertStatus=$convertStatus")
        convertUiCallback?.onConvertStatusChange(mediaId, ConvertStatus(convertStatus = convertStatus), convertStatus, convertAiTitle = true)
    }

    /**
     * asr 超时
     */
    private fun onAsrTimeout(isGenerateTimeout: Boolean) {
        DebugUtil.i(TAG, if (isGenerateTimeout) "onAsrGenerateTimeout" else "onAsrHeartBeatTimeout")
        cancelAsrConvertTimeout()
        handler.removeMessages(MSG_PROGRESS)
        handler.removeMessages(MSG_HEART_BEAT)
        onConvertStatusChange(ConvertStatus.CONVERT_STATUS_QUERY_TASK_TIMEOUT)
        timeOutListener?.invoke()
    }

    fun onHeartBeat() {
        handler.removeMessages(MSG_HEART_BEAT)
        handler.sendEmptyMessageDelayed(MSG_HEART_BEAT, ASR_TASK_OVER_TIME)
    }

    /**
     * asr 流程结束（不管 正常 or 非正常）
     */
    fun onProcessEnd(convertAiTitle: Boolean) {
        cancelAsrConvertTimeout()
        handler.removeCallbacksAndMessages(null)
        convertUiCallback?.onConvertEnd(mediaId)
        runCallback?.postConvertEnd(mediaId, convertAiTitle)
        convertUiCallback = null
    }
}