/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AudioModeChangeManager
 Description:
 Version: 1.0
 Date: 2022/10/9
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/10/9 1.0 create
 */
package com.soundrecorder.recorderservice.manager

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.media.AudioRecordingConfiguration
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.recorderservice.manager.listener.AudioModeChangeListener
import com.soundrecorder.recorderservice.manager.listener.AudioRecordingChangeListener

object AudioModeChangeManager {

    private const val TAG = "AudioModeOperator"
    private var mAudioManager: AudioManager? = null
    private var mCurrentMode = -1
    private var mLastMode = -1
    private var onModeChangedListener: AudioManager.OnModeChangedListener? = null
    private var mAudioModeChangeListener: AudioModeChangeListener? = null

    //通话行为录音暂停
    private var mIsAudioChangePause = false

    //挂断通话后是否自动继续录制
    private var mIsNeedResume = false
    private val mContext
        get() = BaseApplication.getAppContext()
    private var mHandler: Handler? = null

    //暂停时的通知栏录制按钮状态
    private var mLastBtnDisabled = false

    /*监听录制收音设备变化*/
    private var audioRecordingCallback: AudioManager.AudioRecordingCallback? = null

    /*是否为手机内置麦克风*/
    @Volatile
    private var isAudioMicType: Boolean? = null
    var audioSourceChangeListener: AudioRecordingChangeListener? = null
    private var headsetPlugReceiver: BroadcastReceiver? = null

    init {
        mAudioManager = BaseApplication.getAppContext().getSystemService(Context.AUDIO_SERVICE) as? AudioManager
    }

    @SuppressLint("NewApi")
    fun audioManagerModeChange() {
        if (BaseUtil.isAndroidSOrLater) {
            if (onModeChangedListener != null) {
                DebugUtil.w(TAG, "audioManagerModeChange return by listener not null")
                return
            }
            val executor = mContext.mainExecutor
            onModeChangedListener = AudioManager.OnModeChangedListener { mode ->
                DebugUtil.w(TAG, "audioManagerModeChange mode= $mode")
                dealMode(mode)
            }
            mAudioManager?.addOnModeChangedListener(executor, onModeChangedListener!!)
        } /*else {
            val executor = BaseApplication.getAppContext().mainExecutor
            recordExpandController?.getMediaRecorder()?.registerAudioRecordingCallback(
                executor,
                object : AudioManager.AudioRecordingCallback() {
                    override fun onRecordingConfigChanged(configs: MutableList<AudioRecordingConfiguration>?) {
                        val mode = mAudioManager?.mode ?: -1
                        DebugUtil.e(TAG, "R mode = $mode")
                        dealMode(mode)
                    }
                })
        }*/
    }

    /**
     * 初始化默认音源以及音源变化监听
     */
    fun initAudioSourceType() {
        if (audioRecordingCallback != null) {
            DebugUtil.w(TAG, "initAudioSourceType return by callback not null")
            return
        }
        audioRecordingCallback = object : AudioManager.AudioRecordingCallback() {
            override fun onRecordingConfigChanged(configs: MutableList<AudioRecordingConfiguration>) {
                DebugUtil.d(TAG, "onRecordingConfigChanged")
                handleRecordingConfigChange(configs)
            }
        }
        if (mHandler == null) {
            mHandler = Handler(HandlerThread("AudioModeChange").apply {
                start()
            }.looper)
        }
        mAudioManager?.registerAudioRecordingCallback(audioRecordingCallback!!, mHandler)
        registerHeadSetPlugReceiver()
    }

    private fun registerHeadSetPlugReceiver() {
        if (headsetPlugReceiver == null) {
            headsetPlugReceiver = object : BroadcastReceiver() {
                override fun onReceive(p0: Context?, p1: Intent?) {
                    DebugUtil.d(TAG, "onReceive ACTION_HEADSET_PLUG")
                    handleRecordingConfigChange(mAudioManager?.activeRecordingConfigurations)
                }
            }
            val filter = IntentFilter()
            filter.addAction(Intent.ACTION_HEADSET_PLUG)
            mContext.registerReceiver(headsetPlugReceiver, filter)
        }
    }

    private fun handleRecordingConfigChange(configs: MutableList<AudioRecordingConfiguration>?) {
        val isHeadsetOn = isWiredHeadsetOn()
        val isMic = isPhoneMicSource(configs, isHeadsetOn, !isHeadsetOn)
        DebugUtil.i(TAG, "handleRecordingConfigChange,isMic=$isMic, isHeadsetOn=$isHeadsetOn},oldValue=$isAudioMicType")
        if (isMic != isAudioMicType) {
            isAudioMicType = isMic
            audioSourceChangeListener?.onAudioRecordingSourceChange(isAudioMicType ?: false)
        }
    }

    fun isPhoneMic(updateValue: Boolean = true): Boolean {
        val isWiredHeadsetOn = isWiredHeadsetOn()
        return isAudioMicType ?: isPhoneMicSource(mAudioManager?.activeRecordingConfigurations, isWiredHeadsetOn, !isWiredHeadsetOn).apply {
            if (updateValue && isAudioMicType != this) {
                DebugUtil.i(TAG, "isPhoneMic,isMic=$this")
                isAudioMicType = this
            }
        }
    }

    private fun isWiredHeadsetOn(): Boolean {
        return mAudioManager?.isWiredHeadsetOn == true
    }

    /**
     * 判断音频来源是否为手机MIC
     * @param configs
     * @param defaultWhenEmpty 若config为空，默认返回值，这里由于OplusRecorder暂停是通过stop实现，暂停后会导致config为空，从而mic值错误
     */
    private fun isPhoneMicSource(configs: MutableList<AudioRecordingConfiguration>?, isWiredHeadsetOn: Boolean, defaultWhenEmpty: Boolean): Boolean {
        if (configs.isNullOrEmpty()) {
            DebugUtil.w(TAG, "recordingConfig is empty ,defaultWhenEmpty=$defaultWhenEmpty")
            return defaultWhenEmpty
        }
        for (i in configs.indices) {
            if (configs[i].audioDevice?.type == AudioDeviceInfo.TYPE_BUILTIN_MIC) {
                return true
            }
        }
        // 修复bug: ID:8961897 标题:【阻塞压测】【24813】【TC1】【定向录音】开启系统录音，点击录音开启定向录音，插入数字耳机，再拔出数字耳机，不能再次点击使用定向录音
        if (!isWiredHeadsetOn) {
            return true
        }
        return false
    }

    /**
     * 检查当前是否处于通话状态
     * needToast == true 需要提示用户,默认
     * needToast == false 不需要提示用户
     */
    @JvmStatic
    fun checkModeCanRecord(needToast: Boolean = true): Boolean {
        if ((mAudioManager?.mode == AudioManager.MODE_IN_CALL)
            || (mAudioManager?.mode == AudioManager.MODE_IN_COMMUNICATION)) {
            if (needToast) {
                ToastManager.showShortToast(mContext, com.soundrecorder.common.R.string.in_call_not_record)
            }
            return false
        }
        return true
    }

    fun registerChangeListener(audioModeChangeListener: AudioModeChangeListener?) {
        mAudioModeChangeListener = audioModeChangeListener
    }

    private fun unRegisterChangeListener() {
        mAudioModeChangeListener = null
    }

    fun changeAudioPause(newAudioPause: Boolean) {
        mIsAudioChangePause = newAudioPause
    }

    @JvmStatic
    fun isAudioModeChangePause(): Boolean {
        return mIsAudioChangePause
    }

    fun changeNeedResume(isNeed: Boolean) {
        mIsNeedResume = isNeed
    }

    fun isNeedResume(): Boolean {
        return mIsNeedResume
    }

    fun changeLastBtnDisabled(lastBtnDisabled: Boolean) {
        mLastBtnDisabled = lastBtnDisabled
    }

    fun getLastBtnDisabled(): Boolean {
        return mLastBtnDisabled
    }

    fun release() {
        unRegisterChangeListener()
        releaseAudioManagerCallBack()
        mIsAudioChangePause = false
        mIsNeedResume = false
        mCurrentMode = -1
        mLastMode = -1
        mLastBtnDisabled = false
        isAudioMicType = null
    }

    private fun releaseAudioManagerCallBack() {
        audioRecordingCallback?.let {
            mAudioManager?.unregisterAudioRecordingCallback(it)
        }
        onModeChangedListener?.let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                mAudioManager?.removeOnModeChangedListener(it)
            }
        }
        headsetPlugReceiver?.let {
            mContext.unregisterReceiver(it)
        }
        headsetPlugReceiver = null
        audioRecordingCallback = null
        onModeChangedListener = null
        audioSourceChangeListener = null
    }

    /**
     * mCurrentMode,mLastMode
     * 去重逻辑
     */
    private fun dealMode(mode: Int) {
        mCurrentMode = mode
        if (mCurrentMode != mLastMode) {
            mAudioModeChangeListener?.onAudioModeChanged(mCurrentMode)
            mLastMode = mCurrentMode
        }
    }
}