package com.soundrecorder.wavemark.wave.view;

import static com.soundrecorder.common.utils.MarkSerializUtil.VERSION_NEW;
import static org.mockito.Mockito.mock;

import android.app.Activity;
import android.graphics.Paint;
import android.os.Build;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.RectF;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.utils.FunctionOption;
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;
import com.soundrecorder.wavemark.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.wavemark.wave.WaveViewUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.LooperMode;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class})
@LooperMode(LooperMode.Mode.PAUSED)
@PrepareForTest({WaveRecyclerView.class, Activity.class})
public class WaveRecyclerViewTest {
    private Context mContext;
    private ActivityController<Activity> mController;
    private Activity mActivity;
    private WaveRecyclerView mRecyclerView;
    private final String TAG = "WaveRecyclerViewTest";

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mController = Robolectric.buildActivity(Activity.class);
        mRecyclerView = Mockito.spy(new WaveRecyclerView<WaveItemView>(mContext, null) {
            @Override
            public void onBindItemView(@NonNull WaveItemView rulerView, int position) {

            }

            @Override
            public void onItemViewCreated(@NonNull ViewGroup parent, @NonNull WaveItemView rulerView) {

            }

            @Override
            public int fixItemCount(int totalCount) {
                return 0;
            }

            @NonNull
            @Override
            public WaveItemView createNewItemView(@NonNull Context context, @NonNull ViewGroup parent) {
                return new WaveItemView(context);
            }
        });
    }

    @After
    public void tearDown() {
        mController = null;
        mRecyclerView = null;
        mContext = null;
    }

    @Test
    public void verify_value_when_initSlop() throws Exception {
        Whitebox.invokeMethod(mRecyclerView, "initSlop", mContext);
        Assert.assertNotEquals(0, (int) Whitebox.getInternalState(mRecyclerView, "mSlop"));
    }

    @Test
    public void verify_value_when_init() {
        try {
            Whitebox.invokeMethod(mRecyclerView, "init", mContext);
            Assert.assertNotEquals(0F, Whitebox.getInternalState(mRecyclerView, "mPxPerMs"));

            Whitebox.invokeMethod(mRecyclerView, "setMaxRecycledViews", 0, 0, WaveAdapter.NORMAL_ITEM);
            Whitebox.invokeMethod(mRecyclerView, "setMaxRecycledViews", 1080, 1080 / 6, WaveAdapter.NORMAL_ITEM);

            mRecyclerView.stopScroll();
        } catch (Exception e) {
            Log.e(TAG, "verify_value_when_init: " + e.getMessage());
        }
    }


    @Test
    public void verify_value_when_initPaint() throws Exception {
        Whitebox.invokeMethod(mRecyclerView, "initPaint");
        Assert.assertNotNull(Whitebox.getInternalState(mRecyclerView, "mCenterLinePaint"));
    }

    @Test
    public void verify_value_when_initAdapter() throws Exception {
        Whitebox.invokeMethod(mRecyclerView, "initAdapter", mContext);
        Assert.assertNotNull(Whitebox.getInternalState(mRecyclerView, "mWaveAdapter"));
    }

    @Test
    public void verify_value_when_checkMarkRegion() throws Exception {
        Whitebox.setInternalState(mRecyclerView, "mParent", new LinearLayout(mContext));
        Whitebox.setInternalState(mRecyclerView, "mMarkViewHeight", 200);
        MotionEvent event = MotionEvent.obtain(System.currentTimeMillis(), 100, MotionEvent.ACTION_DOWN, 100f, 100f, 0);
        Whitebox.invokeMethod(mRecyclerView, "checkMarkRegion", event);
        Assert.assertTrue(Whitebox.getInternalState(mRecyclerView, "mIsTouchingMarkView"));
    }

    @Test
    public void verify_value_when_setDragListener() {
        mRecyclerView.setDragListener(() -> {

        });
        Assert.assertNotNull(Whitebox.getInternalState(mRecyclerView, "mDragListener"));
    }

    @Test
    public void verify_value_when_setIsCanScrollTimeRuler() {
        mRecyclerView.setIsCanScrollTimeRuler(false);
        Assert.assertFalse(Whitebox.getInternalState(mRecyclerView, "mCanScrollHorizontally"));
    }

    @Test
    public void verify_value_when_setMaxAmplitudeSource() {
        mRecyclerView.setMaxAmplitudeSource(new MaxAmplitudeSource() {
            @Override
            public int getMaxAmplitude() {
                return 0;
            }

            @Override
            public long getTime() {
                return 0;
            }

            @Override
            public int getRecorderState() {
                return 0;
            }
        });
        Assert.assertNotNull(Whitebox.getInternalState(mRecyclerView, "mMaxAmplitudeSource"));
    }

    @Test
    public void verify_value_when_setTotalTime() {
        mRecyclerView.setTotalTime(5000L);
        Assert.assertEquals(5000L, mRecyclerView.getTotalTime());
    }

    @Test
    public void verify_value_when_getDiffMoreThanLastItemRealWidth() {
        mRecyclerView.setLayoutManager(new WaveLinearLayoutManager(mContext));
        Assert.assertEquals(0, mRecyclerView.getDiffMoreThanLastItemRealWidth());
        mRecyclerView.setTotalTime(5000L);
        Whitebox.setInternalState(mRecyclerView, "mPxPerMs", 10);
        Assert.assertTrue(mRecyclerView.getDiffMoreThanLastItemRealWidth() > 0);
    }

    @Test
    public void verify_value_when_updateCenterLinePosition() throws Exception {
        mRecyclerView.setTotalTime(5000L);
        Whitebox.setInternalState(mRecyclerView, "mCenterPointDuration", 2000);

        Whitebox.invokeMethod(mRecyclerView, "updateCenterLinePosition", 1000L);

        Whitebox.invokeMethod(mRecyclerView, "updateCenterLinePosition", 2000L);

        Whitebox.invokeMethod(mRecyclerView, "updateCenterLinePosition", 3000L);
        //模拟 (currentTimeMillis - mCenterPointDuration) %  WaveViewUtil.ONE_WAVE_VIEW_DURATION == 0的情况
        Whitebox.invokeMethod(mRecyclerView, "updateCenterLinePosition", WaveViewUtil.ONE_WAVE_VIEW_DURATION + 2000 - 500);

        Whitebox.invokeMethod(mRecyclerView, "updateCenterLinePosition", 5000L);
    }

    @Test
    @Ignore
    public void verify_value_when_startSmoothScroll() {
        mActivity = mController.create().get();
        WaveRecyclerView<WaveItemView> recyclerView = Whitebox.getInternalState(mActivity, "mWaveRecyclerView");
        recyclerView.setTotalTime(5000L);
        WaveLinearLayoutManager layoutManager = mock(WaveLinearLayoutManager.class);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setSelectTime(1000);
        try {
            Whitebox.invokeMethod(recyclerView, "startSmoothScroll");

            Whitebox.setInternalState(recyclerView, "mCurrentTimeMillis", 0);
            Whitebox.setInternalState(recyclerView, "mScrollTotalLength", 0);
            Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenReturn(0);
            Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(new View(mContext));

            Mockito.when(layoutManager.getReverseLayout()).thenReturn(true);
            Whitebox.invokeMethod(recyclerView, "startSmoothScroll");

            Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenThrow(new RuntimeException());
            Whitebox.invokeMethod(recyclerView, "startSmoothScroll");
        } catch (Exception e) {
//            e.printStackTrace();
        }
    }

    @Test
    public void verify_value_when_isReverseLayout() throws Exception {
        Assert.assertFalse(Whitebox.invokeMethod(mRecyclerView, "isReverseLayout"));

        mRecyclerView.setLayoutManager(new WaveLinearLayoutManager(mContext));
        Assert.assertFalse(Whitebox.invokeMethod(mRecyclerView, "isReverseLayout"));
    }

    @Test
    public void verify_value_when_onScrolled() throws Exception {
        WaveRecyclerView recyclerView = Mockito.mock(WaveRecyclerView.class);;
        WaveLinearLayoutManager layoutManager = mock(WaveLinearLayoutManager.class);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.onScrolled(100, 0);
        Whitebox.setInternalState(recyclerView, "mMsPerPx", 1);
        View mockedView = new WaveItemView(mContext);
        mockedView.setLeft(100);
        Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenReturn(0);
        Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(mockedView);
        int scrollLength = Whitebox.invokeMethod(recyclerView, "getTotalScrolledLength");
        Assert.assertEquals(0, scrollLength);
    }

    @Test
    public void verify_value_when_drawCenterLine() throws Exception {
        WaveRecyclerView waveRecyclerView = Mockito.mock(WaveRecyclerView.class);
        Canvas canvas = new Canvas();

        Whitebox.setInternalState(waveRecyclerView, "mCenterLinePaint", new Paint());
        Whitebox.invokeMethod(waveRecyclerView, "drawCenterLine", canvas);
    }

    @Test
    public void verify_value_when_getAmplitudes() {
        WaveRecyclerView waveRecyclerView = Mockito.mock(WaveRecyclerView.class);
        Assert.assertEquals(0, waveRecyclerView.getAmplitudes().size());
    }

    @Test
    public void verify_value_when_getSlideTime() {
        WaveLinearLayoutManager layoutManager = Mockito.spy(new WaveLinearLayoutManager(mContext));
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setMaxAmplitudeSource(new MaxAmplitudeSource() {
            @Override
            public int getMaxAmplitude() {
                return 0;
            }

            @Override
            public long getTime() {
                return 0;
            }

            @Override
            public int getRecorderState() {
                return 0;
            }
        });

        mRecyclerView.setAdapter(new WaveAdapter(mContext, new IWaveItemViewDelegate() {
            @NonNull
            @Override
            public WaveItemView createNewItemView(@NonNull Context context, @NonNull ViewGroup parent) {
                return null;
            }

            @Override
            public int fixItemCount(int totalCount) {
                return 0;
            }

            @Override
            public void onItemViewCreated(@NonNull ViewGroup parent, @NonNull WaveItemView rulerView) {

            }

            @Override
            public void onBindItemView(@NonNull WaveItemView rulerView, int position) {

            }
        }));
        mRecyclerView.setSelectTime(1000);
        mRecyclerView.setTotalTime(50000);

        Mockito.when(mRecyclerView.isShown()).thenReturn(true);
        Mockito.when(layoutManager.getReverseLayout()).thenReturn(true);

        Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenReturn(0);
        Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(null);
        Assert.assertEquals(-1, mRecyclerView.getSlideTime("test"));

        Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenReturn(0);
        Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(new View(mContext));
        Assert.assertEquals(-1, mRecyclerView.getSlideTime("test"));

        Mockito.when(layoutManager.getReverseLayout()).thenReturn(false);

        Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenReturn(0);
        Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(null);
        Assert.assertEquals(-1, mRecyclerView.getSlideTime("test"));

        Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenReturn(0);
        Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(new View(mContext));
        Assert.assertEquals(-1, mRecyclerView.getSlideTime("test"));
    }

    @Test
    public void verify_value_when_outsideEvent() {
        WaveRecyclerView waveRecyclerView = Mockito.mock(WaveRecyclerView.class);
        WaveLinearLayoutManager layoutManager = Mockito.spy(new WaveLinearLayoutManager(mContext));
        waveRecyclerView.setLayoutManager(layoutManager);
        WaveItemView waveItemView = new WaveItemView(mContext);
        ArrayList<MarkDataBean> arrayList = new ArrayList<>();


        waveRecyclerView.setAdapter(new WaveAdapter(mContext, new IWaveItemViewDelegate() {
            @NonNull
            @Override
            public WaveItemView createNewItemView(@NonNull Context context, @NonNull ViewGroup parent) {
                return null;
            }

            @Override
            public int fixItemCount(int totalCount) {
                return 0;
            }

            @Override
            public void onItemViewCreated(@NonNull ViewGroup parent, @NonNull WaveItemView rulerView) {

            }

            @Override
            public void onBindItemView(@NonNull WaveItemView rulerView, int position) {

            }
        }));
        MotionEvent motionEvent = MotionEvent.obtain(System.currentTimeMillis(), 200, MotionEvent.ACTION_DOWN, 100, 100, 0, 0, 0, 0, 0, 0, 0);

        MockedStatic<FunctionOption> featureOptionMockedStatic = Mockito.mockStatic(FunctionOption.class);
        waveRecyclerView.outsideEvent(motionEvent);

        motionEvent.setAction(MotionEvent.ACTION_UP);
        featureOptionMockedStatic.when(FunctionOption::isSupportPhotoMark).thenReturn(true);
        waveRecyclerView.outsideEvent(motionEvent);
        Mockito.when(layoutManager.findFirstVisibleItemPosition()).thenReturn(0);
        Mockito.when(layoutManager.findLastVisibleItemPosition()).thenReturn(0);
        Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(null);
        waveRecyclerView.outsideEvent(motionEvent);

        //markTimeList.size() == 0
        Mockito.when(layoutManager.findViewByPosition(Mockito.anyInt())).thenReturn(waveItemView);
        waveRecyclerView.outsideEvent(motionEvent);

        MarkDataBean markDataBean = new MarkDataBean(1000, VERSION_NEW);
        markDataBean.setPictureFilePath("adadasd/dasdas/dad");
        arrayList.add(markDataBean);
        markDataBean.setRect(new RectF(0f, 0f, 1080f, 200f));
        waveItemView.setMarkTimeList(arrayList);
        waveRecyclerView.outsideEvent(motionEvent);

        featureOptionMockedStatic.reset();
        featureOptionMockedStatic.close();
    }
}