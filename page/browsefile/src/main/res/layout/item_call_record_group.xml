<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <import type="android.view.View" />

        <variable
            name="itemGroupParent"
            type="com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/item_round_rect_bg">

        <LinearLayout
            android:id="@+id/ll_call_content"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp40"
            android:paddingStart="@dimen/dp16"
            android:paddingEnd="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.soundrecorder.common.widget.CircleTextImage
                android:id="@+id/civ_call_image"
                android:layout_width="@dimen/dp20"
                android:layout_height="@dimen/dp20"
                app:circle_text_color="@color/White"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginBottom="@dimen/dp8"
                app:circle_text_size="@dimen/dp12"
                app:sub_first_character="true"
                app:use_random_color="true"/>

            <TextView
                android:id="@+id/tv_call_name"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginBottom="@dimen/dp8"
                android:layout_marginStart="@dimen/dp8"
                android:contentDescription="@{itemGroupParent.callerName}"
                android:text="@{itemGroupParent.callerName}"
                android:textColor="@color/coui_color_label_secondary"
                android:textSize="@dimen/sp12"
                android:maxLines="1"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium" />

            <LinearLayout
                android:id="@+id/ll_call_group_more"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="@dimen/dp12"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_call_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15"
                    android:textColor="@color/coui_color_label_secondary"
                    android:textSize="@dimen/sp12"
                    android:fontFamily="sans-serif-medium"/>

                <ImageView
                    android:id="@+id/iv_call_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/coui_btn_next"
                    android:clickable="false"
                    android:layout_marginStart="@dimen/support_preference_image_margin_start"
                    android:focusable="false"
                    android:importantForAccessibility="no"/>

            </LinearLayout>
        </LinearLayout>

        <androidx.recyclerview.widget.COUIRecyclerView
            android:id="@+id/rv_call_child_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_call_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
