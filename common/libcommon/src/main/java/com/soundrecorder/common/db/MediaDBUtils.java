package com.soundrecorder.common.db;

import static com.soundrecorder.base.utils.TimeUtils.TIME_ONE_SECOND;
import static com.soundrecorder.common.constant.Constants.CALL_RECORDINGS;
import static com.soundrecorder.common.constant.Constants.INTERVIEW_RECORDINGS;
import static com.soundrecorder.common.constant.Constants.MEETING_RECORDINGS;
import static com.soundrecorder.common.constant.Constants.PAGE_SIZE;
import static com.soundrecorder.common.constant.Constants.RECORDINGS;
import static com.soundrecorder.common.constant.Constants.STANDARD_RECORDINGS;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_AMR_WB;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_RAW;
import static com.soundrecorder.common.constant.RecordConstant.POSTFIX_POINT_AMR;

import android.app.RecoverableSecurityException;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.ParcelFileDescriptor;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.RequiresApi;
import androidx.annotation.VisibleForTesting;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.MediaDataScanner;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.utils.FileDealUtil;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileDescriptor;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MediaDBUtils {
    public static final Uri BASE_URI = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
    private static final String TAG = "MediaUtils";

    public static int rename(Uri uriWithId, String title, String suffix, String mimeType) throws RecoverableSecurityException {
        if (uriWithId == null || TextUtils.isEmpty(title) || TextUtils.isEmpty(suffix)) {
            DebugUtil.i(TAG, "one of param is null");
            return -1;
        }
        ContentValues values = new ContentValues();
        values.put(MediaStore.Audio.Media.TITLE, title);
        values.put(MediaStore.Audio.Media.DISPLAY_NAME, title + suffix);
        values.put(MediaStore.Audio.Media.MIME_TYPE, mimeType);
        int renameResult = renameInternal(uriWithId, values);
        DebugUtil.d(TAG, "rename result:" + renameResult + " ,from manual mimeType = " + mimeType);
        return renameResult;
    }

    public static int rename(Uri uriWithId, Record recordCloud) throws RecoverableSecurityException {
        if (uriWithId == null) {
            DebugUtil.e(TAG, "rename uriWithId is null");
            return -1;
        }
        ContentValues values = new ContentValues();
        values.put(MediaStore.Audio.Media.TITLE, getTitleByName(recordCloud.getDisplayName()));
        values.put(MediaStore.Audio.Media.DISPLAY_NAME, recordCloud.getDisplayName());
        values.put(MediaStore.Audio.Media.RELATIVE_PATH, recordCloud.getRelativePath());
        String mimeType = recordCloud.getMimeType();
        values.put(MediaStore.Audio.Media.MIME_TYPE, mimeType);
        if (!TextUtils.isEmpty(recordCloud.getDisplayName()) && !TextUtils.isEmpty(mimeType)) {
            int lastIndex = recordCloud.getDisplayName().lastIndexOf(".");
            if ((lastIndex > 0) && (recordCloud.getDisplayName().substring(lastIndex).equals(POSTFIX_POINT_AMR)) && MIMETYPE_AMR_WB.equals(mimeType)) {
                values.put(MediaStore.Audio.Media.MIME_TYPE, MIMETYPE_RAW);
            }
        }

        int renameResult = renameInternal(uriWithId, values);
        DebugUtil.d(TAG, "rename result:" + renameResult + " ,from cloud mimeType = " + mimeType);
        return renameResult;
    }

    public static int rename(Uri uriWithId, String newDisplayName) throws RecoverableSecurityException {
        if (uriWithId == null) {
            DebugUtil.e(TAG, "rename uriWithId is null");
            return -1;
        }
        ContentValues values = new ContentValues();
        values.put(MediaStore.Audio.Media.TITLE, getTitleByName(newDisplayName));
        values.put(MediaStore.Audio.Media.DISPLAY_NAME, newDisplayName);

        int renameResult = renameInternal(uriWithId, values);
        DebugUtil.d(TAG, "rename result:" + renameResult + " , newDisplayName = " + newDisplayName);
        return renameResult;
    }

    @VisibleForTesting
    public static int renameInternal(Uri uriWithId, ContentValues updateValues) throws RecoverableSecurityException {
        int renameResult = -1;
        try {
            String displayName = updateValues.getAsString(MediaStore.Audio.Media.DISPLAY_NAME);
            String mimeType = updateValues.getAsString(MediaStore.Audio.Media.MIME_TYPE);
            if (!FileDealUtil.isDisplayNameExtMatchWithMimeType(displayName, mimeType)) {
                DebugUtil.d(TAG, "renameInternal, suffix not match mimetype");
                Record mediaRecord = getRecordFromMediaByUriId(uriWithId);
                if (mediaRecord != null) {
                    File oldFile = new File(mediaRecord.mData);
                    File newFile = new File(oldFile.getParentFile(), updateValues.getAsString(MediaStore.Audio.Media.DISPLAY_NAME));
                    DebugUtil.d(TAG, "renameInternal, oldFile: " + oldFile + ", newFile: " + newFile);
                    if (oldFile.renameTo(newFile)) {
                        renameResult = 1;
                        MediaDataScanner.getInstance().add(BaseApplication.getAppContext(), newFile.getAbsolutePath());
                        MediaDataScanner.getInstance().flush(BaseApplication.getAppContext());
                    }
                }
            } else {
                renameResult = BaseApplication.getAppContext().getContentResolver().update(uriWithId, updateValues, null, null);
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "rename exception", e);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                if (e instanceof RecoverableSecurityException) {
                    throw e;
                } else if (e instanceof IllegalArgumentException) {
                    Record mediaRecord = getRecordFromMediaByUriId(uriWithId);
                    if (mediaRecord != null) {
                        File oldFile = new File(mediaRecord.mData);
                        DebugUtil.d(TAG, "oldFile: " + oldFile);
                        File newFile = new File(oldFile.getParentFile(), updateValues.getAsString(MediaStore.Audio.Media.DISPLAY_NAME));
                        DebugUtil.d(TAG, "newFile: " + newFile);
                        if (oldFile.renameTo(newFile)) {
                            renameResult = 1;
                            MediaDataScanner.getInstance().add(BaseApplication.getAppContext(), newFile.getAbsolutePath());
                            MediaDataScanner.getInstance().flush(BaseApplication.getAppContext());
                        }
                    }
                }
            }
        }
        return renameResult;
    }

    public static int update(Uri uri, ContentValues updateValues) {
        return BaseApplication.getAppContext().getContentResolver().update(uri, updateValues, null, null);
    }

    public static int delete(Uri uri) {
        int deleteResult = -1;
        Record record = getRecordFromMediaByUriId(uri);
        String filePath = "";
        if (record != null) {
            filePath = record.getData();
        }
        try {
            deleteResult = BaseApplication.getAppContext().getContentResolver().delete(uri, null, null);
        } catch (Throwable e) {
            DebugUtil.e(TAG, "delete uri error", e);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                if (e instanceof RecoverableSecurityException) {
                    throw e;
                }
            }
        }
        boolean fileDelete = false;
        if ((!BaseUtil.isAndroidQOrLater()) && (!TextUtils.isEmpty(filePath))) {
            File file = new File(filePath);
            if (file.exists()) {
                fileDelete = file.delete();
            }
        }
        DebugUtil.i(TAG, "delete uri:" + uri + ", deleteResult:" + deleteResult + ", file delete: " + fileDelete);
        return deleteResult;
    }

    public static boolean updateRealSizeAndDuration(Uri uri) {
        if (uri == null) {
            DebugUtil.i(TAG, "updateRealSize: uri is null, return ");
            return false;
        }
        DebugUtil.d(TAG, "updateRealSize File Name: " + uri);
        DebugUtil.d(TAG, "updateRealSize start !");
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        long realSize = FileUtils.getFileSize(uri);
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        Pair<FileDescriptor, ParcelFileDescriptor> fileDescriptors = MediaDBUtils.getFileDescriptors(uri, "r");
        long time = 0;
        try {
            FileDescriptor fd = fileDescriptors.first;
            if (fd != null) {
                retriever.setDataSource(fd);
                time = Long.parseLong(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
                DebugUtil.i(TAG, "updateRealSizeAndDuration time = " + time);
            } else {
                DebugUtil.e(TAG, "updateRealSizeAndDuration getFileDescriptor is null !");
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB setDataSource get Exception " + e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                DebugUtil.e(TAG, " retriever.release() error:", e);
            }
            if (fileDescriptors.second != null) {
                try {
                    fileDescriptors.second.close();
                } catch (IOException e) {
                    DebugUtil.v(TAG, "close error:" + e);
                }
            }
        }
        ContentValues cv = new ContentValues();
        cv.put(MediaStore.Audio.Media.SIZE, realSize);
        if (time != 0) {
            cv.put(MediaStore.Audio.Media.DURATION, time);
        }
        int count = resolver.update(uri, cv, null, null);
        DebugUtil.d(TAG, "updateRealSize end ! count = " + count);
        return count > 0;
    }

    public static boolean updateRealSizeAndDuration(Context context, File file) {
        if ((file == null) || !file.exists()) {
            DebugUtil.i(TAG, "updateRealSize: file not exist or file is null, return ");
            return false;
        }
        DebugUtil.d(TAG, "updateRealSize File Name: " + file.getName());
        DebugUtil.d(TAG, "updateRealSize start !");
        ContentResolver resolver = context.getContentResolver();
        Uri base = BASE_URI;
        String data = null;
        String where = null;
        String mFilePath = file.getAbsolutePath();
        if (mFilePath.contains("'")) {
            data = mFilePath.replace("'", "''");
        } else {
            data = mFilePath;
        }
        if (!TextUtils.isEmpty(data)) {
            where = MediaStore.Audio.Media.DATA + " COLLATE NOCASE =" + "'" + data + "'";
        }
        Cursor cr = null;
        long realSize = file.length();
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        long time = 0;
        try {
            retriever.setDataSource(file.getPath());
            time = Long.parseLong(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB setDataSource get Exception " + e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                DebugUtil.e(TAG, " retriever.release() error:", e);
            }
        }
        try {
            cr = resolver.query(base, CursorHelper.getProjection(), where, null, null);
            if ((cr != null) && (cr.getCount() > 0) && cr.moveToFirst()) {
                int sizeIndex = cr.getColumnIndex(MediaStore.Audio.Media.SIZE);
                long sizeFromDB = 0;
                if (sizeIndex >= 0) {
                    sizeFromDB = cr.getLong(sizeIndex);
                }
                ContentValues cv = new ContentValues();
                cv.put(MediaStore.Audio.Media.SIZE, realSize);
                if (time != 0) {
                    cv.put(MediaStore.Audio.Media.DURATION, time);
                }
                int updateCount = resolver.update(base, cv, where, null);
                DebugUtil.d(TAG, "filesize from mediaDB: " + sizeFromDB + ", real file size:  " + realSize + ", updasteCount: " + updateCount + ", parsed time from file is " + time);
                return updateCount > 0;
            } else {
                DebugUtil.w(TAG, "updateRealSize resolver.query null");
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRealSize query get Exception ", e);
        } finally {
            if (cr != null) {
                cr.close();
            }
        }
        return false;
    }

    public static boolean updateRealSizeAndDurationAndName(Uri uri, String filePath, boolean changeFileName) {
        if (uri == null) {
            DebugUtil.i(TAG, "updateRealSizeAndDurationAndName: uri is null, return ");
            return false;
        }
        DebugUtil.d(TAG, "updateRealSizeAndDurationAndName File Name: " + uri + ", fileName: " + FileUtils.getDisplayNameByPath(filePath) + ", start");
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        long realSize = -1;
        realSize = FileUtils.getFileSize(uri);
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        long time = 0;
        Pair<FileDescriptor, ParcelFileDescriptor> fileDescriptors = null;
        try {
            fileDescriptors = MediaDBUtils.getFileDescriptors(uri, "r");
            FileDescriptor fd = fileDescriptors.first;
            if (fd != null) {
                retriever.setDataSource(fd);
                time = Long.parseLong(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
                DebugUtil.i(TAG, "updateRealSizeAndDurationAndName time = " + time);
            } else {
                DebugUtil.e(TAG, "updateRealSizeAndDurationAndName getFileDescriptor is null !");
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRealSizeAndDurationAndName setDataSource get Exception " + e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                DebugUtil.e(TAG, " retriever.release() error:", e);
            }
            if ((fileDescriptors != null) && (fileDescriptors.second != null)) {
                try {
                    fileDescriptors.second.close();
                } catch (IOException e) {
                    DebugUtil.v(TAG, "close error:" + e);
                }
            }
        }
        ContentValues cv = new ContentValues();
        if (realSize > -1) {
            cv.put(MediaStore.Audio.Media.SIZE, realSize);
        }
        if (time != 0) {
            cv.put(MediaStore.Audio.Media.DURATION, time);
        }
        if (changeFileName) {
            cv.put(MediaStore.Audio.Media.DATA, filePath);
            String displayName = getDisplayNameFromFullPath(filePath);
            String title = getTitleFromDisplayName(displayName);
            DebugUtil.i(TAG, "updateRealSizeAndDurationAndName: displayName : " + displayName + ", title: " + title);
            cv.put(MediaStore.Audio.Media.TITLE, title);
            cv.put(MediaStore.Audio.Media.DISPLAY_NAME, displayName);
        }
        int count = 0;
        try {
            count = resolver.update(uri, cv, null, null);
            DebugUtil.d(TAG, "updateRealSize end ! count = " + count);
        } catch (Exception e) {
            DebugUtil.e(TAG, "media db update failed", e);
        }
        return count > 0;
    }

    public static Uri addToMediaDBForRecoveryCopy(Context context, File file, String relativePath, boolean needScanMediaDB) {
        DebugUtil.d(TAG, "add to media DB start !");
        DebugUtil.d(TAG, "file Name = " + file.getName());
        ContentResolver resolver = context.getContentResolver();
        Uri base = BASE_URI;
        ContentValues cv = new ContentValues();
        long modDate = file.lastModified();
        long size = file.length();
        String fileName = file.getName();
        String title = fileName.substring(0, fileName.lastIndexOf('.'));
        String mFilePath = file.getAbsolutePath();
        String data = null;
        String where = null;

        if (mFilePath.contains("'")) {
            data = mFilePath.replace("'", "''");
        } else {
            data = mFilePath;
        }

        if (!TextUtils.isEmpty(data)) {
            where = MediaStore.Audio.Media.DATA + " COLLATE NOCASE = ? ";
        }

        Cursor cr = null;

        try {
            cr = resolver.query(base, CursorHelper.getProjection(), where, new String[]{data}, null);

            if ((cr != null) && (cr.getCount() > 0) && cr.moveToFirst()) {
                MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                long time = 0;

                try {
                    retriever.setDataSource(file.getPath());
                    time = Long.parseLong(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
                } catch (Exception e) {
                    DebugUtil.e(TAG, "addToMediaDB setDataSource get Exception " + e);
                    time = 0;
                } finally {
                    try {
                        retriever.release();
                    } catch (Exception e) {
                        DebugUtil.e(TAG, " retriever.release() error:", e);
                    }
                }

                DebugUtil.d(TAG, "file name = " + fileName + "long file time = " + time + " file size = " + size);
                cv.put(MediaStore.Audio.Media.DATE_MODIFIED, modDate / TIME_ONE_SECOND);
                cv.put(MediaStore.Audio.Media.DURATION, time);
                cv.put(MediaStore.Audio.Media.SIZE, size);
                resolver.update(base, cv, where, new String[]{data});
                return null;
            } else {
                DebugUtil.w(TAG, "addToMediaDB resolver.query null");
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB query get Exception ", e);
            return null;
        } finally {
            if (cr != null) {
                cr.close();
            }
        }
        cv.put(MediaStore.Audio.Media.IS_MUSIC, "0");
        cv.put(MediaStore.Audio.Media.TITLE, title);
        cv.put(MediaStore.Audio.Media.DATA, file.getAbsolutePath());
        if (BaseUtil.isAndroidQOrLater()) {
            cv.put(MediaStore.Audio.Media.RELATIVE_PATH, relativePath);
        }
        cv.put(MediaStore.Audio.Media.DATE_ADDED, modDate / TIME_ONE_SECOND);
        cv.put(MediaStore.Audio.Media.DATE_MODIFIED, modDate / TIME_ONE_SECOND);
        cv.put(MediaStore.Audio.Media.SIZE, size);
        String mimeType = "";
        String postfix = fileName.substring(fileName.length() - 3).trim().toLowerCase();

        if ("amr".equals(postfix)) {
            mimeType = "audio/amr";
        } else if ("wav".equals(postfix)) {
            mimeType = "audio/x-wav";
        } else if ("mp3".equals(postfix)) {
            mimeType = "audio/mpeg";
        }
        cv.put(MediaStore.Audio.Media.MIME_TYPE, mimeType);
        DebugUtil.v(TAG, "File mimeType: " + mimeType);
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        long time = 0;
        try {
            retriever.setDataSource(file.getPath());
            time = Long.parseLong(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
        } catch (Exception e) {
            DebugUtil.e(TAG, "--get Exception " + e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                DebugUtil.e(TAG, " retriever.release() error:", e);
            }
        }
        cv.put(MediaStore.Audio.Media.DURATION, time);
        DebugUtil.v(TAG, "File time: " + cv.get(MediaStore.Audio.Media.DURATION));
        DebugUtil.v(TAG, "Inserting audio record: " + cv.toString());

        Uri result = null;
        try {
            result = resolver.insert(base, cv);
        } catch (Exception e) {
            DebugUtil.e(TAG, "setDataSource insert get Exception", e);
            return null;
        }
        DebugUtil.v(TAG, "insertUri: " + result);
        if (needScanMediaDB) {
            MediaDataScanner.getInstance().add(context, file.getAbsolutePath());
            MediaDataScanner.getInstance().flush(context);
        }
        return result;
    }


    public static int moveRecordFileByCursor(Cursor cursor) {
        long start = System.currentTimeMillis();
        int updateResult = -1;
        String path = null;
        int pathIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATA);
        if (pathIndex >= 0) {
            path = cursor.getString(pathIndex);
        }
        long rowId = -1;
        int idIndex = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
        if (idIndex >= 0) {
            rowId = cursor.getLong(idIndex);
        }
        if (!TextUtils.isEmpty(path) && (rowId != -1)) {
            String relativePath = getMusicRelativePathByPath(path);
            if (!TextUtils.isEmpty(relativePath)) {
                Uri uri = MediaDBUtils.genUri(rowId);
                ContentValues cv = new ContentValues();
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    cv.put(MediaStore.Audio.Media.RELATIVE_PATH, relativePath);
                }
                try {
                    updateResult = MediaDBUtils.update(uri, cv);
                } catch (Exception e) {
                    DebugUtil.e(TAG, "move record file exception", e);
                }
            }
        }
        DebugUtil.i(TAG, "moveRecordFileByCursor: result : " + updateResult
                + ", use time:" + (System.currentTimeMillis() - start) + " ms, name: " + FileUtils.getDisplayNameByPath(path));
        return updateResult;
    }

    public static int getNeedMoveRecordFileCount() {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Cursor cursor = null;
        String[] projection = new String[]{MediaStore.Audio.Media._ID};
        int count = 0;
        try {
            cursor = resolver.query(BASE_URI, projection, MediaDBUtils.getNeedMoveRecordFileWhere(), CursorHelper.getsAcceptableAudioTypes(), null);
            if (cursor != null) {
                count = cursor.getCount();
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "query exception", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        DebugUtil.i(TAG, "get need move record file count : " + count);
        return count;
    }

    public static String getNeedMoveRecordFileWhere() {
        String phoneDir = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext());
        String sdcardDir = BaseUtil.getSDCardStorageDir(BaseApplication.getAppContext());
        String phoneRecordings = phoneDir + File.separator + RECORDINGS;
        String sdcardRecordings = sdcardDir + File.separator + RECORDINGS;
        List<String> pathList = new ArrayList<>();
        pathList.add(phoneRecordings);
        pathList.add(sdcardRecordings);

        /**
         * 若是一加，则增加迁移一加目录
         */
        if (BaseUtil.isOnePlus()) {
//            Record
            String firstDir = RecordModeConstant.OP_RELATIVE_PATH_RECORD_ABOVE_Q;
//            Record/SoundRecord
            String secondDir = RecordModeConstant.OP_STORAGE_RECORD_ABOVE_AND_R;
            if (BaseUtil.isAndroidQ()) {
                firstDir = RecordModeConstant.OP_RECORD;
                secondDir = RecordModeConstant.OP_STORAGE_RECORD_BELOW_Q;
            }
            String phoneRecords = phoneDir + File.separator + firstDir + File.separator;
            String sdcardRecords = sdcardDir + File.separator + firstDir + File.separator;
            String phoneSoundRecord = phoneDir + File.separator + secondDir + File.separator;
            String sdcardSoundRecord = sdcardDir + File.separator + secondDir + File.separator;
            pathList.add(phoneRecords);
            pathList.add(sdcardRecords);
            pathList.add(phoneSoundRecord);
            pathList.add(sdcardSoundRecord);
        }
        String where = "(" + MediaStore.Audio.Media.MIME_TYPE + " " + CursorHelper.getsAcceptableAudioTypesSQL() + ") "
                + " AND (" + MediaStore.Audio.Media.SIZE + "!=" + 0 + ")";

        int length = pathList.size() - 1;
        for (int i = 0; i <= length; i++) {
            if (i == 0) {
                where += " and ( ";
            }
            where += MediaStore.Audio.Media.DATA + " LIKE '" + pathList.get(i) + "%'" + (i == length ? ")" : " OR ");
        }
        DebugUtil.d(TAG, "getNeedMoveRecordFileWhere is " + where);
        return where;
    }

    public static String getMusicRelativePathByPath(String path) {
        String dirRecordings = File.separator + RECORDINGS + File.separator;
        String dirStandard = dirRecordings + STANDARD_RECORDINGS + File.separator;
        String dirMeeting = dirRecordings + MEETING_RECORDINGS + File.separator;
        String dirInterview = dirRecordings + INTERVIEW_RECORDINGS + File.separator;
        String dirCall = dirRecordings + CALL_RECORDINGS + File.separator;
        String dirOPlus = File.separator + RecordModeConstant.OP_RECORD + File.separator;
        String relativePath = Environment.DIRECTORY_MUSIC + File.separator + RECORDINGS + File.separator;
        if (path.contains(dirStandard)) {
            relativePath += STANDARD_RECORDINGS + File.separator;
        } else if (path.contains(dirMeeting)) {
            relativePath += MEETING_RECORDINGS + File.separator;
        } else if (path.contains(dirInterview)) {
            relativePath += INTERVIEW_RECORDINGS + File.separator;
        } else if (path.contains(dirCall)) {
            relativePath += CALL_RECORDINGS + File.separator;
        } else if (path.contains(dirRecordings)) {
            return relativePath;
        } else if (path.contains(dirOPlus)) {
            relativePath += STANDARD_RECORDINGS + File.separator;
        } else {
            relativePath = "";
        }
        return relativePath;
    }

    // 通过OWNER_PACKAGE_NAME对比判断是否是录音App产生的文件
    public static boolean hasOwnerPackageName(String data) {
        boolean hasOwnerPackageName = false;
        Cursor cursor = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            try {
                ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
                String[] projection = new String[]{MediaStore.Audio.Media.OWNER_PACKAGE_NAME};
                String selection = MediaStore.Audio.Media.DATA + "=?";
                String[] selectionArgs = new String[]{String.valueOf(data)};
                cursor = resolver.query(BASE_URI, projection, selection, selectionArgs, null);
                if ((cursor != null) && cursor.moveToFirst()) {
                    int index = cursor.getColumnIndex(MediaStore.Audio.Media.OWNER_PACKAGE_NAME);
                    String string = null;
                    if (index >= 0) {
                        string = cursor.getString(index);
                    }
                    DebugUtil.i(TAG, "owner package name is " + string);
                    hasOwnerPackageName = BaseApplication.getAppContext().getPackageName().equalsIgnoreCase(string);
                }
            } catch (Throwable e) {
                DebugUtil.e(TAG, "hasOwnerPackageName ", e);
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
        } else {
            hasOwnerPackageName = false;
        }
        return hasOwnerPackageName;
    }

    private static String getDisplayNameFromFullPath(String fullName) {
        int lastIndex = fullName.lastIndexOf(File.separator);
        if ((lastIndex > 0) && (lastIndex < fullName.length())) {
            return fullName.substring(lastIndex + 1);
        }
        return "";
    }

    private static String getTitleFromDisplayName(String inputDisplayName) {
        String title = inputDisplayName;
        if (TextUtils.isEmpty(inputDisplayName)) {
            return title;
        }
        int lastIndex = inputDisplayName.lastIndexOf(".");
        if ((lastIndex > 0) && (lastIndex < inputDisplayName.length())) {
            title = inputDisplayName.substring(0, lastIndex);
        }
        return title;
    }

    public static String getTitleByName(String name) {
        if (TextUtils.isEmpty(name)) {
            DebugUtil.w(TAG, "getTitleByName  isEmpty");
            return "";
        }
        int lastDot = name.lastIndexOf(".");
        String title = name;
        int firstSeparator = 0;
        if (name.contains(File.separator)) {
            firstSeparator = name.lastIndexOf(File.separator) + 1;
        }
        if (firstSeparator < name.length()) {
            if (lastDot > 0) {
                title = name.substring(firstSeparator, lastDot);
            } else {
                title = name.substring(firstSeparator);
            }
        }
        DebugUtil.i(TAG, "get title by name :" + title);
        return title;
    }

    public static Uri genUri(long rowId) {
        return ContentUris.withAppendedId(BASE_URI, rowId);
    }

    public static Uri genUri(ContentValues values) {
        return BaseApplication.getAppContext().getContentResolver().insert(BASE_URI, values);
    }

    public static ContentValues getCloudContentValues(@NotNull Record recordCloud, @NotNull String displayName) {
        ContentValues value = new ContentValues();
        value.put(MediaStore.Audio.Media.RELATIVE_PATH, recordCloud.getRelativePath());
        value.put(MediaStore.Audio.Media.DISPLAY_NAME, displayName);
        value.put(MediaStore.Audio.Media.MIME_TYPE, recordCloud.getMimeType());
        value.put(MediaStore.Audio.Media.DATE_MODIFIED, recordCloud.getDateModied());
        return value;
    }

    public static Record getRecordFromMediaByUriId(Uri uriWithId) {
        ContentResolver cr = BaseApplication.getAppContext().getContentResolver();
        Cursor cursor = null;
        Record result = null;
        try {
            cursor = cr.query(uriWithId, null, null, null, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                result = new Record(cursor, Record.TYPE_FROM_MEDIA);
            }
        } catch (Throwable throwable) {
            DebugUtil.e(TAG, "getRecordFromMediaByUriId e: ", throwable);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    public static Record queryRecordById(long rowId) {
        Record record = null;
        Cursor cursor = null;
        String selection = MediaStore.Audio.Media._ID + " = ?";
        String[] selectionArgs = new String[]{String.valueOf(rowId)};
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(BASE_URI, null, selection, selectionArgs, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                record = new Record(cursor, Record.TYPE_FROM_MEDIA);
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query mime type error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return record;
    }

    public static boolean queryAudioFileExist(long rowId) {
        Cursor cursor = null;
        String selection = MediaStore.Audio.Media._ID + " = ?";
        String[] selectionArgs = new String[]{String.valueOf(rowId)};
        String[] projection = new String[]{MediaStore.Audio.Media._ID};
        boolean exist = false;
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(BASE_URI, projection, selection, selectionArgs, null);
            exist = cursor != null && cursor.getCount() > 0;
        } catch (Throwable e) {
            DebugUtil.e(TAG, "queryAudioFileExist", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        DebugUtil.d(TAG, "queryAudioFileExist, exist:" + exist);
        return exist;
    }

    public static Record queryRecordByUri(Uri uri) {
        Record record = null;
        Cursor cursor = null;
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(uri, null, null, null, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                record = new Record(cursor, Record.TYPE_FROM_MEDIA);
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query mime type error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return record;
    }

    public static Record queryRecordByRelativePathAndDisplayName(String relativePath, String displayName) {
        Cursor cursor = null;
        Record record = null;
        String[] projection = CursorHelper.getProjection();
        String selection = MediaStore.Audio.Media.RELATIVE_PATH + " COLLATE NOCASE = ? and " + MediaStore.Audio.Media.DISPLAY_NAME + " = ?";
        String[] selectionArgs = new String[]{relativePath, displayName};
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(BASE_URI, projection, selection, selectionArgs, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                record = new Record(cursor, Record.TYPE_FROM_MEDIA);
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query row id error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return record;
    }

    public static Record queryRecordByFullPath(String fullPath) {
        Cursor cursor = null;
        Record record = null;
        String[] projection = CursorHelper.getProjection();
        String selection = MediaStore.Audio.Media.DATA + " = ?";
        String[] selectionArgs = new String[]{fullPath};
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(BASE_URI, projection, selection, selectionArgs, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                record = new Record(cursor, Record.TYPE_FROM_MEDIA);
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "queryRecordByFullPath query error", e);
        } finally {
            try {
                if (cursor != null) {
                    cursor.close();
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "queryRecordByFullPath close error", e);
            }
        }
        return record;
    }

    public static Uri getMediaUriForRecord(Record record) {
        Uri result = null;
        if (BaseUtil.isAndroidQOrLater()) {
            String relativePath = record.getRelativePath();
            String displayName = record.getDisplayName();
            DebugUtil.i(TAG, "getMediaUriForRecord: relativePath: " + relativePath + ", displayName: " + displayName);
            result = getMediaUriForRelativePathAndDisplayName(relativePath, displayName);
        } else {
            String data = record.getData();
            DebugUtil.i(TAG, "getMediaUriForRecord: name: " + FileUtils.getDisplayNameByPath(data));
            result = getMediaUriForAbsolutePath(data);
        }
        DebugUtil.i(TAG, "getMediaUriForRecord: result: " + result);
        return result;
    }

    public static Uri getMediaUriForRelativePathAndDisplayName(String relativePath, String displayName) {
        String[] projection = new String[]{MediaStore.Audio.Media._ID};
        String whereClause = MediaStore.Audio.Media.RELATIVE_PATH + " COLLATE NOCASE = ? AND " + MediaStore.Audio.Media.DISPLAY_NAME + " = ? AND " + MediaStore.Audio.Media.SIZE + " > ?";
        String[] whereArgs = new String[]{relativePath, displayName, String.valueOf(0)};
        Uri uri = null;
        long id = getIdForMediaRecord(projection, whereClause, whereArgs);
        if (id != -1) {
            uri = genUri(id);
        }
        return uri;
    }

    public static Uri getMediaUriForAbsolutePath(String data) {
        String[] projection = new String[]{MediaStore.Audio.Media._ID};
        String whereClause = MediaStore.Audio.Media.DATA + " COLLATE NOCASE = ? AND " + MediaStore.Audio.Media.SIZE + " > ?";
        String[] whereArgs = new String[]{data, String.valueOf(0)};
        Uri uri = null;
        long id = getIdForMediaRecord(projection, whereClause, whereArgs);
        if (id != -1) {
            uri = genUri(id);
        }
        return uri;
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    public static long queryRowIdByRelativePathAndDisplayName(String relativePath, String displayName) {
        long rowId = 0;
        Cursor cursor = null;
        String[] projection = new String[]{MediaStore.Audio.Media._ID};
        String selection = MediaStore.Audio.Media.RELATIVE_PATH + " COLLATE NOCASE = ? and " + MediaStore.Audio.Media.DISPLAY_NAME + " = ?";
        String[] selectionArgs = new String[]{relativePath, displayName};
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(BASE_URI, projection, selection, selectionArgs, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                int idIndex = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
                if (idIndex >= 0) {
                    rowId = cursor.getLong(idIndex);
                }
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query row id error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return rowId;
    }

    public static long getDurationFromMediaId(long meidaId) {
        Uri dataUri = genUri(meidaId);
        long mDuration = getDurationFromUri(dataUri);
        DebugUtil.i(TAG, "checkDuration: get duration from uri: " + dataUri + ", duration : " + mDuration);
        return mDuration;
    }


    public static long getDurationFromUri(Uri uriWithId) {
        long time = 0;
        Pair<FileDescriptor, ParcelFileDescriptor> descriptors = getFileDescriptors(uriWithId, "r");
        if (descriptors.first == null) {
            return time;
        }
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            retriever.setDataSource(descriptors.first);
            time = Long.parseLong(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB setDataSource get Exception " + e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                DebugUtil.e(TAG, " retriever.release() error:", e);
            }
            try {
                if (descriptors.second != null) {
                    descriptors.second.close();
                }
            } catch (IOException e) {
                DebugUtil.v(TAG, "fd close error" + e);
            }
        }
        return time;
    }

    public static String getFileFormatFromUri(Uri uriWithId) {
        String fileFormat = "";
        Pair<FileDescriptor, ParcelFileDescriptor> descriptors = getFileDescriptors(uriWithId, "r");
        if (descriptors.first == null) {
            return fileFormat;
        }
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            retriever.setDataSource(descriptors.first);
            fileFormat = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE);
        } catch (Exception e) {
            DebugUtil.e(TAG, "addToMediaDB setDataSource get Exception " + e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                DebugUtil.e(TAG, " retriever.release() error:", e);
            }
            try {
                if (descriptors.second != null) {
                    descriptors.second.close();
                }
            } catch (IOException e) {
                DebugUtil.v(TAG, "fd close error" + e);
            }
        }
        return fileFormat;
    }

    public static Pair<String, Long> getFileFormatAndDurationFromUri(Uri uriWithId) {
        String fileFormat = getMimeTypeFromUri(BaseApplication.getAppContext(), uriWithId);
        long time = getDurationFromMediaStore(BaseApplication.getAppContext(), uriWithId);
        return new Pair(fileFormat, time);
    }

    public static Pair<FileDescriptor, ParcelFileDescriptor> getFileDescriptors(Uri uri, String mode) {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        FileDescriptor fd = null;
        ParcelFileDescriptor pfd = null;
        Pair<FileDescriptor, ParcelFileDescriptor> pair = null;
        try {
            pfd = resolver.openFileDescriptor(uri, mode);
            if (pfd != null) {
                fd = pfd.getFileDescriptor();
                DebugUtil.i(TAG, "getFileDescriptors uri " + uri + ", pfd: " + pfd + ", pfd.stateSize: " + pfd.getStatSize() + ", fd: " + fd);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "open uri get file descriptor error", e);
            try {
                if (pfd != null) {
                    pfd.close();
                }
            } catch (IOException ioe) {
                DebugUtil.e(TAG, "pfd close error", ioe);
            }
        } finally {
            pair = new Pair<>(fd, pfd);
        }
        return pair;
    }

    public static long queryIdByData(String data) {
        Cursor cursor = null;
        String[] projection = new String[]{MediaStore.Audio.Media._ID};
        String selection = MediaStore.Audio.Media.DATA + " COLLATE NOCASE = ?";
        String[] selectionArgs = new String[]{data};
        long mediaId = -1;
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(BASE_URI, projection, selection, selectionArgs, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                int idIndex = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
                if (idIndex >= 0) {
                    mediaId = cursor.getLong(idIndex);
                }
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "queryIdByData error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return mediaId;
    }

    /**
     * 通过文件名查询媒体库文件Id
     * @param displayName
     * @return
     */
    public static long queryIdByRelativePathAndDisplayName(String relativePath, String displayName) {
        Cursor cursor = null;
        String[] projection = new String[]{MediaStore.Audio.Media._ID, MediaStore.Audio.Media.DISPLAY_NAME};
        String selection = MediaStore.Audio.Media.RELATIVE_PATH + " = ? and " + MediaStore.Audio.Media.DISPLAY_NAME + " = ?";
        String[] selectionArgs = new String[]{relativePath, displayName};
        long mediaId = -1;
        try {
            cursor = BaseApplication.getAppContext().getContentResolver().query(BASE_URI, projection, selection, selectionArgs, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                int idIndex = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
                if (idIndex >= 0) {
                    mediaId = cursor.getLong(idIndex);
                }
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "queryIdByData error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return mediaId;
    }

    private static long getIdForMediaRecord(String[] projection, String whereClause, String[] whereArgs) {
        long id = -1;
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Cursor cursor = null;
        try {
            cursor = resolver.query(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, projection, whereClause, whereArgs, null);
            DebugUtil.i(TAG, "getIdForMediaRecord: " + whereClause + ", whereArgs: " + Arrays.toString(whereArgs));
            if ((cursor != null) && (cursor.getCount() > 0)) {
                if (cursor.moveToFirst()) {
                    int idIndex = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
                    if (idIndex >= 0) {
                        id = cursor.getLong(idIndex);
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "query exception", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return id;
    }

    public static void deleteRecordsInMediaDB(Context context, List<String> items) {
        if ((items != null) && !items.isEmpty()) {
            try {
                int pageSize = PAGE_SIZE;
                int pageCount = (int) Math.ceil(items.size() * 1.0 / pageSize);
                for (int i = 1; i <= pageCount; i++) {
                    int startLength = (i - 1) * pageSize;
                    int endLength = (i == pageCount) ? items.size() : (i * pageSize);
                    List<String> itemsBatch = items.subList(startLength, endLength);

                    StringBuilder builder = getWhereForInKeyword(itemsBatch.size(), MediaStore.Audio.Media.DATA);
                    String where = builder.toString();
                    String[] selectionArgs = new String[itemsBatch.size()];
                    itemsBatch.toArray(selectionArgs);
                    List<Long> recordIds = getMediaRecordIds(context, where, selectionArgs);
                    int result = context.getContentResolver().delete(BASE_URI, where,
                            selectionArgs);
                    if (!recordIds.isEmpty()) {
                        for (Long id : recordIds) {
                            ConvertDeleteUtil.deleteConvertData(context, id);
                            NoteDbUtils.deleteNoteByMediaId(String.valueOf(id));
                        }
                    }
                    DebugUtil.w(TAG, "deleteRecordsInMediaDB, real delete media result=" + result + "input delete size: " + items.size(), true);
                }
            } catch (Exception e) {
                DebugUtil.w(TAG, "deleteRecordsInMediaDB, media, e=" + e);
            }
        }
        scanRecordingsDir(context);
    }

    private static void scanRecordingsDir(Context context) {
        String recorderDir = BaseUtil.INSTANCE.getPhoneStorageDir(context);
        if (recorderDir == null) {
            recorderDir = CursorHelper.DEFAULT_DIR;
        }
        boolean isAboveQ = BaseUtil.isAndroidQOrLater();
        if (isAboveQ) {
            recorderDir = recorderDir + File.separator + RecordModeConstant.STORAGE_RECORD_ABOVE_Q;
        } else {
            recorderDir = recorderDir + File.separator + RecordModeConstant.STORAGE_RECORD;
        }
        MediaDataScanner.getInstance().add(context, recorderDir);
        MediaDataScanner.getInstance().flush(context);
    }

    public static StringBuilder getWhereForInKeyword(int size, String column) {
        StringBuilder builder = new StringBuilder();
        if (size == 0) {
            return builder;
        }
        builder.append(column).append(" IN ( ");
        for (int i = 0; i < size; i ++) {
            builder.append("?").append(",");
        }
        if (builder.lastIndexOf(",") == builder.length() - 1) {
            builder.deleteCharAt(builder.length() - 1);
        }
        builder.append(")");
        return builder;
    }

    private static List<Long> getMediaRecordIds(Context context, String where, String[] whereArgs) {
        Cursor cursor = null;
        ArrayList<Long> result = new ArrayList<>();
        try {
            cursor = context.getContentResolver().query(BASE_URI, new String[]{MediaStore.Audio.Media._ID}, where, whereArgs, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int indexOfId = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
                while (cursor.moveToNext()) {
                    if (indexOfId > 0) {
                        long id = cursor.getLong(indexOfId);
                        result.add(id);
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getMediaRecordIds error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    public static List<Record> getMediaRecordsById(Context context, String[] idList) {
        String order = MediaStore.Audio.Media.DATE_ADDED + " DESC," + MediaStore.Audio.Media.DISPLAY_NAME + " DESC";
        StringBuilder selection = new StringBuilder(MediaStore.Audio.Media._ID + " in (");
        for (int i = 0; i < idList.length; i++) {
            if (i == idList.length - 1) {
                selection.append("?)");
            } else {
                selection.append("?, ");
            }
        }
        DebugUtil.d(TAG, "getMediaRecordsById, selection:" + selection.toString());
        Record record = null;
        Cursor cursor = null;
        ArrayList<Record> result = new ArrayList<>();
        try {
            cursor = context.getContentResolver().query(BASE_URI, null, selection.toString(), idList, order);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    record = new Record(cursor, Record.TYPE_FROM_MEDIA);
                    result.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getMediaRecordsById error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    /**
     * 通过ContentResolver获取文件的MIME类型
     * @param context 上下文
     * @param fileUri 文件的Uri
     * @return MIME类型
     */
    private static String getMimeTypeFromUri(Context context, Uri fileUri) {
        try {
            return context.getContentResolver().getType(fileUri);
        } catch (Exception e) {
            DebugUtil.e(TAG, "getMimeTypeFromUri error", e);
            return null;
        }
    }

    /**
     * 通过MediaStore查询媒体文件的时长
     * @param context 上下文
     * @param fileUri 文件的Uri
     * @return 时长（毫秒），若失败返回-1
     */
    private static long getDurationFromMediaStore(Context context, Uri fileUri) {
        Cursor cursor = null;
        try {
            String[] projection = {MediaStore.MediaColumns.DURATION};
            cursor = context.getContentResolver().query(
                    fileUri,
                    projection,
                    null,
                    null,
                    null
            );
            if (cursor != null && cursor.moveToFirst()) {
                return cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DURATION));
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getDurationFromMediaStore error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return -1;
    }
}
