package com.soundrecorder.common.utils

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean

object MarkProcessUtil {

    const val TAG = "MarkProcessUtil"

    @JvmStatic
    fun mergeOldAndNewMarkList(oldMarkDataBean: List<MarkDataBean>, newMarkDataBean: List<MarkDataBean>): MutableList<MarkDataBean> {
        //merge marklist
        val result: MutableList<MarkDataBean> = mutableListOf()
        result.addAll(oldMarkDataBean)
        if (newMarkDataBean.isNotEmpty()) {
            newMarkDataBean.forEach {
                if (result.none { mark -> mark.timeInMills == it.timeInMills }) {
                    result.add(it)
                }
            }
        }
        result.sort()
        return result
    }


    fun diffMarkList(oldMarks: MutableList<MarkDataBean>, newMarks: MutableList<MarkDataBean>): MarkDiffResult {
        DebugUtil.i(TAG, "diffMarkList ")
        var addMarks: MutableList<MarkDataBean> = mutableListOf()
        var updateMarks: MutableList<MarkDataBean> = mutableListOf()
        var deleteMarks: MutableList<MarkDataBean> = mutableListOf()
        var matchedOldMarks: MutableList<MarkDataBean> = mutableListOf()
        //排序
        oldMarks.sort()
        newMarks.sort()
        //
        deleteMarks.addAll(oldMarks)


        for (newMark in newMarks) {
            var newMarkMatched = false
            for (oldMark in oldMarks) {
                if (newMark.timeInMills == oldMark.timeInMills) {
                    updateMarks.add(newMark)
                    matchedOldMarks.add(oldMark)
                    newMarkMatched = true
                    break
                }
            }
            if (!newMarkMatched) {
                addMarks.add(newMark)
            }
        }

        deleteMarks.removeAll(matchedOldMarks)
        return MarkDiffResult(addMarks, updateMarks, deleteMarks)
    }


    data class MarkDiffResult(
        var addMarks: MutableList<MarkDataBean>,
        var updateMarks: MutableList<MarkDataBean>,
        var deleteMarks: MutableList<MarkDataBean>
    )
}