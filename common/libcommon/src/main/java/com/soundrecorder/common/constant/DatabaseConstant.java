/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : DatabaseConstant
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, huang<PERSON>wang, create
 ***********************************************************/

package com.soundrecorder.common.constant;


import android.net.Uri;

import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALLER_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_AVATAR_COLOR;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRECT_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ERROR_CODE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_DIRECT_ON;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LEVEL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ORIGINAL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PARSE_CALL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SCAN_OSHARE_TEXT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID;

import com.oplus.recorderlog.util.CommonFlavor;
import com.soundrecorder.common.db.KeyWordDbUtils;

import java.util.ArrayList;
import java.util.List;

public class DatabaseConstant {
    public static final String PACKAGE_NAME = CommonFlavor.getInstance().getPackageName();
    public static final String AUTHORITY = PACKAGE_NAME + ".provider";
    public static final String PATH_CONVERT = "convert";
    public static final String PATH_UPLOAD = "upload";

    public static final String PATH_GROUP_INFO = "group_info";
    public static final String OBSERVER_CONVERT_COMPLETE = "observer_convert_complete";
    public static final String PROVIDER_RECORD_TYPE = "vnd.android.cursor.dir/record";
    public static final String PROVIDER_STATUS_TYPE = "vnd.android.cursor.dir/status";
    public static final String PROVIDER_CONVERT_TYPE = "vnd.android.cursor.dir/convert";
    public static final String PROVIDER_UPLOAD_TYPE = "vnd.android.cursor.dir/upload";

    public static final String PROVIDER_GROUP_INFO_TYPE = "vnd.android.cursor.dir/group_info";
    public static final String PROVIDER_SEARCH_HISTORY_TYPE = "vnd.android.cursor.dir/search_history";
    public final static String TABLE_NAME_RECORDER = "records";
    public final static String TABLE_NAME_RECORDER_TMP = "records_tmp";
    public final static String TABLE_NAME_STATUS = "status";
    public final static String TABLE_NAME_CONVERT = "convert";
    public final static String TABLE_NAME_CONVERT_TEMP = "convert_temp";
    public final static String TABLE_NAME_UPLOAD = "upload";
    public final static String TABLE_NAME_RECYCLE_BIN = "recycle_bin";
    public final static String TABLE_NAME_SEARCH_HISTORY = "search_history";

    public final static String TABLE_NAME_GROUP_INFO = "group_info";

    public final static String TABLE_NAME_GROUP_INFO_TMP = "group_info_tmp";
    /* 数据库trigger： 当 Table group_info deleted字段有更新时触发，用于更新跟分组相关的表*/
    public final static String DB_TRIGGER_GROUP_INFO_UPDATE = "db_trigger_update_group_deleted";
    /* 数据库trigger： 当 Table group_info 记录有删除时触发，用于更新跟分组相关的表*/
    public final static String DB_TRIGGER_GROUP_INFO_DELETE = "db_trigger_delete_group";

    public static final String ROOT = "recorderrecord";
    public static final String CONCAT_PROJECTION_STRING = COLUMN_NAME_RELATIVE_PATH + " || " + COLUMN_NAME_DISPLAY_NAME;

    private static final String CONTENT_AUTHORITY = "content://" + AUTHORITY;
    private static final String PATH_RECORD = "records";
    private static final String PATH_STATUS = "status";
    private static final String[] RECORD_PROJECTION = new String[]{
            RecorderColumn.COLUMN_NAME_ID,
            COLUMN_NAME_UUID,
            COLUMN_NAME_DATA,
            COLUMN_NAME_SIZE,
            COLUMN_NAME_DISPLAY_NAME,
            COLUMN_NAME_MIMETYPE,
            COLUMN_NAME_DATE_CREATED,
            COLUMN_NAME_DATE_MODIFIED,
            COLUMN_NAME_RECORD_TYPE,
            COLUMN_NAME_MARK_DATA,
            COLUMN_NAME_AMP_DATA,
            COLUMN_NAME_DURATION,
            COLUMN_NAME_BUCKET_ID,
            COLUMN_NAME_BUCKET_DISPLAY_NAME,
            COLUMN_NAME_DIRTY,
            COLUMN_NAME_DELETE,
            COLUMN_NAME_MD5,
            COLUMN_NAME_FILE_ID,
            COLUMN_NAME_GLOBAL_ID,
            COLUMN_NAME_SYNC_TYPE,
            COLUMN_NAME_SYNC_UPLOAD_STATUS,
            COLOUM_NAME_SYNC_DOWNLOAD_STATUS,
            COLUMN_NAME_ERROR_CODE,
            COLUMN_NAME_LEVEL,
            COLUMN_NAME_LOCAL_EDIT_STATUS,
            COLUMN_NAME_SYNC_DATE,
            COLUMN_NAME_FAIL_COUNT,
            COLUMN_NAME_LAST_FAIL_TIME,
            COLUMN_NAME_RELATIVE_PATH,
            COLUMN_NAME_AMP_FILE_PATH,
            COLUMN_NAME_PRIVATE_STATUS,
            COLUMN_NAME_MIGRATE_STATUS,
            COLUMN_NAME_IS_MARKLIST_SHOWING,
            COLUMN_NAME_IS_DIRECT_ON,
            COLUMN_NAME_DIRECT_TIME,
            COLUMN_NAME_IS_RECYCLE,
            COLUMN_NAME_FILE_RECYCLE_PATH,
            COLUMN_NAME_DELETE_TIME,
            COLUMN_NAME_GROUP_ID,
            COLUMN_NAME_GROUP_UUID,
            COLUMN_NAME_CALLER_NAME,
            COLUMN_NAME_ORIGINAL_NAME,
            COLUMN_NAME_CALL_AVATAR_COLOR,
            COLUMN_NAME_SCAN_OSHARE_TEXT,
            COLUMN_NAME_CALL_NAME,
            COLUMN_NAME_PARSE_CALL,
    };

    public static class GroupInfoColumn {
        public final static String COLUMN_NAME_ID = "_id";
        public final static String COLUMN_NAME_UUID = "uuid";
        public final static String COLUMN_NAME_GROUP_NAME = "group_name";
        public final static String COLUMN_NAME_GROUP_COLOR = "group_color";
        public final static String COLUMN_NAME_GROUP_SORT = "group_sort";
        public final static String COLUMN_NAME_IS_PRIVATE = "is_private";
        public final static String COLUMN_NAME_GLOBAL_ID = "global_id";
        public final static String COLUMN_NAME_DIRTY = "dirty";
        public final static String COLUMN_NAME_DELETED = "deleted";
        public final static String COLUMN_NAME_GROUP_COUNT = "group_count";
        public final static String COLUMN_NAME_GROUP_TYPE = "group_type";
        public final static String COLUMN_NAME_CLOUD_SYS_VERSION = "sys_version";
        public final static String COLUMN_NAME_SYNC_TYPE = "sync_type";
        public final static String COLUMN_NAME_SYNC_UPLOAD_STATUS = "sync_upload_status";
        public final static String COLUMN_NAME_SYNC_DOWNLOAD_STATUS = "sync_download_status";
        public final static String COLUMN_NAME_ERROR_CODE = "error_code";
        public final static String COLUMN_NAME_SYNC_DATE = "sync_date";
        public final static String COLUMN_NAME_LOCAL_EDIT_STATUS = "editStatus";
    }

    public static class RecorderColumn {
        public final static String COLUMN_NAME_ID = "_id";
        public final static String COLUMN_NAME_UUID = "uuid";
        public final static String COLUMN_NAME_DATA = "_data";
        public final static String COLUMN_NAME_SIZE = "size";
        public final static String COLUMN_NAME_DISPLAY_NAME = "display_name";
        public final static String COLUMN_NAME_MIMETYPE = "mime_type";
        public final static String COLUMN_NAME_DATE_CREATED = "date_added";
        public final static String COLUMN_NAME_DATE_MODIFIED = "date_modified";
        public final static String COLUMN_NAME_RECORD_TYPE = "record_type";
        public final static String COLUMN_NAME_MARK_DATA = "mark_data";
        public final static String COLUMN_NAME_AMP_DATA = "amplitude_data";
        public final static String COLUMN_NAME_DURATION = "duration";
        public final static String COLUMN_NAME_BUCKET_ID = "bucket_id";
        public final static String COLUMN_NAME_BUCKET_DISPLAY_NAME = "bucket_display_name";
        public final static String COLUMN_NAME_DIRTY = "dirty";
        public final static String COLUMN_NAME_DELETE = "deleted";
        public final static String COLUMN_NAME_MD5 = "md5";
        public final static String COLUMN_NAME_FILE_ID = "file_id";
        public final static String COLUMN_NAME_GLOBAL_ID = "global_id";
        public final static String COLUMN_NAME_SYNC_TYPE = "sync_type";
        public final static String COLUMN_NAME_SYNC_UPLOAD_STATUS = "sync_upload_status";
        public final static String COLOUM_NAME_SYNC_DOWNLOAD_STATUS = "sync_download_status";
        public final static String COLUMN_NAME_ERROR_CODE = "error_code";
        public final static String COLUMN_NAME_LEVEL = "level";
        public final static String COLUMN_NAME_LOCAL_EDIT_STATUS = "editStatus";
        public final static String COLUMN_NAME_SYNC_DATE = "sync_date";
        public final static String COLUMN_NAME_FAIL_COUNT = "failed_count";
        public final static String COLUMN_NAME_LAST_FAIL_TIME = "last_failed_time";
        public final static String COLUMN_NAME_RELATIVE_PATH = "relative_path";
        public final static String COLUMN_NAME_AMP_FILE_PATH = "amp_file_path";
        public final static String COLUMN_NAME_PRIVATE_STATUS = "private_status";
        public final static String COLUMN_NAME_MIGRATE_STATUS = "migrate_status";
        public final static String COLUMN_NAME_IS_MARKLIST_SHOWING = "is_marklist_showing";
        public final static String COLUMN_NAME_RECORD_KEY_ID_FOR_OHTER_TABLE = "key_id";
        // 对应cloudKit 恢复数据元数据版本号sysVersion
        public final static String COLUMN_NAME_CLOUD_SYS_VERSION = "sys_version";
        // 对应cloudKit上传文件返回checkPayload，临时使用，备份上传Create元数据传入，校验文件在云端是否
        public final static String COLUMN_NAME_CLOUD_CHECK_PAYLOAD = "file_checkPayload";
        /*定向录音新增字段 is_directOn、 direct_time*/
        public final static String COLUMN_NAME_IS_DIRECT_ON = "is_directOn";
        public final static String COLUMN_NAME_DIRECT_TIME = "direct_time";

        /*回收站需求新增字段, 回收站文件的绝对路径 */
        public final static String COLUMN_NAME_IS_RECYCLE = "is_recycle";
        /*回收站文件的绝对路径 */
        public final static String COLUMN_NAME_FILE_RECYCLE_PATH = "recycle_path";
        /*进入回收站的开始时间 */
        public final static String COLUMN_NAME_DELETE_TIME = "delete_time";
        /*记录录音文件分组名称 */
        public final static String COLUMN_NAME_GROUP_ID = "group_id";
        public final static String COLUMN_NAME_GROUP_UUID = "group_uuid";
        public final static String COLUMN_NAME_CALLER_NAME = "caller_name";
        public final static String COLUMN_NAME_ORIGINAL_NAME = "original_name";
        public final static String COLUMN_NAME_CALL_AVATAR_COLOR = "call_avatar_color";

        /**该文件是否扫描了oshare目录下的转写文件
         * 默认值0，代表未扫描
         * 1：表示该录音文件已扫描了/Documents/oshare/Recording目录的转写文件*/
        public final static String COLUMN_NAME_SCAN_OSHARE_TEXT = "scan_oshare_text";
        /*联系人名称*/
        public final static String COLUMN_NAME_CALL_NAME = "callname";
        /*是否已经解析过昵称,默认0 未解析*/
        public final static String COLUMN_NAME_PARSE_CALL = "parse_call";
    }


    /**
     * 录音回收站表
     */
    public static class RecycleBinColumn {
        public static final String ID = "_id";
        public static final String DISPLAY_NAME = "display_name";
        public final static String NAME_MD5 = "md5";
        public final static String COLUMN_NAME_FILE_ID = "file_id";
        public final static String COLUMN_NAME_OLD_MEDIA_ID = "old_media_id"; //原本的媒体库id
        public final static String COLUMN_NAME_OLD_RECORD_ID = "old_record_id"; //原本的录音records表主键id
        public final static String COLUMN_NAME_GLOBAL_ID = "global_id";
        public final static String COLUMN_NAME_RECORD_TYPE = "record_type"; //0标准录音，1会议录音，2采访录音，3通话录音
        public final static String COLUMN_NAME_FILE_RECYCLE_PATH = "recycle_path"; //回收站文件的绝对路径
        public final static String COLUMN_NAME_DIRTY = "dirty"; //是否云同步，0：默认值，未同步；  1是已同步
        public final static String COLUMN_NAME_FILE_CREATE_TIME = "date_added"; //文件的创建时间
        public final static String COLUMN_NAME_DELETE_TIME = "delete_time"; //进入回收站的开始时间
    }

    public static class StatusColumn {
        public final static String COLUMN_NAME_ID = "_id";
        public final static String COLUMN_NAME_TYPE = "type";
        public final static String COLUMN_NAME_STATUS = "status";
    }

    public static class SearchHistoryColumn {
        public static final String COLUMN_NAME_ID = "_id";
        public static final String CONTENT = "content";
        public static final String DATE = "date";
    }

    public static class ConvertColumn {
        public static final String _ID = "_id";
        public static final String RECORD_ID = "record_id";
        public static final String MEDIA_PATH = "media_path";
        public static final String CONVERT_TEXTFILE_PATH = "convert_textfile_path";
        public static final String CHUNK_NAME = "chunk_name";
        public static final String COMPLETE_STATUS = "complete_status";
        public static final String ONLY_ID = "only_id"; // only id for record
        public static final String VERSION = "version";
        public static final String TASKID = "taskId";
        public static final String UPLOAD_REQUEST_ID = "upload_request_id";
        public static final String UPLOAD_KEY = "upload_key";
        public static final String PART_COUNT = "part_count";
        public static final String UPLOAD_STATUS = "upload_status";
        public static final String CONVERT_STATUS = "convert_status";
        public static final String UPLOAD_ALL_URL = "upload_all_url";
        public static final String HISTORY_ROLENAME = "history_role_name";
        public static final String SERVER_PLAN_CODE = "server_plan_code";
        public static final String CAN_SHOW_SPEAKER_ROLE = "can_show_speaker_role";
        public static final String SPEAKER_ROLE_ISSHOWING = "speaker_role_isshowing";
        public static final String SPEAKER_ROLE_ORIGINAL_NUMBER = "speaker_role_original_number";
        public static final String SPEAKER_ROLE_HAS_FIRSTSHOW = "speaker_role_has_firstshow";

        /*定向录音新增字段 is_directOn、 direct_time*/
        public final static String IS_DIRECT_ON = "is_directOn";
        public final static String DIRECT_TIME = "direct_time";
    }

    public static class UploadColumn {
        public static final String _ID = "_id";
        public static final String ONLY_ID = "only_id";
        public static final String UPLOAD_URL = "upload_url";
        public static final String UPLOAD_FILE_RANG_START = "file_rang_start";
        public static final String UPLOAD_FILE_RANG_END = "file_rang_end";
        public static final String UPLOAD_ETAG = "etag";
        public static final String UPLOAD_SEQ_NUM = "sequence_num";
    }

    public static final class RecordUri {
        public static final Uri RECORD_CONTENT_URI = getContentUri(PATH_RECORD);
        public static final Uri STATUS_CONTENT_URI = getContentUri(PATH_STATUS);
        public static final String NO_NOTIFY_KEY = "nonotify";
        public static final String NO_NOTIFY_VALUE = "1";

        public static Uri getAppnedRecordContentUri(long id) {
            return getAppnedContentUri(PATH_RECORD, id);
        }

        public static Uri getAppnedStatusContentUri(long id) {
            return getAppnedContentUri(PATH_STATUS, id);
        }

    }

    public static final class ConvertUri {
        public static final Uri CONVERT_CONTENT_URI = getContentUri(PATH_CONVERT);

        public static final Uri CONVERT_COMPLETE_OBSERVER_URI = getContentUri(OBSERVER_CONVERT_COMPLETE);
    }

    public static final class UploadUri {
        public static final Uri UPLOAD_CONTENT_URI = getContentUri(PATH_UPLOAD);
    }

    public static final class GroupInfoUri {
        public static final Uri GROUP_INFO_URI = getContentUri(PATH_GROUP_INFO);
    }

    public static final class KeyWordUri {
        public static final Uri KEY_WORD_URI = getContentUri(KeyWordDbUtils.TABLE_KEY_WORD_NAME);
    }

    public static Uri getContentUri(String volumeName) {
        return Uri.parse(CONTENT_AUTHORITY + "/" + volumeName);
    }

    private static Uri getAppnedContentUri(String volumeName, long id) {
        return Uri.parse(CONTENT_AUTHORITY + "/" + volumeName + "/" + id);
    }

    public static String[] getRecordProjection() {
        return RECORD_PROJECTION;
    }

    /**
     * 获取数据库字段并指定需要去重的数据库字段
     * @param distinctColumnName
     * @return
     */
    public static String[] getDistinctRecordProjection(String distinctColumnName) {
        String[] projection = new String[RECORD_PROJECTION.length];
        projection[0] = "DISTINCT " + distinctColumnName;
        List<String> tmpList = new ArrayList<>();
        for (int i = 0; i < RECORD_PROJECTION.length; i++) {
            String columnName = RECORD_PROJECTION[i];
            if (!columnName.equals(distinctColumnName)) {
                tmpList.add(columnName);
            }
        }
        System.arraycopy(tmpList.toArray(new String[tmpList.size()]), 0, projection, 1, tmpList.size());
        return projection;
    }
}
