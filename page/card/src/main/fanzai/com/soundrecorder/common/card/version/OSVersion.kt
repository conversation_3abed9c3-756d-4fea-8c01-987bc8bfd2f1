/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OSVersion
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/8/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.version

interface OSVersion {
    fun onSeedlingCardSizeChanged(newSize: Int)
    fun onSeedlingCardShowChangeWhenSave(showStatusBar: Boolean)
    fun resetShowStatusBar()
    fun getShowStatusBar(): Boolean
    fun needHideSeedlingCard(): <PERSON>olean
}