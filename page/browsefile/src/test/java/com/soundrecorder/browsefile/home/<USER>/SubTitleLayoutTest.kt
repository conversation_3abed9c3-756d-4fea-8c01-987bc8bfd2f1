/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SubTitleLayoutTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view

import android.content.Context
import android.os.Build
import android.view.MotionEvent
import android.widget.TextView
import androidx.test.core.app.ApplicationProvider
import androidx.test.espresso.action.MotionEvents
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.R
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus
import com.soundrecorder.modulerouter.cloudkit.tipstatus.OnLinkTextClickListener
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class SubTitleLayoutTest {

    companion object {
        const val STATE_COMPLETED = 4
    }

    private var mContext: Context? = null
    private var layout: SubTitleLayout? = null


    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        createLayout()
    }

    private fun createLayout() {
        val activity = Robolectric.buildActivity(BrowseFile::class.java).get()
        layout = SubTitleLayout(activity)
        layout?.let {
            val desc = TextView(activity).apply {
                id = R.id.subtitle_desc
            }

            val event = TextView(activity).apply {
                id = R.id.subtitle_event
            }

            it.addView(desc)
            it.addView(event)

            Whitebox.invokeMethod<Void>(it, "onFinishInflate")
        }
    }

    @After
    fun tearDown() {
        mContext = null
        layout = null
    }

    @Test
    fun should_not_null_when_onFinishInflate() {
        layout?.let {
            Whitebox.invokeMethod<Void>(it, "onFinishInflate")
            val desTv = Whitebox.getInternalState(it, "descTv") as TextView
            val eventTv = Whitebox.getInternalState(it, "eventBtn") as TextView
            println("des $desTv")
            println("event $eventTv")
            Assert.assertNotNull(desTv)
            Assert.assertNotNull(eventTv)
        }
    }

    @Test
    fun should_when_initView() {
        layout?.let {
            Whitebox.invokeMethod<Void>(it, "initView")
            val desTv = Whitebox.getInternalState(it, "descTv") as TextView
            val eventTv = Whitebox.getInternalState(it, "eventBtn") as TextView
            println("des $desTv")
            println("event $eventTv size:${eventTv.textSize}")
            Assert.assertNotNull(desTv)
            Assert.assertNotNull(eventTv)
            Assert.assertTrue(eventTv.hasOnClickListeners())
        }
    }

    @Test
    fun should_when_updateSubTitleClickState() {
        layout?.let {
            it.updateSubTitleClickState(0.0f)
            var alpha = Whitebox.getInternalState(it, "mNewAlpha") as Float
            Assert.assertEquals(0.0f, alpha)

            it.updateSubTitleClickState(0.1f)
            alpha = Whitebox.getInternalState(it, "mNewAlpha") as Float
            Assert.assertEquals(0.1f, alpha)

            it.updateSubTitleClickState(1.0f)
            alpha = Whitebox.getInternalState(it, "mNewAlpha") as Float
            Assert.assertEquals(1.0f, alpha)
        }
    }

    private fun obtainDownEvent(): MotionEvent {
        val position = FloatArray(2)
        position[0] = 10.0f
        position[1] = 10.0f
        return MotionEvents.obtainDownEvent(position, position)
    }

    @Test
    fun should_bool_when_onTouchEvent() {
        layout?.let {
            val event = obtainDownEvent()

            it.updateSubTitleClickState(0.0f)
            var result = it.onTouchEvent(event)
            Assert.assertFalse(result)

            it.updateSubTitleClickState(2.0f)
            result = it.onTouchEvent(event)
            Assert.assertFalse(result)
        }
    }

    @Test
    fun should_when_setCompoundStartDrawables() {
        layout?.let {
            val desTv = Whitebox.getInternalState(it, "descTv") as TextView

            it.setCompoundStartDrawables(0)
            var drawables = desTv.compoundDrawablesRelative
            Assert.assertNotNull(drawables)
            Assert.assertEquals(4, drawables.size)
            Assert.assertNull(drawables[0])

            it.setCompoundStartDrawables(R.drawable.ic_all_callrecorder)
            drawables = desTv.compoundDrawablesRelative
            Assert.assertNotNull(drawables)
            Assert.assertEquals(4, drawables.size)
            Assert.assertNotNull(drawables[0])
        }
    }

    @Test
    fun should_when_setTitleText() {
        layout?.let {

            it.setTitleText("", "")
            val desTv = Whitebox.getInternalState(it, "descTv") as TextView
            val eventTv = Whitebox.getInternalState(it, "eventBtn") as TextView
            Assert.assertEquals("", desTv.text)
            Assert.assertEquals("", eventTv.text)

            it.setTitleText("云同步暂停", "查看")
            desTv.post {
                Assert.assertEquals("云同步暂停", desTv.text)
                Assert.assertEquals("查看", eventTv.text)
            }


            it.setTitleText("云同步暂停", "查看")
            desTv.post {
                Assert.assertEquals("云同步暂停", desTv.text)
                Assert.assertEquals("查看", eventTv.text)
            }

            it.setTitleText("当前处于充电中，为避免设备温度过高，同步暂停", "查看")
            desTv.post {
                Assert.assertEquals("当前处于充电中，为避免设备温度过高，同步暂停", desTv.text)
                Assert.assertEquals("查看", eventTv.text)
            }
        }
    }

    @Test
    fun should_when_setOnLinkTextClickListener() {
        layout?.let {
            it.setOnLinkTextClickListener(null, null)
            var tipStatus = Whitebox.getInternalState(it, "tipStatusState") as Int
            var listener = Whitebox.getInternalState(it, "clickListener") as? OnLinkTextClickListener
            Assert.assertEquals(ITipStatus.STATE_NONE, tipStatus)
            Assert.assertNull(listener)

            it.setOnLinkTextClickListener(STATE_COMPLETED, object : OnLinkTextClickListener {
                override fun onClick(state: Int) { }
            })
            tipStatus = Whitebox.getInternalState(it, "tipStatusState") as Int
            listener = Whitebox.getInternalState(it, "clickListener") as? OnLinkTextClickListener
            Assert.assertEquals(STATE_COMPLETED, tipStatus)
            Assert.assertNotNull(listener)
        }
    }
}