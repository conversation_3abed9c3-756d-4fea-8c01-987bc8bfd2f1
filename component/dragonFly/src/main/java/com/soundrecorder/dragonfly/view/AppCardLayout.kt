/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardViewHolder
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.dragonfly.view

import android.annotation.SuppressLint
import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Paint
import android.os.Build
import android.os.Looper
import android.os.SystemClock
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.View.OnClickListener
import android.view.View.OnLayoutChangeListener
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.Keep
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.transition.*
import com.soundrecorder.dragonfly.R
import com.soundrecorder.dragonfly.bean.ActivityAction.ACTION_SHOW_NO_PERMISSION
import com.soundrecorder.dragonfly.bean.ActivityAction.ACTION_SHOW_SAVE_FILE_SUCCESS
import com.soundrecorder.dragonfly.bean.ActivityAction.KEY_DO_ACTION
import com.soundrecorder.dragonfly.bean.ActivityAction.KEY_FILE_NAME
import com.soundrecorder.dragonfly.bean.AppCardData
import com.soundrecorder.dragonfly.bean.CheckStartService.ENABLE
import com.soundrecorder.dragonfly.bean.ClickAction.CARD_ADD_TEXT_MARK
import com.soundrecorder.dragonfly.bean.ClickAction.CARD_SAVE_RECORDER_FILE
import com.soundrecorder.dragonfly.bean.ClickAction.CARD_SWITCH_RECORDER_STATUS
import com.soundrecorder.dragonfly.bean.ClickAction.CHECK_RECORDER_PERMISSION
import com.soundrecorder.dragonfly.bean.ClickAction.CHECK_START_SERVICE
import com.soundrecorder.dragonfly.bean.RecorderState
import com.soundrecorder.dragonfly.bean.SaveFileState
import com.soundrecorder.dragonfly.runnable.AppCardRunnable
import com.soundrecorder.dragonfly.transotion.AppCardAlphaTransition
import com.soundrecorder.dragonfly.utils.AppCardUtils
import com.soundrecorder.dragonfly.utils.AppCardUtils.DURATION_250
import com.soundrecorder.dragonfly.utils.AppCardUtils.DURATION_5000
import com.soundrecorder.dragonfly.utils.AppCardUtils.FLOAT_1_5
import com.soundrecorder.dragonfly.utils.AppCardUtils.appCtx
import com.soundrecorder.dragonfly.utils.AppCardUtils.doAction
import com.soundrecorder.dragonfly.utils.AppCardUtils.log
import com.soundrecorder.dragonfly.utils.AppCardUtils.runBackground
import com.soundrecorder.dragonfly.utils.CardDataUtils
import com.soundrecorder.dragonfly.view.button.AppCardButton
import com.soundrecorder.dragonfly.view.wave.WaveRecyclerView
import kotlin.math.abs

@Keep
@Suppress("TooGenericExceptionCaught")
internal class AppCardLayout(context: Context, attrs: AttributeSet? = null) : FrameLayout(context, attrs), OnClickListener, OnLayoutChangeListener {
    private var isOnVisible = false
    private val tvTimeText by lazy { findViewById<TextView>(R.id.tvTimeText) }
    private val tvRecorderName by lazy { findViewById<TextView>(R.id.tvRecorderName) }
    private val tvStateText by lazy { findViewById<TextView>(R.id.tvStateText) }
    private val btnAddTextMark by lazy { findViewById<AppCardButton>(R.id.btnAddTextMark) }
    private val btnSaveFile by lazy { findViewById<AppCardButton>(R.id.btnSaveFile) }
    private val btnSwitchState by lazy { findViewById<AppCardButton>(R.id.btnSwitchState) }
    private val waveRecyclerView by lazy { findViewById<WaveRecyclerView>(R.id.waveRecyclerView) }
    private var data: AppCardData? = null
    private var doShowAnimationTime = -1L
    private var doHideAnimationTime = -1L
    private val restoreCardDataRunnable = AppCardRunnable(this) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            CardDataUtils.obtainData()
        }
        addRestoreCardDataRunnable()
    }

    init {
        inflate(context, R.layout.app_card_view, this)
        setBackgroundColor(Color.WHITE)
        btnAddTextMark.setOnClickListener(this)
        btnSaveFile.setOnClickListener(this)
        btnSwitchState.setOnClickListener(this)
        tvRecorderName.paint.style = Paint.Style.FILL_AND_STROKE
        tvRecorderName.paint.strokeWidth = FLOAT_1_5
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            isForceDarkAllowed = false
        }
    }

    override fun onClick(v: View?) {
        if (AppCardUtils.isFastDoubleClick()) {
            return
        }
        when (v) {
            btnAddTextMark -> {
                val recordState = this.data?.recordState ?: RecorderState.INIT
                if (recordState != RecorderState.INIT) {
                    context.addTextMark()
                }
            }
            btnSwitchState -> {
                val recordState = this.data?.recordState ?: RecorderState.INIT
                if (recordState == RecorderState.INIT) {
                    context.startRecorderService()
                } else {
                    context.switchRecorderState()
                }
            }
            btnSaveFile -> {
                if (this.data != null) {
                    context.saveRecorderFile()
                }
            }
        }
    }

    fun refreshData(data: AppCardData) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            setData(data)
        } else {
            post(AppCardRunnable(this) {
                setData(data)
            })
        }
    }

    private fun setData(data: AppCardData) {
        try {
            backgroundColor(data.cardColor)
            tvRecorderName.textValue(data.recorderName)
            tvTimeText.fixTextFlash(data.timeText)
            tvTimeText.isFakeBoldText(data.isFakeBoldText)
            tvTimeText.textDescription(data.timeDes)
            tvTimeText.textColor(data.timeTextColor)
            tvStateText.textValue(data.stateText)
            tvStateText.textColor(data.stateTextColor)
            if (data.saveFileState == SaveFileState.SUCCESS) {
                hideMarkAndSaveFileViewWithAnimation()
                context.startRecorderAppCardActivity(ACTION_SHOW_SAVE_FILE_SUCCESS, data.fileName)
            } else if ((data.recordState == RecorderState.INIT)) {
                hideMarkAndSaveFileViewWithAnimation(false)
            } else {
                showMarkAndSaveFileView(false)
            }
            setBtnImgResource(btnAddTextMark, data.markSrc)
            setBtnImgResource(btnSaveFile, data.saveFileSrc)
            setBtnImgResource(btnSwitchState, data.recordStateSrc)
            btnAddTextMark.fakeDisable = !data.markEnabled
            btnSaveFile.isEnabled = data.saveEnabled
            btnSwitchState.fakeDisable = !data.switchEnabled
            btnAddTextMark.textDescription(data.markDesc ?: "")
            btnSwitchState.textDescription(data.recordStateDesc ?: "")
            btnSaveFile.textDescription(data.saveDesc ?: "")
            waveRecyclerView.refreshColor(data.cardWaveColor, data.cardDashWaveColor)
            if (data.recordState == RecorderState.RECORDING) {
                waveRecyclerView.recorderIntervalUpdate(data.ampsSize, data.lastAmps, data.hasRefreshTime70)
            } else {
                waveRecyclerView.stopRecorderMove(data.ampsSize, data.lastAmps)
            }
            this.data = data
            addRestoreCardDataRunnable()
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun setBtnImgResource(view: AppCardButton, @DrawableRes imgResource: Int) {
        try {
            if (getTag(R.id.tag_imageview_image_resource) != imgResource) {
                val dp24 = context.resources.getDimension(R.dimen.dp24).toInt()
                val drawable = ContextCompat.getDrawable(context.appCtx(), imgResource)?.apply {
                    setBounds(0, 0, dp24, dp24)
                }
                view.setImageDrawable(drawable)
                setTag(R.id.tag_imageview_image_resource, imgResource)
            }
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun showMarkAndSaveFileView(should: Boolean = true) {
        try {
            if (checkAnimatorRunning(doShowAnimationTime)) {
                return
            }
            if (should) {
                doShowAnimationTime = SystemClock.elapsedRealtime()
            }
            if (btnAddTextMark.alpha != 0f) {
                return
            }
            beginDelayedTransition(should) {
                btnAddTextMark.alpha = 1f
                btnAddTextMark.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    endToEnd = ConstraintLayout.LayoutParams.UNSET
                    endToStart = R.id.btnSwitchState
                }
                btnSaveFile.alpha = 1f
                btnSaveFile.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    startToStart = ConstraintLayout.LayoutParams.UNSET
                    startToEnd = R.id.btnSwitchState
                }
            }
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun hideMarkAndSaveFileViewWithAnimation(should: Boolean = true) {
        try {
            if (checkAnimatorRunning(doHideAnimationTime)) {
                return
            }
            if (should) {
                doHideAnimationTime = SystemClock.elapsedRealtime()
            }
            if (btnAddTextMark.alpha != 1f) {
                return
            }
            beginDelayedTransition(should) {
                btnAddTextMark.alpha = 0f
                btnAddTextMark.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    endToStart = ConstraintLayout.LayoutParams.UNSET
                }
                btnSaveFile.alpha = 0f
                btnSaveFile.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    startToEnd = ConstraintLayout.LayoutParams.UNSET
                }
            }
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun beginDelayedTransition(should: Boolean, function: () -> Unit) {
        try {
            if (should) {
                TransitionManager.beginDelayedTransition(this, TransitionSet().apply {
                    addTransition(AutoTransition())
                    addTransition(AppCardAlphaTransition(btnAddTextMark.id, btnSaveFile.id))
                }.setDuration(DURATION_250))
            }
        } catch (e: Exception) {
            TransitionManager.endTransitions(this)
            e.log()
        } finally {
            function()
        }
    }

    private fun Context.saveRecorderFile() {
        runBackground(AppCardRunnable(this@AppCardLayout) {
            doAction(CARD_SAVE_RECORDER_FILE, data?.widgetCode.toString())
        })
    }

    private fun Context.switchRecorderState() {
        runBackground(AppCardRunnable(this@AppCardLayout) {
            doAction(CARD_SWITCH_RECORDER_STATUS, data?.widgetCode.toString())
        })
    }

    private fun Context.addTextMark() {
        runBackground(AppCardRunnable(this@AppCardLayout) {
            doAction(CARD_ADD_TEXT_MARK, data?.widgetCode.toString())
        })
    }

    private fun Context.checkRecorderPermission(function: (Boolean) -> Unit) {
        runBackground(AppCardRunnable(this@AppCardLayout) {
            val flag = doAction(CHECK_RECORDER_PERMISSION, data?.widgetCode.toString())?.getBoolean("data", false) ?: false
            "checkRecorderPermission flag = $flag".log()
            post(AppCardRunnable(this) {
                function(flag)
            })
        })
    }

    private fun Context.checkStartService() {
        runBackground(AppCardRunnable(this@AppCardLayout) {
            val flag = doAction(CHECK_START_SERVICE, data?.widgetCode.toString())?.getInt("data", ENABLE) ?: ENABLE
            "checkStartService flag = $flag".log()
            if (flag == ENABLE) {
                post(AppCardRunnable(this) {
                    startForegroundRecorderService()
                })
            }
        })
    }

    private fun Context.startRecorderService() {
        checkRecorderPermission {
            try {
                if (it) {
                    checkStartService()
                } else {
                    startRecorderAppCardActivity()
                }
            } catch (e: Exception) {
                e.log()
            }
        }
    }

    private fun Context.startRecorderAppCardActivity(action: String = ACTION_SHOW_NO_PERMISSION, fileName: String = "") {
        try {
            startActivity(Intent("com.soundrecorder.dragonfly.RecorderAppCardActivity").apply {
                setPackage(AppCardUtils.appPackageName())
                putExtra(KEY_DO_ACTION, action)
                putExtra(KEY_FILE_NAME, fileName)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            }, ActivityOptions.makeBasic().setLaunchDisplayId(1).toBundle())
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun Context.startForegroundRecorderService() {
        try {
            startForegroundService(Intent().apply {
                action = "oplus.intent.action.RECORDER_SERVICE"
                setPackage(AppCardUtils.appPackageName())
                putExtra("service_auto_start_record", true)
                putExtra("launchFrom", "service_from_app_card")
            })
            showMarkAndSaveFileView()
            waveRecyclerView.doEnterAnimator()
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun checkAnimatorRunning(time: Long): Boolean {
        return SystemClock.elapsedRealtime() - time <= DURATION_250
    }

    private inline fun <reified T : ViewGroup.LayoutParams> View.updateLayoutParams(block: T.() -> Unit) {
        val params = layoutParams as T
        block(params)
        layoutParams = params
    }

    private fun addRestoreCardDataRunnable() {
        removeRestoreCardDataRunnable()
        val data = this.data
        if (data != null && isOnVisible && (data.recordState == RecorderState.PAUSED || data.recordState == RecorderState.RECORDING)) {
            postDelayed(restoreCardDataRunnable, DURATION_5000)
        }
    }

    private fun removeRestoreCardDataRunnable() {
        removeCallbacks(restoreCardDataRunnable)
    }

    fun onVisible() {
        isOnVisible = true
        CardDataUtils.register(context, this)
        CardDataUtils.obtainData()
        "onVisible $this".log()
    }

    fun onInVisible() {
        isOnVisible = false
        CardDataUtils.unregister(context, this)
        removeRestoreCardDataRunnable()
        "onInVisible $this".log()
    }

    override fun onAttachedToWindow() {
        removeOnLayoutChangeListener(this)
        addOnLayoutChangeListener(this)
        CardDataUtils.register(context, this)
        super.onAttachedToWindow()
        "onAttachedToWindow $this".log()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeOnLayoutChangeListener(this)
        CardDataUtils.unregister(context, this)
        removeRestoreCardDataRunnable()
        "onDetachedFromWindow $this".log()
    }

    private fun TextView.textValue(str: String) {
        try {
            if (text != str) {
                text = str
            }
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun View.textDescription(str: String) {
        try {
            if (contentDescription != str) {
                contentDescription = str
            }
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun TextView.textColor(color: Int) {
        try {
            if (getTag(R.id.tag_textview_text_color) != color) {
                setTextColor(color)
                setTag(R.id.tag_textview_text_color, color)
            }
        } catch (e: Exception) {
            e.log()
        }
    }


    private fun TextView.isFakeBoldText(flag: Boolean) {
        try {
            if (paint.isFakeBoldText != flag) {
                paint.isFakeBoldText = flag
            }
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun View.backgroundColor(color: Int) {
        try {
            if (getTag(R.id.tag_view_background_color) != color) {
                setBackgroundColor(color)
                setTag(R.id.tag_view_background_color, color)
            }
        } catch (e: Exception) {
            e.log()
        }
    }


    @SuppressLint("RtlHardcoded")
    private fun TextView.fixTextFlash(text: String, force: Boolean = false) {
        try {
            setText(text)
            val min = resources.getDimension(R.dimen.dp24)
            val newText = this.text.toString()
            gravity = Gravity.LEFT or Gravity.CENTER_VERTICAL
            val textWidth = paint.measureText(newText)
            val paddingS = ((width - textWidth) / 2).toInt()
            if (force || abs(paddingS - paddingLeft) > min) {
                setPadding(paddingS, paddingTop, paddingBottom, 0)
            }
        } catch (_: Exception) {
        }
    }

    override fun onLayoutChange(v: View?, left: Int, top: Int, right: Int, bottom: Int, oldLeft: Int, oldTop: Int, oldRight: Int, oldBottom: Int) {
        if (right - left != oldRight - oldLeft) {
            val timeText = data?.timeText
            if (!timeText.isNullOrEmpty()) {
                tvTimeText.fixTextFlash(timeText, true)
            }
        }
    }
}