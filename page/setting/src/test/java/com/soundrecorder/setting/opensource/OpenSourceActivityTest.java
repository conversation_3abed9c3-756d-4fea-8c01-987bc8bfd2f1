/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OpenSourceActivityTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.opensource;

import static android.content.Intent.ACTION_USER_BACKGROUND;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.robolectric.Shadows.shadowOf;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.view.MenuItem;

import androidx.lifecycle.Lifecycle;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.setting.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;

import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowLooper;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class OpenSourceActivityTest {
    private ActivityController<OpenSourceActivity> mController;
    private ShadowApplication mShadowApplication;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(OpenSourceActivity.class);
        mShadowApplication = ShadowApplication.getInstance();
    }

    @Test
    @Ignore
    public void should_startActivityAndRegisteredReceivers_when_onCreate() {
        OpenSourceActivity activity = mController.create().get();
        ShadowLooper.runUiThreadTasksIncludingDelayedTasks();
        Assert.assertNotNull(activity);
        Assert.assertEquals(Lifecycle.State.CREATED, activity.getLifecycle().getCurrentState());
        Assert.assertTrue(mShadowApplication.hasReceiverForIntent(new Intent(ACTION_USER_BACKGROUND)));
    }

    @Test
    @Ignore
    public void should_destoryActivity_when_onDestroy() {
        OpenSourceActivity activity = mController.create().destroy().get();
        Assert.assertEquals(Lifecycle.State.DESTROYED, activity.getLifecycle().getCurrentState());
        Assert.assertFalse(mShadowApplication.hasReceiverForIntent(new Intent(Intent.ACTION_USER_BACKGROUND)));
    }

    @Test
    public void should_startSharedActivity_when_onOptionsItemSelected_with_MenuItemIsHome() {
        OpenSourceActivity activity = mController.create().resume().get();
        MenuItem mockMenuItem = mock(MenuItem.class);
        doReturn(android.R.id.home).when(mockMenuItem).getItemId();
        activity.onOptionsItemSelected(mockMenuItem);
        int resultCode = shadowOf(activity).getResultCode();
        Assert.assertEquals(Activity.RESULT_CANCELED, resultCode);
        Assert.assertTrue(activity.isFinishing());
    }

    @After
    public void tearDown() {
        mController = null;
    }
}

