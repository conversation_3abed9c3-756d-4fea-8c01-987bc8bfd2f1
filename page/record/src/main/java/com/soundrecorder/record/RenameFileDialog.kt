/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - RenameFileDialog.kt
 * Description:
 *     The helper class for ui showing rename in record pat.
 *
 * Version: 1.0
 * Date: 2025-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-30   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record

import android.app.Activity
import android.view.View
import com.soundrecorder.common.R
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.dialog.AbsEditAlertDialog
import com.soundrecorder.common.dialog.PositiveCallback
import com.soundrecorder.common.fileoperator.rename.NameFileDialogUtil

class RenameFileDialog(
    activity: Activity,
    private val from: Int,
    private var content: String,
    private var positiveCallback: PositiveCallback? = null
) : AbsEditAlertDialog(activity) {

    companion object {
        const val FROM_NAVIGATION = 0
        const val FROM_PLAYBACK_MORE = 1
    }

    var mediaRecord: Record? = null
    var mDialogUtil: NameFileDialogUtil? = null
    var requestCode: Int = Constants.REQUEST_CODE_RENAME

    override fun onInitCustomView(customView: View) {
        getEditText()?.apply {
            hint = activity.getString(R.string.enter_filename)
            contentDescription = activity.getString(R.string.enter_filename)
        }
    }

    override fun getTitleText() = R.string.custom_name

    override fun onSave() {
        val newName = getNewContent()
        mDialogUtil = NameFileDialogUtil(requestCode)
        mDialogUtil?.apply {
            val result: Int = onPositive(activity, NameFileDialogUtil.DIALOG_TYPE_RENAME, newName, mediaRecord)
            CloudStaticsUtil.addCloudLog("RenameFileDialog", "onSave,rename ${mediaRecord?.displayName} to $newName, result=$result")
            if (result == NameFileDialogUtil.FAIL_WITH_SECURITY_EXCEPTION) {
                dismiss()
                return
            }
            if (result != -1) {
                showTextNote(result)
                return
            }
            var path: String? = null
            if (mediaRecord != null) {
                path = core2Full(newName, mediaRecord?.data)
            }
            positiveCallback?.callback(newName, path)
            this
        }

        when (from) {
            FROM_NAVIGATION -> BuryingPoint.addActionForRenameBtnClick(RecorderUserAction.VALUE_RENAME_DIALOG_NAVIGATION_BTN_SAVE)
            FROM_PLAYBACK_MORE -> BuryingPoint.addActionForRenameBtnClick(RecorderUserAction.VALUE_RENAME_DIALOG_PLAYBACK_BTN_SAVE)
        }
        dismiss()
    }

    override fun onCancel() {
        when (from) {
            FROM_NAVIGATION -> BuryingPoint.addActionForRenameBtnClick(RecorderUserAction.VALUE_RENAME_DIALOG_NAVIGATION_BTN_CANCEL)
            FROM_PLAYBACK_MORE -> BuryingPoint.addActionForRenameBtnClick(RecorderUserAction.VALUE_RENAME_DIALOG_PLAYBACK_BTN_CANCEL)
        }
        dismiss()
    }

    override fun getOriginalContent() = content

    /**用于重命名无权限时赋值*/
    fun getRenameContent(): String {
        return mDialogUtil?.renameContent ?: ""
    }

    /**用于重命名无权限时赋值*/
    fun getOperating(): Boolean = mDialogUtil?.operating ?: false

    fun resetOperating() {
        mDialogUtil?.resetOperating()
    }

    fun release() {
        dismiss()
        mDialogUtil?.release()
        mDialogUtil = null
    }
}