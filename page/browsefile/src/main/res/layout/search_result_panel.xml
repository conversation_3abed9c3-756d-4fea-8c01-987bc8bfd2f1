<?xml version="1.0" encoding="utf-8"?>
<layout>

    <merge xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/resultContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/common_background_color"
            android:orientation="vertical"
            android:paddingTop="0dp"
            android:visibility="gone"
            tools:visibility="visible">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include
                    android:id="@+id/empty_search"
                    layout="@layout/layout_empty_search" />

                <androidx.recyclerview.widget.COUIRecyclerView
                    android:id="@+id/resultList"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16" />
            </FrameLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/color_search_load_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/abl"
            android:visibility="gone">

            <TextView
                android:id="@+id/loadingTip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:text="@string/searching" />

            <com.coui.appcompat.progressbar.COUILoadingView
                style="?attr/couiLoadingViewLargeStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/loadingTip"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="10dp" />
        </RelativeLayout>
    </merge>
</layout>