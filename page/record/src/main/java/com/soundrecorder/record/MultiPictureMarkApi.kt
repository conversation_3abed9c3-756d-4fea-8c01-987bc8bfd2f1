/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MultiPictureMarkApi
 * Description:
 * Version: 1.0
 * Date: 2022/10/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/10/26 1.0 create
 */

package com.soundrecorder.record

import androidx.appcompat.app.AppCompatActivity
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.modulerouter.mark.MultiPictureMarkAction
import com.soundrecorder.modulerouter.mark.MultiPictureMarkAction.START_POP_VIEW_LOADING_ACTIVITY
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerData
import com.soundrecorder.record.picturemark.PopPicture
import com.soundrecorder.record.picturemark.PopViewLoadingActivity.Companion.startPopViewLoadingActivity

@Component(MultiPictureMarkAction.COMPONENT_NAME)
object MultiPictureMarkApi {

    @Action(START_POP_VIEW_LOADING_ACTIVITY)
    @JvmStatic
    fun <T, R : PhotoViewerData> startPopViewLoadingActivity(
        activity: AppCompatActivity,
        result: ArrayList<R>,
        doMultiPictureMark: (ArrayList<T>) -> Int,
        doCompleteMultiPictureMark: () -> Unit,
        requestCode: Int,
    ) {
        activity.startPopViewLoadingActivity(result as ArrayList<PopPicture>,
            doMultiPictureMark as (ArrayList<MarkMetaData>) -> Int,
            doCompleteMultiPictureMark,
            requestCode)
    }
}