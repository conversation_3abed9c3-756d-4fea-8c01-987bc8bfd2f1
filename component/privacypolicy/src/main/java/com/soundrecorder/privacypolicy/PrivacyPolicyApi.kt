/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionGuideDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionPrivacyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyAction
import com.soundrecorder.privacypolicy.functionguide.FunctionGuideDelegate

@Component(PrivacyPolicyAction.COMPONENT_NAME)
object PrivacyPolicyApi {

    @JvmStatic
    @Action(PrivacyPolicyAction.NEW_PRIVACY_POLICY_FRAGMENT)
    fun newPrivacyPolicyFragment(): Fragment {
        return PrivacyPolicyFragment()
    }

    @JvmStatic
    @Action(PrivacyPolicyAction.NEW_PRIVACY_POLICY_INFO_FRAGMENT)
    fun newPrivacyPolicyInfoFragment(type: Int): Fragment {
        return PrivacyPolicyInfoFragment.newInstance(type)
    }

    /**
     * 收集个人信息明示清单
     */
    @JvmStatic
    @Action(PrivacyPolicyAction.NEW_COLLECTION_INFO_FRAGMENT)
    fun newCollectionInfoFragment(type: Int): Fragment {
        return CollectionInfoFragment.newInstance(type)
    }

    /**
     * 收集个人信息明示清单-详情页
     */
    @JvmStatic
    @Action(PrivacyPolicyAction.NEW_COLLECTION_INFO_CONTENT_FRAGMENT)
    fun newCollectionInfoContentFragment(title: String?, type: Int, collectionType: Int): Fragment {
        return CollectionInfoContentFragment.newInstance(title, type, collectionType)
    }

    @JvmStatic
    @Action(PrivacyPolicyAction.NEW_PRIVACY_POLICY_DELEGATE)
    fun newPrivacyPolicyDelegate(context: AppCompatActivity, type: Int, resultListener: IPrivacyPolicyResultListener?): IPrivacyPolicyDelegate {
        return PrivacyPolicyDelegate(context, type, resultListener)
    }

    @JvmStatic
    @Action(PrivacyPolicyAction.NEW_FUNCTION_GUIDE_DELEGATE)
    fun newFunctionGuideDelegate(context: AppCompatActivity, functionClickOk: ((fromUserNotice: Boolean) -> Unit)?): IFunctionGuideDelegate {
        return FunctionGuideDelegate(context, functionClickOk)
    }

    @JvmStatic
    @Action(PrivacyPolicyAction.NEW_FUNCTION_PRIVACY_DELEGATE)
    fun newFunctionPrivacyDelegate(funcType: Int): IFunctionPrivacyDelegate {
        return FunctionPrivacyDelegate(funcType)
    }
}