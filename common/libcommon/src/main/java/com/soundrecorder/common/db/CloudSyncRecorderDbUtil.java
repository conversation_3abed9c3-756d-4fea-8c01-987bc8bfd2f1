package com.soundrecorder.common.db;

import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID;
import static com.soundrecorder.common.constant.RecordConstant.LOCAL_EDIT_COMPLET;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_DIRTY_MEGA_ONLY;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_NORMAL;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_NOT_DIRTY;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_PRIVETE_ENCRYPT;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_BACKUP_START;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_DEFAULT;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_LOCALLY_EXISTS_METADATA;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_SUC;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_TYPE_RECOVERY;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.soundrecorder.common.utils.RecordFileChangeNotify;
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState;
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction;

public class CloudSyncRecorderDbUtil {
    public static final String TAG = "CloudSyncRecorderDbUtil";

    public static boolean insertCloudMetadataForLocallyExistsFile(Record recordCloud) {
        if (recordCloud == null) {
            DebugUtil.i(TAG, "recordCloud is null, can not insert", true);
            return false;
        }
        recordCloud.setSyncType(SYNC_TYPE_RECOVERY);
        recordCloud.setSyncDownlodStatus(SYNC_STATUS_LOCALLY_EXISTS_METADATA);
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri base = DatabaseConstant.RecordUri.RECORD_CONTENT_URI;
        ContentValues cv = recordCloud.convertToContentValues();
        byte[] markData = recordCloud.getMarkData();
        try {
            Uri insertUri = resolver.insert(base, cv);
            //云端新增数据，需要解析并插入到mark数据表
            if ((insertUri != null) && (markData != null)) {
                long recordId = ContentUris.parseId(insertUri);
                String keyId = recordId + "";
                RecorderDBUtil.updateOrInsertMarkInNewTable(keyId, true, markData, false);
            }
            if (insertUri != null) {
                return true;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "insertCloudMetadataForLocallyExistsFile error.", e);
        }
        return false;
    }

    public static boolean processEncryptAudioFile(String displayName, String relativePath, String md5) {
        DebugUtil.w(TAG, "processEncrypAudioFile: displayName" + displayName + ", relativePath :" + relativePath + ", md5: " + md5);
        if (TextUtils.isEmpty(displayName) || TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(md5)) {
            return false;
        } else {
            String where = COLUMN_NAME_DISPLAY_NAME + " COLLATE NOCASE =? AND " + COLUMN_NAME_RELATIVE_PATH + " COLLATE NOCASE =? AND " + COLUMN_NAME_MD5 + " = ?";
            String[] whereArgs = new String[]{displayName, relativePath, md5};
            List<Record> records = RecorderDBUtil.getRecordData(BaseApplication.getAppContext(), null, where, whereArgs, null);
            if ((records != null) && (records.size() > 0)) {
                DebugUtil.i(TAG, "processEncrypAudioFile: find Records size " + records.size(), true);
                boolean processSuc = false;
                for (Record record : records) {
                    if (!TextUtils.isEmpty(record.getGlobalId())) {
                        processSuc |= markRecordAsEncryptAndDeleted(record.getId(), false);
                    } else {
                        boolean fileExist = record.fileExist();
                        if (!fileExist) {
                            // 加入私密保险箱的文件，录音不保留db数据，更录音删除操作一致
                            processSuc |= RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordsById(record.getId());
                            /*processSuc |= RecorderDBUtil.getInstance(BaseApplication.getAppContext()).markRecordAsPrivateStatus(record.getId(), true);*/
                        } else {
                            //do nothing, should not into this branch.
                            DebugUtil.e(TAG, "processEncrypAudioFile: file still exist should not be this");
                            processSuc = false;
                        }
                    }
                    // 删除转文本文件记录
                    ConvertDeleteUtil.deleteConvertData(BaseApplication.getAppContext(), record.getData());
                }
                return processSuc;
            } else {
                DebugUtil.i(TAG, "no record found in local db, do nothing and return", true);
                return false;
            }
        }
    }

    private static boolean markRecordAsEncryptAndDeleted(long id, boolean trigBackupNow) {
        if (id < 0) {
            DebugUtil.i(TAG, "markRecordAsDeleted id < 0", true);
            return false;
        }
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
        cv.put(COLUMN_NAME_SYNC_TYPE, RecordConstant.SYNC_TYPE_BACKUP);
        cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, RecordConstant.SYNC_STATUS_BACKUP_START);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, RECORD_PRIVETE_ENCRYPT);
        String where = COLUMN_NAME_ID + " = ?";
        int updateCount = BaseApplication.getAppContext().getContentResolver().update(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, cv, where, new String[]{String.valueOf(id)});
        if ((updateCount > 0) && trigBackupNow) {
            trigBackupNow();
        }
        return updateCount > 0;
    }

    public static boolean updateDisplayName(String originalName, String newName, String relativePath,
                                            boolean setNeedUpload, String realOriginalName) {
        boolean updateDbSuccess =
                RecorderDBUtil.getInstance(BaseApplication.getAppContext()).updateDisplayName(originalName, newName, relativePath,
                        setNeedUpload, realOriginalName);
        if (updateDbSuccess) {
            DebugUtil.i(TAG, "rename trig backup", true);
            trigBackupNow();
        }
        return updateDbSuccess;
    }

    public static boolean updateDisplayNameById(String rowId, String newName, int recordType, boolean setNeedUpload) {
        boolean updateDbSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).updateDisplayNameByRecordId(rowId, newName, recordType, setNeedUpload);
        if (updateDbSuccess) {
            DebugUtil.i(TAG, "rename trig backup", true);
            trigBackupNow();
        }
        return updateDbSuccess;
    }

    public static synchronized boolean updateDisplayName(String originalPath, String newPath, boolean setEditingComplete, boolean resetUuId) {
        Uri base = DatabaseConstant.RecordUri.RECORD_CONTENT_URI;
        if (TextUtils.isEmpty(newPath) || TextUtils.isEmpty(originalPath)) {
            DebugUtil.e(TAG, "one of paths is empty , can not update");
            return false;
        }
        ContentValues cv = new ContentValues();

        String displayName = FileUtils.getDisplayNameByPath(newPath);

        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA, newPath);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME, displayName);
        if (!newPath.equals(originalPath)) {
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
        }
        if (setEditingComplete) {
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS, LOCAL_EDIT_COMPLET);
        }
        if (resetUuId) {
            try {
                int cloudSwitchOnStatus = CloudSyncAction.queryCloudSwitchState(false);
                if (cloudSwitchOnStatus == CloudSwitchState.CLOSE) {
                    //switch is false , modify the uuId to avoid same uuid download to db
                    cv.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "updateDisplayName update UUID failed", e);
            }
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE = ?";
        int updateCount = 0;
        try {
            updateCount = BaseApplication.getAppContext().getContentResolver().update(base, cv, where, new String[]{originalPath});
            DebugUtil.i(TAG, " update DB _data displayName " + displayName + ", updateCount " + updateCount, true);
        } catch (Exception e) {
            DebugUtil.e(TAG, "setDataSource insert get Exception", e);
        }
        if (updateCount > 0) {
            DebugUtil.i(TAG, "rename trig backup", true);
            trigBackupNow();
        }
        return updateCount > 0;
    }

    public static boolean updateRecordMarkByPath(String fullpath, String inputString) {
        boolean updateDbSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).updateRecordMarkByPath(fullpath, inputString);
        if (updateDbSuccess) {
            DebugUtil.i(TAG, "rename trig backup", true);
            trigBackupNow();
        }
        return updateDbSuccess;
    }

    public static boolean updateRecordMarkById(long id, String inputString, boolean trigCloudBackUpNow) {
        boolean updateDbSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).updateRecordMarkById(id, inputString);
        if (updateDbSuccess && trigCloudBackUpNow) {
            DebugUtil.i(TAG, "rename trig backup", true);
            trigBackupNow();
        }
        return updateDbSuccess;
    }

    public static boolean updateDisplayNameForDownloadRename(long id, String newPath, boolean setEditingComplete) {
        Uri base = DatabaseConstant.RecordUri.RECORD_CONTENT_URI;
        if (TextUtils.isEmpty(newPath)) {
            DebugUtil.e(TAG, "path is empty , can not update", true);
            return false;
        }
        ContentValues cv = new ContentValues();
        String displayName = FileUtils.getDisplayNameByPath(newPath);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA, newPath);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME, displayName);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
        if (setEditingComplete) {
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS, LOCAL_EDIT_COMPLET);
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + " = ?";
        int updateCount = 0;
        try {
            updateCount = BaseApplication.getAppContext().getContentResolver().update(base, cv, where, new String[]{String.valueOf(id)});
            DebugUtil.i(TAG, " update DB displayName " + displayName + ", updateCount " + updateCount, true);
        } catch (Exception e) {
            DebugUtil.e(TAG, "setDataSource insert get Exception", e);
        }
        if (updateCount > 0) {
            DebugUtil.i(TAG, "rename trig backup", true);
            trigBackupNow();
        }
        return updateCount > 0;
    }

    public static boolean updateDisplayNameForRecoveryRename(boolean containMarkData, String globalId, Record recordOnCloud) {
        Uri base = DatabaseConstant.RecordUri.RECORD_CONTENT_URI;
        if (TextUtils.isEmpty(globalId)) {
            DebugUtil.e(TAG, " originalPath: " + globalId + ", one of them is empty , can not update");
            return false;
        }
        ContentValues cv = recordOnCloud.convertToContentValues();
        cv.put(DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS, SYNC_STATUS_RECOVERY_FILE_SUC);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE, System.currentTimeMillis());
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT, 0);
        cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME, 0);
        if (containMarkData) {
            cv.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA, recordOnCloud.getMarkData());
        }
        String where = DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID + " = ?";
        int updateCount = 0;
        try {
            updateCount = BaseApplication.getAppContext().getContentResolver().update(base, cv, where, new String[]{globalId});
            DebugUtil.i(TAG, " update DB originalPath _globalId " + globalId + ", contentValues " + cv + ", updateCount " + updateCount, true);
        } catch (Exception e) {
            DebugUtil.e(TAG, "setDataSource insert get Exception", e);
        }
        return updateCount > 0;
    }

    public static boolean updateGlobalIdAndFileIdForDelete(long id, String globalId, String fileId) {
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_GLOBAL_ID, globalId);
        cv.put(COLUMN_NAME_FILE_ID, fileId);
        String where = COLUMN_NAME_ID + "=?";
        int updateCount = BaseApplication.getAppContext().getContentResolver().update(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, cv, where, new String[]{String.valueOf(id)});
        DebugUtil.i(TAG, "updateGlobalIdAndFileIdForDelete : " + id + ", updateCount: " + updateCount + ", newGlobalId: " + globalId + ", newFileId: " + fileId, true);
        if (updateCount > 0) {
            return true;
        }
        return false;
    }

    public static boolean deleteRecordByPath(String path, boolean fromCloud) throws Exception {
        if (TextUtils.isEmpty(path)) {
            return true;
        }
        boolean deleteResult = false;
        DebugUtil.i(TAG, "deleteRecordByPath input name is " + FileUtils.getDisplayNameByPath(path) + ", fromCloud is " + fromCloud, true);
        if (fromCloud) {
            deleteResult = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).directDeleteFileAndRecord(path);
        } else {
            Record record = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).qureyRecordByPath(path);
            if (record == null) {
                DebugUtil.i(TAG, "find no record , can not delete", true);
                return false;
            }
            if (TextUtils.isEmpty(record.getGlobalId())) {
                // not in cloud , direct delete the record
                deleteResult = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).directDeleteFileAndRecord(path);
                //TipStatusManager.executeAsyncQueryStatus(Constants.MSG_DELETE_RECORD_NOT_IN_CLOUD);
            } else {
                boolean updateSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).markRecordAsDeleted(path);
                if (updateSuccess) {
                    trigBackupNow();
                }
            }
        }
        return deleteResult;
    }

    /**
     * only delete record db , not delete the file.
     *
     * @param path
     * @param fromCloud
     * @return
     */
    public static boolean deleteRecordDBRecordByPath(String path, boolean fromCloud) {
        if (TextUtils.isEmpty(path)) {
            return true;
        }
        boolean deleteResult = false;
        DebugUtil.i(TAG, "deleteRecordDBRecordByPath input path is " + path + " ,  fromCloud is " + fromCloud, true);
        if (fromCloud) {
            deleteResult = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordByPathExcludeAudioFile(path);
        } else {
            Record record = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).qureyRecordByPath(path);
            if (record == null) {
                DebugUtil.i(TAG, "find no record , can not delete", true);
                return false;
            }
            if (TextUtils.isEmpty(record.getGlobalId())) {
                // not in cloud , direct delete the record
                deleteResult = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordByPathExcludeAudioFile(path);
            } else {
                boolean updateSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).markRecordAsDeleted(path);
                if (updateSuccess) {
                    trigBackupNow();
                }
            }
        }
        return deleteResult;
    }

    /**
     * 回收站需求，文件移动到回收站后，需要标记删除
     *
     * @param record 文件在媒体库的绝对路径
     * @return
     */
    public static boolean updateRecordDBRecordFromRecycleBin(Record record, long recordId, String newDisplayName, boolean isCausedByRemoveGroup) {
        if (record == null) {
            DebugUtil.i(TAG, " no record , can not delete to recycle", true);
            return false;
        }
        DebugUtil.i(TAG, "deleteRecordDBRecordFromRecycleBin deleteRecordDBRecordFromRecycleBin " + record, true);

        //更新record表
        boolean updateSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .updateRecycleDataToRecord(record, recordId, newDisplayName, isCausedByRemoveGroup);
        return updateSuccess;
    }


    /**
     * 回收站需求，把文件从回收站恢复到 媒体库
     *
     * @param record 文件在媒体库的绝对路径
     * @return
     */
    public static boolean updateRecoveryRecord(Context context, Record record) {
        if (record == null) {
            DebugUtil.i(TAG, " no record , can not recovery.", true);
            return false;
        }
        DebugUtil.i(TAG, "recoveryRecordFromRecycleBin " + record, true);
        //更新record表
        boolean updateSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).updateRecoveryDataToRecord(record);

        return updateSuccess;
    }

    public static void handleSyncDataForLoginout(Context context, boolean deleteMediaData) {
        if (deleteMediaData) {
            GroupInfoManager.getInstance(context).deleteSyncedGroupRecords();
            deleteLocalSyncedDataForLogingout(context, true);
        } else {
            GroupInfoManager.getInstance(context).clearLocalSyncStatusForLogout();
            clearLocalSyncStatusForLogingout(context);
        }
    }

    //delelte all synced data for loggingout
    public static void deleteLocalSyncedDataForLogingout(Context context, boolean deleteMediaData) {
        if (deleteMediaData) {
            List<String> paths = getToDeletePaths(context);
            if ((paths != null) && (!paths.isEmpty())) {
                ArrayList<String> realDeleteList = FileUtils.deleteFileList(paths);
                if ((realDeleteList != null) && (!realDeleteList.isEmpty())) {
                    MediaDBUtils.deleteRecordsInMediaDB(context, realDeleteList);
                }
                RecordFileChangeNotify recordFileChangeNotify = new RecordFileChangeNotify();
                recordFileChangeNotify.sendAllChangeBroadCast(context);
            }
        }
        String where = "(" + COLUMN_NAME_GLOBAL_ID + " not null and " + COLUMN_NAME_GLOBAL_ID + " !='' " + ") or (" + COLUMN_NAME_FILE_ID + " not null and "
                + COLUMN_NAME_FILE_ID + " !='')";
        try {
            int deleteCount = RecorderDBUtil.getInstance(context).deleteRecordData(context, where, null);
            DebugUtil.i(TAG, "deleteSyncedDataForLogingout deleteCount : " + deleteCount, true);
        } catch (Exception e) {
            DebugUtil.e(TAG, "deleteSyncedDataForLogingout : " + e.getMessage(), e);
        }
    }

    //clear all synced status for loggingout
    public static void clearLocalSyncStatusForLogingout(Context context) {
        String where = "(" + COLUMN_NAME_GLOBAL_ID + " not null and " + COLUMN_NAME_GLOBAL_ID + " !='') or (" + COLUMN_NAME_FILE_ID + " not null and "
                + COLUMN_NAME_FILE_ID + " !='' )or " + COLUMN_NAME_SYNC_UPLOAD_STATUS + " > ? ";
        String[] whereArgs = new String[]{String.valueOf(SYNC_STATUS_DEFAULT)};
        ContentValues cv = new ContentValues();
        cv.put(COLUMN_NAME_FILE_ID, (String) null);
        cv.put(COLUMN_NAME_GLOBAL_ID, (String) null);
        cv.put(COLUMN_NAME_CLOUD_SYS_VERSION, 0);
        cv.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, (String) null);
        cv.put(COLUMN_NAME_DIRTY, RECORD_NOT_DIRTY);
        cv.put(COLUMN_NAME_DELETE, RECORD_NORMAL);
        cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_DEFAULT);
        cv.put(COLUMN_NAME_SYNC_DATE, 0);
        cv.put(COLUMN_NAME_FAIL_COUNT, 0);
        cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
        try {
            int updateCount = context.getContentResolver().update(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, cv, where, whereArgs);
            DebugUtil.i(TAG, "clearSyncStatusForLogingout deleteCount : " + updateCount, true);
        } catch (Exception e) {
            DebugUtil.e(TAG, "deleteSyncedDataForLogingout : " + e.getMessage(), e);
        }
    }

    private static List<String> getToDeletePaths(Context context) {
        String where = "(" + COLUMN_NAME_GLOBAL_ID + " not null and " + COLUMN_NAME_GLOBAL_ID + " !='' " + ") or (" + COLUMN_NAME_FILE_ID + " not null and "
                + COLUMN_NAME_FILE_ID + " !='')";
        Cursor cursor = null;
        List<String> paths = new ArrayList<>();
        try {
            cursor = context.getContentResolver().query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, null, where, null, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    String dataPath = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_NAME_DATA));
                    paths.add(dataPath);
                }
                DebugUtil.i(TAG, "getToDeletePaths: size: " + cursor.getCount(), true);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getToDeletePaths : " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return paths;
    }

    public static boolean markAsWaitingToUploadForDeleteById(long id, boolean updateUuid, boolean clearGlobalIdAndFileId) {
        ContentValues cv = new ContentValues();
        if (updateUuid) {
            cv.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
        }
        cv.put(COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
        cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
        cv.put(COLUMN_NAME_LOCAL_EDIT_STATUS, LOCAL_EDIT_COMPLET);
        cv.put(COLUMN_NAME_SYNC_DATE, 0);
        cv.put(COLUMN_NAME_FAIL_COUNT, 0);
        cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
        if (clearGlobalIdAndFileId) {
            // 清空globalId，意味着是新数据，相关的fileId、sysVersion、checkPayload都需要清空
            cv.put(COLUMN_NAME_GLOBAL_ID, (String) null);
            cv.put(COLUMN_NAME_FILE_ID, (String) null);
            cv.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, (String) null);
            cv.put(COLUMN_NAME_CLOUD_SYS_VERSION, (String) null);
        }
        String where = COLUMN_NAME_ID + "=?";
        int updateCount = BaseApplication.getAppContext().getContentResolver().update(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, cv, where, new String[]{String.valueOf(id)});
        DebugUtil.i(TAG, "markAsWaitingToUploadForDeleteById : " + id + ", updateCount: " + updateCount + ", clearGlobalIdAndFileId: " + clearGlobalIdAndFileId, true);
        if (updateCount > 0) {
            trigBackupNow();
            return true;
        }
        return false;
    }

    public static boolean markAsWaitingToUploadForUpdateById(long id, boolean clearGlobalId, boolean updateUuid) {
        ContentValues cv = new ContentValues();
        if (updateUuid) {
            cv.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
        }
        cv.put(COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY);
        cv.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_BACKUP_START);
        cv.put(COLUMN_NAME_LOCAL_EDIT_STATUS, LOCAL_EDIT_COMPLET);
        cv.put(COLUMN_NAME_SYNC_DATE, 0);
        cv.put(COLUMN_NAME_FAIL_COUNT, 0);
        cv.put(COLUMN_NAME_LAST_FAIL_TIME, 0);
        if (clearGlobalId) {
            // 清空globalId，意味着是新数据，相关的fileId、sysVersion、checkPayload都需要清空
            cv.put(COLUMN_NAME_GLOBAL_ID, (String) null);
            cv.put(COLUMN_NAME_FILE_ID, (String) null);
            cv.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, (String) null);
            cv.put(COLUMN_NAME_CLOUD_SYS_VERSION, (String) null);
        }
        String where = COLUMN_NAME_ID + "=?";
        int updateCount = BaseApplication.getAppContext().getContentResolver().update(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, cv, where, new String[]{String.valueOf(id)});
        DebugUtil.i(TAG, "markAsWaitingToUploadById : " + id + ", updateCount: " + updateCount + ", clearGlobalId: " + clearGlobalId, true);
        if (updateCount > 0) {

            return true;
        }
        return false;
    }

    public static void trigBackupNow() {
        CloudSyncAction.trigBackupNow(BaseApplication.getAppContext());
    }
}
