/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PhotoViewerApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.photoviewer

import android.content.Context
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.photoviewer.PhotoViewer.startWith
import com.photoviewer.ui.PhotoViewerActivity
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.task.ActivityTaskUtils.topActivity
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.imageload.ImageLoaderUtils.intoBigImage
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerAction
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerData

@Component(PhotoViewerAction.COMPONENT_NAME)
object PhotoViewerApi {

    private const val SCREEN_SIZE_720 = 720
    private const val SCREEN_SIZE_1080 = 1080

    @Action(PhotoViewerAction.START_WITH_BIG_IMAGE)
    @JvmStatic
    fun <T : PhotoViewerData> startWithBigImage(
        context: Context?,
        data: List<T>,
        startIndex: Int = 0,
        transitionView: ImageView? = null,
        imageViewListener: ((T?) -> Unit)? = null,
    ) {
        if (context !is AppCompatActivity) {
            return
        }
        if (context.topActivity() is PhotoViewerActivity) {
            return
        }
        val imageLoadData = data.map {
            ImageLoadData(it.src(), SCREEN_SIZE_720, SCREEN_SIZE_1080)
        }

        ImageLoaderUtils.clearMemoryCache()
        context.startWith(
            PhotoViewerInfo(
                images = data,
                imageLoader = { imageView, i ->
                    val src = imageLoadData.getOrNull(i)
                    if (src is ImageLoadData) {
                        imageView.intoBigImage(src)
                    }
                },
                startPosition = startIndex,
                targetView = transitionView,
                onPageSelected = { i ->
                    imageViewListener?.invoke(data.getOrNull(i))
                },
                releaseDataListener = { imageView, i ->
                    imageView.setImageDrawable(null)
                    imageView.setImageBitmap(null)
                    val src = imageLoadData.getOrNull(i)
                    if (src != null) {
                        ImageLoaderUtils.clearMemoryCacheByKey(src)
                    }
                },
                src = {
                    imageLoadData.getOrNull(it)?.src
                }
            ),
            PhotoViewerAction.SHOW_BIG_IMAGE_REQUEST_CODE_X
        )
    }

    @Action(PhotoViewerAction.START_WITH_MULTI_PICTURE_SELECT)
    @JvmStatic
    fun <T : PhotoViewerData> startWithMultiPictureSelect(
        context: AppCompatActivity,
        data: List<T>,
        transitionView: ImageView?,
        requestCode: Int,
        resultListener: ((T?) -> Unit)?,
    ) {
        val imageLoadData = data.map {
            ImageLoadData(it.src(), SCREEN_SIZE_720, SCREEN_SIZE_1080)
        }
        context.startWith(
            PhotoViewerInfo(
                images = data,
                imageLoader = { imageView: ImageView?, index: Int ->
                    val src = imageLoadData.getOrNull(index)
                    if (src != null) {
                        imageView?.intoBigImage(src)
                    }
                },
                targetView = transitionView,
                onResult = { result ->
                    val popPicture = result.map { it as T }.firstOrNull()
                    resultListener?.invoke(popPicture)
                },
                isSwipeToDismissAllowed = false,
                releaseDataListener = { imageView, index ->
                    imageView.setImageDrawable(null)
                    imageView.setImageBitmap(null)
                    val src = imageLoadData.getOrNull(index)
                    if (src != null) {
                        ImageLoaderUtils.clearMemoryCacheByKey(src)
                    }
                },
                src = {
                    imageLoadData.getOrNull(it)?.src
                }
            ),
            requestCode
        )
        BuryingPoint.showPhotoViewerNumber()
    }

    /**
     * 更新动画targetView
     */
    @Action(PhotoViewerAction.UPDATE_IMAGE_VIEW)
    @JvmStatic
    fun updateImageView(targetView: ImageView?) {
        PhotoViewer.INSTANCE?.targetView = targetView
    }
}