/**
 * Copyright (C), 2008-2025 OPLUS Mobile Comm Corp., Ltd.
 * File: SelectLanguageDialog
 * Description:
 * Version: 1.0
 * Date: 2025/6/4
 * Author: <EMAIL>
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 */
package com.soundrecorder.setting.setting.dialog

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.adapter.ChoiceListAdapter
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction

/**
 * 语言选择弹窗
 */
class SelectLanguageDialog(val activity: Activity) {

    companion object {
        private const val TAG = "SelectLanguageDialog"
    }
    private var mDialog: AlertDialog? = null
    var dialogItemListener: DialogItemClickListener? = null

    fun showDialog(selectedLanguage: String) {
        DebugUtil.d(TAG, "showDialog")
        RecorderViewModelAction.getSupportLanguageList{ languageCodes ->
            DebugUtil.d(TAG, "showDialog getSupportLanguageList langCodeList size=${languageCodes.size}")
            val languageNames = languageCodes.map { code ->
                LanguageUtil.getAsrLangMap(activity, listOf(code))[code] ?: code
            }.toTypedArray()
            DebugUtil.d(TAG, "showDialog languageNames=$languageNames")
            val checkboxStates = BooleanArray(languageCodes.size) { false }
            val disableStatus = BooleanArray(languageCodes.size) { false }

            val selectedPos = languageCodes.indexOf(selectedLanguage).coerceAtLeast(0)
            checkboxStates[selectedPos] = true

            val singleChoiceListAdapter = ChoiceListAdapter(
                activity,
                com.support.dialog.R.layout.coui_select_dialog_singlechoice,
                languageNames,
                null,
                checkboxStates,
                disableStatus,
                false
            )
            activity.runOnUiThread {
                mDialog = COUIAlertDialogBuilder(
                    activity,
                    com.support.dialog.R.style.COUIAlertDialog_BottomAssignment
                )
                    .setBlurBackgroundDrawable(true)
                    .setTitle(activity.getString(com.soundrecorder.common.R.string.audio_language_recognition))
                    .setAdapter(singleChoiceListAdapter) { _, which ->
                        dialogItemListener?.click(languageCodes[which])
                        release()
                    }
                    .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
                    .show()
                ViewUtils.updateWindowLayoutParams(mDialog?.window)
            }
        }
    }

    fun release() {
        mDialog?.dismiss()
        dialogItemListener = null
        mDialog = null
    }

    interface DialogItemClickListener {
        fun click(languageCode: String)
    }
}
