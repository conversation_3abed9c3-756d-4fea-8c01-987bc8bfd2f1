package com.recorder.cloudkit.push;

import android.content.Context;
import android.text.TextUtils;

import com.heytap.cloudkit.libsync.ext.CloudSyncManager;
import com.heytap.msp.push.HeytapPushManager;
import com.heytap.msp.push.callback.ICallBackResultService;
import com.heytap.msp.push.mode.ErrorCode;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;

import java.util.concurrent.Executors;

import com.recorder.cloudkit.BuildConfig;

public class CloudPushAgent {
    private static final String TAG = CloudPushAgent.class.getSimpleName();
    private static volatile boolean sPushInit = false;
    private static volatile boolean sPushRegister = false;
    //新增push注册成功，记录push的regisId，后续在
    private static volatile String sPushRegisId = null;


    public static synchronized void init(Context context) {
        /*b: isNeedLog*/
        HeytapPushManager.init(context, false);
        DebugUtil.e(TAG, "CloudPushAgent init", true);
        sPushInit = true;
    }

    public static synchronized void checkPushRegister(Context context) {
        if (!sPushInit) {
            DebugUtil.i(TAG, "register failure for push not init ", true);
            return;
        }
        if (!sPushRegister && HeytapPushManager.isSupportPush(context)) {
            register(context);
            sPushRegister = true;
        }
    }

    public static String getPushRegisId() {
        return sPushRegisId;
    }

    private static void register(Context context) {
        DebugUtil.e(TAG, "register");
        boolean isOnePlusExp = BaseUtil.isOnePlusExp();
        String appK = isOnePlusExp? BuildConfig.PUSH_APP_KEY_ONEPLUS : BuildConfig.PUSH_APP_KEY_OPPO;
        String appS = isOnePlusExp ? BuildConfig.PUSH_APP_SECRET_ONEPLUS : BuildConfig.PUSH_APP_SECRET_OPPO;
        HeytapPushManager.register(context, appK, appS, new ICallBackResultService() {
            @Override
            public void onRegister(int i, String s, String s1, String s2) {
                if (i != ErrorCode.SUCCESS) {
                    //重试机制
                    HeytapPushManager.getRegister();
                    DebugUtil.e(TAG, "push register fail " + i + "  message" + s);
                } else {
                    if (!TextUtils.isEmpty(s)) {
                        DebugUtil.e(TAG, "push register success " + s);
                        sPushRegisId = s;
                        Executors.newSingleThreadExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                CloudSyncManager.getInstance().registerPush(s);
                                DebugUtil.e(TAG, "push register CloudSyncManager " + s);
                            }
                        });
                    }
                }
            }

            @Override
            public void onUnRegister(int i, String s, String s1) {
                if (i == ErrorCode.SUCCESS) {
                    sPushRegister = false;
                    DebugUtil.e(TAG, "push unregister cancel success");
                } else {
                    DebugUtil.e(TAG, "push unregister cancel fail");
                }
            }

            @Override
            public void onSetPushTime(int i, String s) {

            }

            @Override
            public void onGetPushStatus(int i, int i1) {

            }

            @Override
            public void onGetNotificationStatus(int i, int i1) {

            }

            @Override
            public void onError(int i, String s, String s1, String s2) {
                sPushRegister = false;
                DebugUtil.e(TAG, "onError i " + i + " s " + s);
            }
        });
    }

    public static synchronized void unregister() {
        if (!sPushInit) {
            DebugUtil.i(TAG, "unregister failure for push not init", true);
            return;
        }
        DebugUtil.i(TAG, "unregister ", true);
        HeytapPushManager.unRegister();
        sPushRegister = false;
    }
}
