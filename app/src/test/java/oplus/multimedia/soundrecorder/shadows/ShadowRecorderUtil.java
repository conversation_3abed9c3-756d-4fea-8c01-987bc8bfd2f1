/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ShadowRecorderUtil.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/8/20
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/8/20, LI Kun, create
 ************************************************************/

package oplus.multimedia.soundrecorder.shadows;

import android.app.Activity;
import android.content.Context;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import oplus.multimedia.soundrecorder.utils.RecorderUtil;

@Implements(RecorderUtil.class)
public class ShadowRecorderUtil {

    private static final String TEST_PHONE_DIR = "test/phoneDir";
    private static final String TEST_SDCARD_DIR = "test/sdcardDir";

    @Implementation
    public static void enableBackgroundService(Context context) {

    }

    @Implementation
    public static boolean isAndroidROrLater() {
        //for test, Android R noe sdk_int is 29 now;
        return false;
    }


    @Implementation
    private static boolean deleteRecordFromCurrentCursor(Activity context, long rowId) {
        return false;
    }


    @Implementation
    public static boolean isSupportMultiRecordMode(Context context) {
        if (null != context) {
            return true;
        }
        return false;
    }

    @Implementation
    public static String getPhoneStorageDir(Context context) {
        if (null != context) {
            return TEST_PHONE_DIR;
        }
        return null;
    }

    @Implementation
    public static String getSDCardStorageDir(Context context) {
        if (null != context) {
            return TEST_SDCARD_DIR;
        }
        return null;
    }

    @Implementation
    public static boolean isAndroidQOrLater() {
        return true;
    }


    @Implementation
    public static boolean hasNMR1() {
        return true;
    }


}
