/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NotificationApiTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.base.BaseNotification
import com.soundrecorder.notification.common.CommonFastPlayNotification
import com.soundrecorder.notification.common.CommonPlaybackNotification
import com.soundrecorder.notification.common.CommonRecordNotification
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import com.soundrecorder.notification.third.ThirdPartyFastPlayNotification
import com.soundrecorder.notification.third.ThirdPartyPlaybackNotification
import com.soundrecorder.notification.third.ThirdPartyRecordNotification
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class NotificationApiTest {

    private var mContext: Context? = null
    private var mMockApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockApplication?.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    fun tearDown() {
        mMockApplication?.close()
        mMockApplication = null
        mContext = null
    }

    @Test
    fun should_contains_when_showNotification() {
        val mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        val page = NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        NotificationApi.showNotification(
            mode, page,
            NotificationModel()
        )
        Assert.assertNotEquals(0, NotificationApi.notificationMap.size)
    }

    @Test
    fun should_remove_when_cancelGroupOtherView() {
        val group = NotificationUtils.NOTIFICATION_PLAY_ID
        val mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        val fastPlayId = NotificationApi.getNotificationIdByModeAndPage(
            mode,
            NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        )
        val playbackId = NotificationApi.getNotificationIdByModeAndPage(
            mode,
            NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
        )
        NotificationApi.notificationMap[fastPlayId] =
            CommonFastPlayNotification(group, fastPlayId)
        NotificationApi.notificationMap[playbackId] =
            CommonFastPlayNotification(group, playbackId)
        NotificationApi.cancelGroupOtherView(group, fastPlayId)
        Assert.assertEquals(0, NotificationApi.notificationMap.size)
    }

    @Test
    fun should_equals_when_createFastPlay() {
        var mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        val page = NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        var notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        var notification: BaseNotification? = Whitebox.invokeMethod(
            NotificationApi::class.java,
            "createNotificationById",
            mode,
            page,
            notificationId
        )
        Assert.assertTrue(notification is CommonFastPlayNotification)

        mode = NotificationUtils.NOTIFICATION_MODE_THIRD
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        notification = Whitebox.invokeMethod(
            NotificationApi::class.java,
            "createNotificationById",
            mode,
            page,
            notificationId
        )
        Assert.assertTrue(notification is ThirdPartyFastPlayNotification)
    }

    @Test
    fun should_equals_when_createPlayBack() {
        var mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        val page = NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
        var notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        var notification: BaseNotification? = Whitebox.invokeMethod(
            NotificationApi::class.java,
            "createNotificationById",
            mode,
            page,
            notificationId
        )
        Assert.assertTrue(notification is CommonPlaybackNotification)

        mode = NotificationUtils.NOTIFICATION_MODE_THIRD
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        notification = Whitebox.invokeMethod(
            NotificationApi::class.java,
            "createNotificationById",
            mode,
            page,
            notificationId
        )
        Assert.assertTrue(notification is ThirdPartyPlaybackNotification)
    }

    @Test
    fun should_equals_when_createRecord() {
        var mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        val page = NotificationUtils.NOTIFICATION_PAGE_RECORD
        var notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        var notification: BaseNotification? = Whitebox.invokeMethod(
            NotificationApi::class.java,
            "createNotificationById",
            mode,
            page,
            notificationId
        )
        Assert.assertTrue(notification is CommonRecordNotification)

        mode = NotificationUtils.NOTIFICATION_MODE_THIRD
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        notification = Whitebox.invokeMethod(
            NotificationApi::class.java,
            "createNotificationById",
            mode,
            page,
            notificationId
        )
        Assert.assertTrue(notification is ThirdPartyRecordNotification)
    }

    @Test
    fun should_equals_when_getGroupIdByModeAndPage() {
        var mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        var page = NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        var group = NotificationApi.getGroupIdByModeAndPage(mode, page)
        Assert.assertEquals(NotificationUtils.NOTIFICATION_PLAY_ID, group)

        page = NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
        group = NotificationApi.getGroupIdByModeAndPage(mode, page)
        Assert.assertEquals(NotificationUtils.NOTIFICATION_PLAY_ID, group)

        page = NotificationUtils.NOTIFICATION_PAGE_EDIT_RECORD
        group = NotificationApi.getGroupIdByModeAndPage(mode, page)
        Assert.assertEquals(NotificationUtils.NOTIFICATION_PLAY_ID, group)

        page = NotificationUtils.NOTIFICATION_PAGE_RECORD
        group = NotificationApi.getGroupIdByModeAndPage(mode, page)
        Assert.assertEquals(NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID, group)

        mode = NotificationUtils.NOTIFICATION_MODE_THIRD
        page = NotificationUtils.NOTIFICATION_PAGE_RECORD
        group = NotificationApi.getGroupIdByModeAndPage(mode, page)
        Assert.assertEquals(NotificationUtils.NOTIFICATION_THIRDPARTY_RECORDE_ID, group)

        mode = -1
        page = NotificationUtils.NOTIFICATION_PAGE_RECORD
        group = NotificationApi.getGroupIdByModeAndPage(mode, page)
        Assert.assertEquals(-1, group)

        page = -1
        group = NotificationApi.getGroupIdByModeAndPage(mode, page)
        Assert.assertEquals(-1, group)
    }

    @Test
    fun should_equals_when_getNotificationIdByModeAndPage() {
        var mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        var page = NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        var notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(0, notificationId)

        page = NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(1, notificationId)

        page = NotificationUtils.NOTIFICATION_PAGE_EDIT_RECORD
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(2, notificationId)

        page = NotificationUtils.NOTIFICATION_PAGE_RECORD
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(3, notificationId)

        mode = NotificationUtils.NOTIFICATION_MODE_THIRD
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(13, notificationId)

        page = NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(10, notificationId)

        page = NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(11, notificationId)

        page = NotificationUtils.NOTIFICATION_PAGE_EDIT_RECORD
        notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        Assert.assertEquals(12, notificationId)
    }

    @Test
    fun should_equals_when_getModeAndPageByNotificationId() {
        val notificationId = 0
        val page = NotificationApi.getModeAndPageByNotificationId(notificationId)[1]
        Assert.assertEquals(NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY, page)
    }

    @Test
    fun should_equals_when_getNotificationMode() {
        var mode = NotificationApi.getNotificationMode(true)
        Assert.assertEquals(NotificationUtils.NOTIFICATION_MODE_THIRD, mode)

        mode = NotificationApi.getNotificationMode(false)
        Assert.assertEquals(NotificationUtils.NOTIFICATION_MODE_COMMON, mode)
    }

    @Test
    fun should_equals_when_cancelNotificationModeAndGroup() {
        val mode = NotificationUtils.NOTIFICATION_MODE_COMMON
        val page = NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
        val notificationId = NotificationApi.getNotificationIdByModeAndPage(mode, page)
        val group = NotificationUtils.NOTIFICATION_PLAY_ID
        NotificationApi.notificationMap[notificationId] =
            CommonPlaybackNotification(group, notificationId)
        NotificationApi.cancelNotificationModeAndGroup(
            mode,
            NotificationUtils.NOTIFICATION_PAGE_EDIT_RECORD
        )
        Assert.assertEquals(0, NotificationApi.notificationMap.size)
    }

    @Test
    fun should_equals_when_cancelAllNotification() {
        NotificationApi.notificationMap[0] =
            CommonFastPlayNotification(
                NotificationUtils.NOTIFICATION_PLAY_ID,
                0
            )
        NotificationApi.notificationMap[10] =
            ThirdPartyFastPlayNotification(
                NotificationUtils.NOTIFICATION_PLAY_ID,
                1
            )
        NotificationApi.cancelAllNotification()
        Assert.assertEquals(0, NotificationApi.notificationMap.size)
    }

    @Test
    fun should_equals_when_cancelNotification() {
        NotificationApi.notificationMap[0] =
            CommonFastPlayNotification(
                NotificationUtils.NOTIFICATION_PLAY_ID,
                0
            )
        NotificationApi.cancelNotification(
            0,
            NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        )
        Assert.assertEquals(0, NotificationApi.notificationMap.size)
    }

    @Test
    fun should_contains_when_getIntentFilter() {
        val intentFilter = NotificationApi.getIntentFilter()
        Assert.assertTrue(intentFilter.hasAction(NotificationUtils.PLAY_STATUS_CHANGED_ACTION))
    }
}