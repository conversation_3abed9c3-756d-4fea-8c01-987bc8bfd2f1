package com.recorder.cloudkit

import android.app.Application
import com.heytap.cloudkit.libcommon.config.CloudConfig
import com.heytap.cloudkit.libcommon.config.CloudEnv
import com.heytap.cloudkit.libcommon.config.CloudLogLevel
import com.heytap.cloudkit.libsync.cloudswitch.compat.CloudKitSwitchCompatUtil
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.usercenter.accountsdk.AcExtension
import com.heytap.usercenter.accountsdk.AccountAgentClient
import com.heytap.usercenter.accountsdk.AccountSDKConfig
import com.recorder.cloudkit.account.AccountBroadcastReceiver
import com.recorder.cloudkit.account.AccountCheckHelper
import com.recorder.cloudkit.account.CloudAccountAgentImpl
import com.recorder.cloudkit.account.VerifyConfirmHelper
import com.recorder.cloudkit.push.CloudPushAgent
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.SyncDataConstants.XOR_KEY
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.arms.ConfigModule
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.XORUtil

/**
 * androidManifest中配置，application初始胡
 */
class CloudSyncConfigModule : ConfigModule {
    companion object {
        /*当前区域是否支持云同步*/
        var sRegionSupportCloud = false
    }

    private val TAG = "CloudSyncConfigModule"
    private lateinit var mApplication: Application

    override fun onApplicationCreate(application: Application) {
        mApplication = application
        DebugUtil.i(TAG, "application init cloudKit")
        // 初始化PUSH，日志打捞也需要注册push，这个地方放在return之前，不然日志打捞无法实现
        CloudPushAgent.init(application)
        VerifyConfirmHelper.init()
        if (!BaseApplication.sIsMainSystem) {
            DebugUtil.e(TAG, "cloudkit can't run in clone system")
            return
        }
        initSync()
        // 该方法判断是通过上方init设置支持区域后，获取才有效，所有initSync为必须执行
        if (!CloudSynStateHelper.isRegionCloudSupport()) {
            //当前不支持云同步  拦截不做处理
            DebugUtil.e(TAG, "current area not support cloud")
            return
        }
        initAccount()
        setOldCloudSwitchCompat()
    }

    /**
     * 账号SDK初始化
     */
    private fun initAccount() {

        val  mExtension = object : AcExtension {
            override fun isForeground(): Boolean = false

            /**
             * 未登录时，调用AccountAgent.reqSignInAccount方法是否拉起登录页
             * true： 拉起登录页
             * false：不拉起登录页
             * */
            override fun isShowAcPage(): Boolean {
                DebugUtil.e(TAG, "isShowAcPage  ${AccountCheckHelper.sShowLoginPage}")
                return AccountCheckHelper.sShowLoginPage
            }
        }
        val builder = AccountSDKConfig.Builder()
            .context(mApplication)
            .extension(mExtension)
            .env(AccountSDKConfig.ENV.ENV_RELEASE)
        AccountAgentClient.get().init(builder.create())
        AccountBroadcastReceiver.register(mApplication)
    }

    /**
     * 同步sdk初始化
     * 内销：
     * 测试环境：wanyol.com
     * 预发布环境：oppomobile.com
     * 正式环境：heytapmobi.com
     *
     */
    private fun initSync() {
        val isOPSound = BaseUtil.isOnePlusExp()
        val cloudConfig = CloudConfig.Builder(mApplication)
            .setAppId(BuildConfig.CLOUDKIT_APP_ID)
            .setAppPkgId(if (isOPSound) BuildConfig.CLOUDKIT_APP_PKG_ID_ONEPLUS else BuildConfig.CLOUDKIT_APP_PKG_ID_OPPO)
            .setAppKey(if (isOPSound) BuildConfig.CLOUDKIT_APP_KEY_ONEPLUS else BuildConfig.CLOUDKIT_APP_KEY_OPPO)
            .setAppSecretKey(if (isOPSound) BuildConfig.CLOUDKIT_APP_SECRET_ONEPLUS else BuildConfig.CLOUDKIT_APP_SECRET_OPPO)
            .setHost(XORUtil.enOrDecrypt(BuildConfig.CLOUDKIT_HOST, XOR_KEY))
            .setEnv(CloudEnv.RELEASE) //云同步外销切换测试环境时需配置测试域名http请求
            //           //日志控制台输出级别，应用发布release版本得设置为LogLevel.NONE
            .setConsoleLogLevel(CloudLogLevel.LEVEL_NONE)
            .setParallelFileCount(2) //建议配置2, 并发传输文件个数配置 必须小于 MAX_PARALLEL_FILE_COUNT(4), 为了符合整机的内存峰值要求
            .setParallelSliceCount(4) //并发分片传输文件个数配置 必须小于 MAX_PARALLEL_SLICE_COUNT(4), 为了符合整机的内存峰值要求
            .setMaxWaitFileCount(200) //最大等待传输的文件个数
            .setMinAvailableLocalSpace(RecordSyncChecker.LOCAL_MIN_SPACE) //External本地剩余存储空间阈值，下载保存 发现剩余空间小于等于阀值则 下载失败，返回CloudBizError.NO_LOCAL_SPACE； 返回这个错误app可以清除非必要缓存，如果还不行则不触发下载（待更新sdk）
//            .setRepeatFileInOneDayConfig(CloudRepeatFileConfig(10, 30)) //一天重复上传下载一个文件最多成功次数和失败次数，sdk默认使用 CloudRepeatFileConfig.getDefault()
            .setFileExpiredDay(3) //过期io db任务淘汰的天数，超过则启动时候 会进行db任务清除
            .setCloudSupportRegionList(getSupportRegions())
            .build()
        CloudSyncManager.getInstance().init(cloudConfig, CloudAccountAgentImpl(mApplication)
        )
        sRegionSupportCloud = CloudSyncManager.getInstance().isRegionCloudSupport
    }

    private fun setOldCloudSwitchCompat() {
        //给CloudKit 设置查询旧有开关接口实现，这样CloudSyncManager.getSyncSwitchCompat 识别到 不支持AppSetting开关的云服务场景 则 会调用此接口实现查询开关
        CloudKitSwitchCompatUtil.setCloudOldSwitchCompat {
            val state = CloudSynStateHelper.getSwitchStateOldVersion()
            DebugUtil.i(TAG, "query cloud switch state by old:$state", true)
            state
        }
    }

    private fun getSupportRegions(): List<String> {
        val supportRegion = mutableListOf<String>()
        // 内销仅中国一个区域
        if (!FeatureOption.OPLUS_VERSION_EXP) {
            // 中国
            supportRegion.add("cn")
            return supportRegion
        }
        // 一加外销仅支持印度区域
        if (BaseUtil.isOnePlusExp()) {
            supportRegion.add("in")
            return supportRegion
        }
        //日本
        supportRegion.add("jp")
        //台湾
        supportRegion.add("tw")
        //泰国
        supportRegion.add("th")
        // 印尼
        supportRegion.add("id")
        //菲律宾
        supportRegion.add("ph")
        // 柬埔寨
        supportRegion.add("kh")
        //印度
        supportRegion.add("in")
        // 新加坡
        supportRegion.add("sg")
        //马来西亚
        supportRegion.add("my")
        //越南
        supportRegion.add("vn")
        // 亚太，再os12.1及以上os系统会将中国香港、缅甸、老挝、尼泊尔、柬埔寨 4个区域打点结果获取为apc，云服务之前是支持柬埔寨的，所以需要配置
        supportRegion.add("apc")
        return supportRegion
    }
}