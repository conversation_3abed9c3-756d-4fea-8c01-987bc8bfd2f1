/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ClickScaleImageView
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-wangyin<PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView

@SuppressLint("ClickableViewAccessibility")
internal class ClickScaleImageView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : AppCompatImageView(context, attrs) {
    companion object {
        const val DEFAULT_SCALE = 1f
        const val CLICK_SCALE = 0.85f
        const val SCALE_DURATION = 250L
    }

    init {
        setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    this.animate().scaleX(CLICK_SCALE).scaleY(CLICK_SCALE)
                        .setDuration(SCALE_DURATION).start()
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    this.animate()
                        .scaleX(DEFAULT_SCALE).scaleY(DEFAULT_SCALE).setDuration(SCALE_DURATION)
                        .start()
                }
            }
            this.onTouchEvent(event)
        }
    }
}