apply from: "../../common_build.gradle"

android {
    namespace 'com.soundrecorder.summary'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(path: ':common:libbase')
    implementation project(path: ':common:libcommon')
    implementation project(path: ':common:RecorderLogBase')
    implementation project(':common:modulerouter')
    implementation project(':component:translate')
    implementation project(':common:markwon-core')
    implementation project(':common:markwon-ext-tasklist')

    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"
    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "com.google.code.gson:gson:$gson_version"
    implementation ("com.oplus.appcompat:core:${prop_versionName}") {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation "com.oplus.appcompat:dialog:${prop_versionName}"
    implementation "com.oplus.appcompat:scrollview:${prop_versionName}"
    implementation "com.oplus.appcompat:poplist:${prop_versionName}"

    implementation "com.oplus.aiunit.open:core:${aiunit_version}"
    implementation("com.oplus.aiunit.open:toolkits:${aiunit_version}")
    implementation("com.oplus.aiunit.open:common:${aiunit_common_version}")
    implementation("com.oplus.aiunit.open:download:${aiunit_version}")
    //ai-sdk
    implementation "com.oplus.ai:summary.sdk:${ai_unified_summary}"

    implementation "com.atlassian.commonmark:commonmark:0.13.0"

    // Room
    kapt 'androidx.room:room-compiler:2.4.0'
    implementation 'androidx.room:room-runtime:2.4.0'
}