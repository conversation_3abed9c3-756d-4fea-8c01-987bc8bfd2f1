/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search.view

import android.app.Activity
import android.os.Build
import android.widget.ImageButton
import android.widget.TextView
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ConvertSearchBottomAreaTest {

    private var context: Activity? = null
    private var bottomArea: ConvertSearchBottomArea? = null

    @Before
    fun setUp() {
        context = Robolectric.buildActivity(PlaybackActivity::class.java).get()
        bottomArea = ConvertSearchBottomArea(context!!)
    }

    @After
    fun tearDown() {
        context = null
        bottomArea = null
    }

    private fun getPrevBtn(group: ConvertSearchBottomArea): ImageButton {
        val prevBtnField = Whitebox.getField(ConvertSearchBottomArea::class.java, "previousImg")
        prevBtnField.isAccessible = true
        return prevBtnField.get(group) as ImageButton
    }

    private fun getNextBtn(group: ConvertSearchBottomArea): ImageButton {
        val nextBtnField = Whitebox.getField(ConvertSearchBottomArea::class.java, "nextImg")
        nextBtnField.isAccessible = true
        return nextBtnField.get(group) as ImageButton
    }

    private fun getResultTv(group: ConvertSearchBottomArea): TextView {
        val resultTvField = Whitebox.getField(ConvertSearchBottomArea::class.java, "resultTv")
        resultTvField.isAccessible = true
        return resultTvField.get(group) as TextView
    }

    private fun getCurrentPos(group: ConvertSearchBottomArea): Int {
        val field = Whitebox.getField(ConvertSearchBottomArea::class.java, "currentPos")
        field.isAccessible = true
        return field.get(group) as Int
    }

    @Test
    fun should_not_null_when_initView() {
        bottomArea?.let {
            Whitebox.invokeMethod<Void>(it, "initView")
            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertNotNull(prevBtn)
            Assert.assertNotNull(nextBtn)
            Assert.assertNotNull(resultTv)
        }
    }

    @Test
    fun should_not_enable_when_initData() {
        bottomArea?.let {
            Whitebox.invokeMethod<Void>(it, "initData")
            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertFalse(resultTv.isEnabled)
        }
    }

    @Test
    fun should_when_initEvent() {
        bottomArea?.let {
            Whitebox.invokeMethod<Void>(it, "initEvent")
            it.setCurrentPosition(0, 3)

            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)

            prevBtn.performClick()
            var currentPos = getCurrentPos(it)
            Assert.assertEquals(0, currentPos)

            nextBtn.performClick()
            currentPos = getCurrentPos(it)
            Assert.assertEquals(1, currentPos)

            nextBtn.performClick()
            currentPos = getCurrentPos(it)
            Assert.assertEquals(2, currentPos)

            nextBtn.performClick()
            currentPos = getCurrentPos(it)
            Assert.assertEquals(2, currentPos)

            prevBtn.performClick()
            currentPos = getCurrentPos(it)
            Assert.assertEquals(1, currentPos)
        }
    }

    @Test
    fun should_return_boolean_when_isInSearch() {
        bottomArea?.let {
            it.setSearchText(null)
            var result = Whitebox.invokeMethod<Boolean>(it, "isInSearch")
            Assert.assertFalse(result)

            it.setSearchText("")
            result = Whitebox.invokeMethod<Boolean>(it, "isInSearch")
            Assert.assertFalse(result)

            it.setSearchText("测试搜索")
            result = Whitebox.invokeMethod<Boolean>(it, "isInSearch")
            Assert.assertTrue(result)
        }
    }

    /**
     * 测试没有输入搜索内容
     */
    @Test
    fun should_when_setCurrentPosition_with_no_input() {
        bottomArea?.let {
            it.setSearchText("")
            it.setCurrentPosition(0, 0)

            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertFalse(resultTv.isEnabled)

            it.setSearchText("测试内容")
            it.setCurrentPosition(0, 0)


            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertFalse(resultTv.isEnabled)
        }
    }

    /**
     * 测试没有搜索到结果
     */
    @Test
    fun should_when_setCurrentPosition_with_no_found() {
        bottomArea?.let {
            it.setSearchText("测试内容")
            it.setCurrentPosition(0, 0)

            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertFalse(resultTv.isEnabled)
        }
    }

    /**
     * 测试搜索到内容
     */
    @Test
    fun should_when_setCurrentPosition_with_found() {
        bottomArea?.let {
            // 测试搜索结果为1/5
            it.setSearchText("测试内容")
            it.setCurrentPosition(0, 5)

            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertTrue(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)

            // 测试搜索结果为1/1
            it.setSearchText("测试内容")
            it.setCurrentPosition(0, 1)
            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)


            // 测试搜索结果为2/5
            it.setSearchText("测试内容")
            it.setCurrentPosition(1, 5)
            Assert.assertTrue(prevBtn.isEnabled)
            Assert.assertTrue(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)

            // 测试搜索结果为5/5
            it.setSearchText("测试内容")
            it.setCurrentPosition(4, 5)
            Assert.assertTrue(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)
        }
    }

    /**
     * 设置有搜索内容
     */
    @Test
    fun should_when_setPositionContent() {
        bottomArea?.let {
            it.setCurrentPosition(0, 1)
            Whitebox.invokeMethod<Void>(it, "setPositionContent")

            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)

            it.setCurrentPosition(0, 5)
            Whitebox.invokeMethod<Void>(it, "setPositionContent")
            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertTrue(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)

            it.setCurrentPosition(2, 5)
            Whitebox.invokeMethod<Void>(it, "setPositionContent")
            Assert.assertTrue(prevBtn.isEnabled)
            Assert.assertTrue(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)

            it.setCurrentPosition(4, 5)
            Whitebox.invokeMethod<Void>(it, "setPositionContent")
            Assert.assertTrue(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)
        }
    }

    /**
     * 测试搜索无结果
     */
    @Test
    fun test_when_setEmptyResult() {
        bottomArea?.let {
            Whitebox.invokeMethod<Void>(it, "setEmptyResult")

            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertFalse(resultTv.isEnabled)
        }
    }

    /**
     * 测试跳转到上一个，下一个
     */
    @Test
    fun test_when_jumpPos() {
        bottomArea?.let {
            it.setSearchText("测试内容")
            it.setCurrentPosition(0, 0)
            Whitebox.invokeMethod<Void>(it, "jumpPos", 0)

            val prevBtn = getPrevBtn(it)
            val nextBtn = getNextBtn(it)
            val resultTv = getResultTv(it)

            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertFalse(resultTv.isEnabled)


            it.setSearchText("测试内容")
            it.setCurrentPosition(0, 1)
            Whitebox.invokeMethod<Void>(it, "jumpPos", 0)
            Assert.assertFalse(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)

            it.setSearchText("测试内容")
            it.setCurrentPosition(1, 5)
            Whitebox.invokeMethod<Void>(it, "jumpPos", 1)
            Assert.assertTrue(prevBtn.isEnabled)
            Assert.assertTrue(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)

            it.setSearchText("测试内容")
            it.setCurrentPosition(4, 5)
            Whitebox.invokeMethod<Void>(it, "jumpPos", 1)
            Assert.assertTrue(prevBtn.isEnabled)
            Assert.assertFalse(nextBtn.isEnabled)
            Assert.assertTrue(resultTv.isEnabled)
        }
    }
}