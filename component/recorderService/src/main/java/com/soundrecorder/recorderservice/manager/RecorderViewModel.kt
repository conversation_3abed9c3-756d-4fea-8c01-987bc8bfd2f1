/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderViewNodel
 Description:
 Version: 1.0
 Date: 2022/8/3
 Author: ********(v-zhengt<PERSON><PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/3 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.app.RecoverableSecurityException
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.IntentExt.getBooleanValue
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.common.utils.AudioNameUtils
import com.soundrecorder.common.utils.MarkSerializUtil.checkInTimeScopeInMarkList
import com.soundrecorder.common.utils.UniDirectionalRecordUtils
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.SaveFileState.ERROR
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.SaveFileState.INIT
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.SaveFileState.SHOW_LOADING_DIALOG
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.SaveFileState.START_LOADING
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.SaveFileState.SUCCESS
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.WaveState.START
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.WaveState.STOP
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.WaveState.UPDATE
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.saveFileState
import com.soundrecorder.modulerouter.summary.ISummaryCallback
import com.soundrecorder.modulerouter.summary.SummaryAction
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver
import com.soundrecorder.recorderservice.controller.observer.WaveObserver
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING
import com.soundrecorder.recorderservice.manager.RecordStatusManager.getCurrentStatus
import com.soundrecorder.recorderservice.manager.statusbar.RecordStatusBarUpdater
import com.soundrecorder.recorderservice.manager.statusbar.SeedlingSavingTimer
import com.soundrecorder.wavemark.mark.MarkHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.ref.SoftReference
import java.lang.ref.WeakReference
import java.util.concurrent.CopyOnWriteArrayList

class RecorderViewModel : ViewModel(), WaveObserver, RecordInfoSaveObserver, RecorderUIController.RecorderStateListener {

    companion object {
        private const val TAG = "RecorderViewModel"
        private const val QUICK_CLICK_INTERVAL = 700
        private const val DELAY_TIME_1000 = 1_000L
        private val VIEW_MODEL by lazy {
            val factory = ViewModelProvider.AndroidViewModelFactory.getInstance(BaseApplication.getApplication())
            ViewModelProvider(ViewModelStore(), factory)[RecorderViewModel::class.java]
        }

        /**
         *  获取RecorderViewModel对象唯一入口
         *  @return RecorderViewModel
         */
        @JvmStatic
        fun getInstance(): RecorderViewModel = VIEW_MODEL

        /**
         * 启动录音服务唯一入口
         */
        @JvmStatic
        fun startRecorderService(function: Intent.() -> Unit) {
            val intent = Intent(BaseApplication.getAppContext(), RecorderService::class.java)
            function.invoke(intent)
            getInstance().isFromOtherAppOrigin =
                intent.getBooleanValue(RecorderDataConstant.SERVICE_IS_FROM_OTHER_APP, false)
            BaseApplication.getApplication().startForegroundService(intent)
        }
    }

    var isFromOtherAppOrigin = false
    private val listeners = CopyOnWriteArrayList<WeakReference<RecorderControllerListener>>()
    private val mainHandler = Handler(Looper.getMainLooper())
    private val recorderService: RecorderService?
        get() {
            return softRecorderService?.get()
        }
    private var softRecorderService: SoftReference<RecorderService>? = null
    private var recordStatusBeforeSave: Int? = null
    private var isRecorderFinish = false

    /*语种 key:语种代码， value：获取的系统语言*/
    var asrSupportLanguageList: MutableList<String>? = null

    /*当前选中的语种*/
    var curSelectedLanguage: String = ""
        get() {
            if (TextUtils.isEmpty(field)) {
                field = PrefUtil.getString(BaseApplication.getAppContext(), PrefUtil.OPLUS_AI_TEXT_ASR_LANGUAGE, LanguageUtil.getAsrDefaultLanguage())
                DebugUtil.d(TAG, "curSelectedLanguage is empty, get from sp: $field")
            }
            DebugUtil.d(TAG, "curSelectedLanguage value: $field")
            return field
        }
        set(value) {
            if (value.isNotEmpty()) {
                field = value
                PrefUtil.putString(BaseApplication.getAppContext(), PrefUtil.OPLUS_AI_TEXT_ASR_LANGUAGE, value)
                DebugUtil.d(TAG, "curSelectedLanguage set value: $field")
            } else {
                DebugUtil.w(TAG, "curSelectedLanguage set value is empty, ignore")
            }
        }

    /*设备是否支持定向录音*/
    private val supportDirectRecording: Boolean by lazy {
        UniDirectionalRecordUtils.isSupportDirectionalRecording(BaseApplication.getAppContext())
    }

    /**
     * 记录保存前一刻录制状态
     */
    fun getRecordStatusBeforeSave(): Int? = recordStatusBeforeSave

    /**
     * 获取标记列表数据
     */
    fun getMarkData(): List<MarkDataBean> {
        return recorderService?.markDatas ?: mutableListOf()
    }

    /**
     * 获取标记最新一条数据
     */
    fun getLastMarkTime(): Long {
        return recorderService?.lastMarkTime?.value ?: 0
    }

    fun setDirectRecordingOn(isOn: Boolean) {
        recorderService?.setDirectionalRecordOn(isOn)
    }

    fun setDirectRecordTime(directTime: String?) {
        recorderService?.setDirectionalRecordTime(directTime)
    }

    fun getDirectRecordTime(): String? {
        return recorderService?.directionalRecordTime?.value
    }

    fun getDirectTimeLiveData(): MutableLiveData<String>? {
        return recorderService?.directionalRecordTime
    }

    /**
     * 是否支持定向录音
     */
    fun isSupportDirectRecording(): Boolean = supportDirectRecording

    /**
     * 定向录音开关
     * true：开启
     * null or false：关闭
     */
    fun isDirectRecodingOn(): Boolean = getDirectRecodingOnLiveData()?.value == true

    fun isDirectRecodingEnable(): Boolean = getDirectRecodingEnableLiveData()?.value == true

    fun getDirectRecodingOnLiveData(): MutableLiveData<Boolean>? = recorderService?.directionalRecordOn

    fun getDirectRecodingEnableLiveData(): MutableLiveData<Boolean>? = recorderService?.directionalRecordEnable

    fun setRecorderFinish(isFinish: Boolean) {
        this.isRecorderFinish = isFinish
    }

    fun isRecorderFinish(): Boolean {
        return isRecorderFinish
    }

    /**
     * 添加录制回调
     */
    @MainThread
    fun addListener(listener: RecorderControllerListener) {
        if (listeners.none { it.get() == listener }) {
            listeners.add(WeakReference(listener))
        }
        if (hasInitRecorderService()) {
            listener.onReadyService()
            if (isRecordSaving()) {
                listener.onSaveFileStateChange(saveFileState)
            }
        }
    }

    /**
     * 获取录制模式RecordType
     */
    fun getRecordType(): Int {
        return recorderService?.recordType ?: RecordModeConstant.RECORD_TYPE_STANDARD
    }

    /**
     * 波形model是否已经初始化
     */
    fun hasInitAmplitude(): Boolean {
        return recorderService?.recorderController?.recorderAmplitudeModel != null
    }

    /**
     * 获取波形当前时间
     */
    fun getAmplitudeCurrentTime(): Long {
        return recorderService?.recorderController?.recorderAmplitudeModel?.currentTimeMillis ?: 0
    }

    /**
     * 获取录制波形数据
     */
    fun getAmplitudeList(): List<Int> {
        return recorderService?.recorderController?.recorderAmplitudeModel?.amplitudeList ?: listOf()
    }

    /**
     *获取最新的波形数据
     */
    fun getLatestAmplitude(): Int {
        return recorderService?.recorderController?.recorderAmplitudeModel?.latestAmplitude ?: 0
    }

    /**
     * 获取最大录制波形值
     */
    fun getMaxAmplitude(): Int {
        return recorderService?.maxAmplitude ?: 0
    }

    fun getMarkEnabledLiveData(): LiveData<Boolean>? = recorderService?.markEnable

    /**
     * 判读标记按钮是置灰,包含是否是图标标记状态
     * @return true: 可以添加标记； false：不可以添加标记
     */
    fun isMarkEnabledFull(): Boolean {
        return recorderService?.markEnable?.value != false
    }

    /**
     * 标记超过最大数
     */
    fun checkMarkDataMoreThanMax(): Boolean {
        return getMarkData().size >= MarkHelper.MAX_MARKER_COUNT
    }

    fun checkMarkDataDuplicated(): Boolean {
        return checkInTimeScopeInMarkList(getMarkData().toMutableList(), getAmplitudeCurrentTime()) >= 0
    }

    /**
     * 快捷录音拒绝通知权限flag
     */
    fun isNeedShowNotificationPermissionDeniedSnackBar(): Boolean {
        return recorderService?.isNeedShowNotificationPermissionDeniedSnackBar ?: false
    }

    /**
     * 重置快捷录音拒绝通知权限flag
     */
    fun resetNeedShowNotificationPermissionDeniedSnackBar() {
        recorderService?.resetNeedShowNotificationPermissionDeniedSnackBar()
    }

    /**
     * 开始录音
     */
    fun start() {
        recorderService?.start()
    }

    /**
     * 继续录制
     */
    fun resume() {
        recorderService?.resume()
    }

    /**
     * 暂停录制
     */
    fun pause() {
        recorderService?.pause()
    }

    /**
     * 取消录制
     */
    fun cancel() {
        recorderService?.cancel()
    }

    /**
     * 停止录制
     */
    fun stop(): String? {
        return recorderService?.stop()
    }

    /**
     * 取消通知
     */
    fun cancelRecordNotification() {
        recorderService?.cancelRecordNotification()
    }

    /**
     * 获取录制文件Uri
     */
    fun getSampleUri(): Uri? {
        return recorderService?.sampleUri
    }


    /**
     * 获取录制文件后缀
     */
    fun getSuffix(): String? {
        return recorderService?.suffix
    }

    /**
     * 获取录制文件绝对路径
     */
    fun getRecordFilePath(): String? {
        return recorderService?.recordFilePath
    }

    /**
     * 获取录制文件相对路径
     */
    fun getRelativePath(): String? {
        return recorderService?.relativePath
    }

    /**
     * 获取当录制文件默认名称
     */
    fun getSampleDisplayName(genNewNameWhenSampleNull: Boolean = true): String {
        if (recorderService?.directionalRecordOn?.value == true) {
            return AudioNameUtils.genDirectRecordingName(recorderService?.suffix)
        }
        return recorderService?.sampleFileName ?: (if (genNewNameWhenSampleNull) {
            recorderService?.defaultDisplayName ?: ""
        } else {
            ""
        })
    }

    /**
     * 获取录制模式名称，如：标准模式/采访模式
     */
    fun getRecordModeName(): String {
        return AudioNameUtils.getRecordModeName(recorderService?.recordType ?: -1)
    }

    fun getFileBeingRecorded(): String? {
        return recorderService?.mFileBeingRecorded
    }

    fun setFileBeingRecorded(fileBeingRecorded: String?) {
        recorderService?.mFileBeingRecorded = fileBeingRecorded
        if (fileBeingRecorded != null) {
            setRecordName(fileBeingRecorded)
        }
    }

    /**
     * 判断录音时长是否小于700
     */
    fun isQuickRecord(): Boolean {
        if (!hasInitRecorderService()) {
            DebugUtil.i(TAG, "isQuickRecord, mRecorderService = null, return")
            return true
        }
        if (getAmplitudeCurrentTime() < QUICK_CLICK_INTERVAL) {
            DebugUtil.i(TAG, "isQuickRecord, mRecorderService.getTime() < QUICK_CLICK_INTERVAL, return")
            return true
        }
        return false
    }

    /**
     * 保存录音文件
     */
    fun saveRecordInfo(displayName: String? = null, originalDisplayName: String? = null, saveRecordFromWhere: Int, needStopService: Boolean = true) {
        if (saveFileState != INIT) return
        if (isQuickRecord()) return
        val service = recorderService ?: return
        recordStatusBeforeSave = getCurrentStatus()
        if (recordStatusBeforeSave == RECORDING) {
            service.pause()
        }
        service.saveRecordInfo(displayName ?: getSampleDisplayName(), originalDisplayName ?: "", needStopService, saveRecordFromWhere)
    }

    /**
     * 判断录制服务是否存在
     */
    fun hasInitRecorderService(): Boolean {
        return recorderService != null
    }

    /**
     * 停止录音服务
     */
    fun stopService() {
        recorderService?.stopService()
    }

    /**
     * 录音服务新建时注册回调
     */
    fun onServiceCreate(recorderService: RecorderService) {
        addListener(RecordStatusBarUpdater)
        softRecorderService = SoftReference(recorderService)
        onReadyService()
        recorderService.registerRecorderControlObservers(this, this)
        recorderService.setRecorderStateListener(this)
        initDirectRecording()
    }

    fun onDirectRecordingOffByMicChanged() {
        forEach(true) { onDirectRecordingOffByMicChanged() }
    }

    private fun initDirectRecording() {
        if (supportDirectRecording) {
            setDirectRecordingOn(false)
            setDirectRecordTime(null)
            recorderService?.setDirectionalRecordEnable(AudioModeChangeManager.isPhoneMic())
        }
    }

    /**
     * 录音服务销毁清除数据,只有RecorderService使用
     */
    fun onServiceDestroy() {
        softRecorderService = null
        saveFileState = INIT
        onCloseService()
        removeListener(RecordStatusBarUpdater)
        recordStatusBeforeSave = null
    }

    /**
     * 取消录制相关回调
     */
    @MainThread
    fun removeListener(listener: RecorderControllerListener) {
        listeners.removeIf {
            val a = it.get()
            (a == null) || (a == listener)
        }
    }

    /**
     * 开始采集波形数据
     */
    override fun onStartSample() {
        forEach { onWaveStateChange(START) }
        recorderService?.refreshMarkEnabled()
    }

    /**
     * 停止采集波形数据
     */
    override fun onStopSample() {
        recorderService?.refreshMarkEnabled()
        forEach { onWaveStateChange(STOP) }
    }

    /**
     * 刷新波形数据
     */
    override fun onUpdateWave() {
        forEach { onWaveStateChange(UPDATE) }
    }

    /**
     * 保存录音loading
     */
    override fun updateLoadingCallback(name: String?) {
        saveFileState = START_LOADING
        recorderService?.saveState?.postValueSafe(START_LOADING)
        forEach { onSaveFileStateChange(START_LOADING, name ?: "") }
        mainHandler.postDelayed({
            saveFileState = SHOW_LOADING_DIALOG
            recorderService?.saveState?.postValueSafe(SHOW_LOADING_DIALOG)
            forEach { onSaveFileStateChange(SHOW_LOADING_DIALOG, name ?: "") }
        }, SHOW_LOADING_DIALOG as Any, DELAY_TIME_1000)
    }

    /**
     * 保存录音成功回调
     */
    override fun saveDialogCallback(name: String?, fullPath: String?, saveRecordFromWhere: Int) {
        cancelShowLoadingDialogJob()
        saveFileState = SUCCESS
        recorderService?.saveState?.postValueSafe(SUCCESS)
        forEach { onSaveFileStateChange(SUCCESS, name ?: "", fullPath) }
        /*从通知入口点击保存，保存成功后toast提示*/
        if (saveRecordFromWhere == RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_NOTIFICATION && !TextUtils.isEmpty(name)) {
            val context = BaseApplication.getAppContext()
            ToastManager.showShortToast(
                context,
                context.getString(com.soundrecorder.common.R.string.recorder_saved, name.title())
            )
        }
    }

    /**
     * 保存录音失败回调
     */
    override fun deleteUriInMediaDBWhenException(name: String?, e: RecoverableSecurityException?) {
        cancelShowLoadingDialogJob()
        saveFileState = ERROR
        recorderService?.saveState?.postValueSafe(ERROR)
        forEach { onSaveFileStateChange(ERROR, name ?: "", e = e) }
    }

    /**
     * 取消保存加载弹窗时机回调任务
     */
    private fun cancelShowLoadingDialogJob() {
        mainHandler.removeCallbacksAndMessages(SHOW_LOADING_DIALOG)
    }

    /**
     * 录音状态监听回调
     */
    override fun onRecordStatusChange(state: Int) {
        DebugUtil.i(TAG, "onRecordStatusChange = $state")
        forEach { onRecordStatusChange(state) }
    }

    override fun onRecordCallConnected() {
        DebugUtil.i(TAG, "onRecordCallConnected")
        forEach { onRecordCallConnected() }
    }

    /**
     * 切换录制状态
     */
    fun switchRecorderStatus(from: String) {
        if (hasInitRecorderService()) {
            if ((getCurrentStatus() == RECORDING) && isQuickRecord()) {
                return
            }
            if (saveFileState != INIT) {
                DebugUtil.w(TAG, "switchRecorderStatus, 正在保存文件中，无法切换录制状态; currentStatus = ${getCurrentStatus()}, saveFileState = $saveFileState")
                return
            }
            recorderService?.handleForFastRecord(from)
        } else {
            DebugUtil.i(TAG, "recorderService not init")
        }
    }

    /**
     * 标记行为和异常监听
     */
    fun onMarkDataChange(markAction: Int, errorOrIndex: Int) {
        forEach { onMarkDataChange(markAction, errorOrIndex) }
    }

    /**
     * 添加单个标记
     */
    fun addMark(mark: MarkMetaData) {
        recorderService?.addMark(false, mark)
    }

    /**
     * 添加多张图片标记
     * @return Int
     */
    fun addMultiPictureMark(marks: ArrayList<MarkMetaData>): Int {
        return recorderService?.addMultiPictureMark(marks) ?: -1
    }

    /**
     * 删除标记
     */
    fun removeMark(index: Int) {
        recorderService?.removeMark(index)
    }

    /**
     * 标记重命名
     * @return Boolean
     */
    fun renameMark(newText: String, index: Int): Boolean {
        return recorderService?.renameMark(newText, index) ?: false
    }

    /**
     * 录制服务绑定
     */
    private fun onReadyService() {
        forEach(true) { onReadyService() }
    }

    /**
     * 录制服务结束
     */
    private fun onCloseService() {
        forEach { onCloseService() }
    }

    /**
     * 是否是侧边栏启动的录制服务
     * @return Boolean
     */
    fun isFromSlidBar(): Boolean {
        return recorderService?.otherConfig?.isFromSlidBar() == true
    }

    /**
     * 是否是小布启动的录制服务
     * @return Boolean
     */
    fun isFromBreno(): Boolean {
        return recorderService?.otherConfig?.isFromBreno() == true
    }

    /**
     * 是否是蜻蜓副屏启动的录制服务
     * @return Boolean
     */
    fun isFromAppCard(): Boolean {
        return recorderService?.otherConfig?.isFromAppCard() == true
    }

    /**
     * 是否是负一屏启动的录制服务
     */
    fun isFromSmallCard(): Boolean {
        return recorderService?.otherConfig?.isFromSmallCard() == true
    }
    /**
     * 是否是负一屏miniAPP启动的录制服务
     */
    fun isFromMiniApp(): Boolean {
        return recorderService?.otherConfig?.isFromMiniApp() == true
    }

    fun isStartServiceFromOtherApp(): Boolean {
        return isFromSlidBar() || isFromBreno() || isFromAppCard() || isFromSmallCard() || isFromMiniApp() || isFromCubeButton() || isFromLockScreen()
    }

    fun isFromOtherApp(): Boolean {
        return recorderService?.otherConfig?.isFromOtherApp == true
    }

    /**
     * 是否是魔方按键启动的录制服务
     */
    fun isFromCubeButton(): Boolean {
        return recorderService?.otherConfig?.isFromCubeButton() == true
    }

    /**
     * 是否是锁屏启动的录制服务
     */
    fun isFromLockScreen(): Boolean {
        return recorderService?.otherConfig?.isFromLockScreen() == true
    }

    /**
     * RecorderControllerListener回调分发
     */
    private fun forEach(forceHandle: Boolean = false, function: RecorderControllerListener.() -> Unit) {
        DebugUtil.d(TAG, "forEach listeners : ${listeners.size}")
        if (!forceHandle && Looper.getMainLooper() == Looper.myLooper()) {
            listeners.forEach {
                it.get()?.function()
            }
        } else {
            mainHandler.post {
                listeners.forEach {
                    it.get()?.function()
                }
            }
        }
    }

    /**
     * 通知栏图片标记监听
     */
    fun addSourceForNotificationBtnDisabled(addPictureMarking: MutableLiveData<Boolean>) {
        recorderService?.addSourceForNotificationBtnDisabled(addPictureMarking)
    }

    /**
     * 获取智能图片标记loading状态
     */
    fun isDoMultiPictureMarkLoading(): Boolean {
        return recorderService?.isDoMultiPictureMarkLoading ?: false
    }

    /**
     * 标记智能图片标记loading状态
     */
    fun setDoMultiPictureMarkLoading(doMultiPictureMarkLoading: Boolean) {
        recorderService?.isDoMultiPictureMarkLoading = doMultiPictureMarkLoading
    }

    fun onConfigurationChanged() {
        forEach {
            onConfigurationChanged()
        }
    }

    /**
     * 是否录制正在保存中
     * @return true:保存中 false：非保存中
     */
    fun isRecordSaving(): Boolean = saveFileState == START_LOADING || saveFileState == SHOW_LOADING_DIALOG

    /**
     * 当前正在录制状态，且不包含正在保存中的录制
     */
    fun isAlreadyRecordingExceptSaving(): Boolean {
        return RecordStatusManager.isAlreadyRecording() && (saveFileState == INIT)
    }

    /**
     * 当前正在录制状态且不包含正在保存中的录制，或者保存成功的状态
     */
    fun isAlreadyRecordingExceptSavingOrSaveSuccess(): Boolean {
        return RecordStatusManager.isAlreadyRecording() && (saveFileState == INIT) || saveFileState == SUCCESS
    }

    /**
     * 当前正在录制状态，或者正在保存中、保存成功、保存失败的录制
     */
    fun isRecordingOrSaving(): Boolean {
        if (saveFileState != INIT) {
            return true
        }
        return RecordStatusManager.isAlreadyRecording()
    }

    /**
     * 当前是保存成功或者保存失败的状态
     */
    fun isAlreadySaveOrSaveFail(): Boolean = saveFileState == SUCCESS || saveFileState == ERROR

    private fun setRecordName(recordName: String) {
        DebugUtil.i(TAG, "setRecordName")
        forEach { onRecordNameSet(recordName) }
    }

    fun doStartSummary(delayMill: Long = 0L) {
        val uri = getSampleUri()
        val markDataList = mutableListOf<MarkDataBean>().apply {
            addAll(getMarkData())
        }
        viewModelScope.launch(Dispatchers.IO) {
            delay(delayMill)
            MediaDBUtils.getRecordFromMediaByUriId(uri)?.let {
                it.checkMd5(uri)
                SummaryAction.registerSummaryCallback(it.id, object : ISummaryCallback {
                    override fun onRecordPreCheckResult(from: String, code: Int) {
                        SummaryAction.unregisterSummaryCallback(this)
                        /*由于可设置多个callback，增加from判断*/
                        if (SummaryStaticUtil.EVENT_FROM_RECORD == from) {
                            SummaryStaticUtil.addClickStartSummaryEvent(from, code)
                        }
                    }
                })
                SummaryAction.startSummary<Record, Any, MarkDataBean>(
                    SummaryStaticUtil.EVENT_FROM_RECORD, it, null, markDataList)
            }
        }
    }

    /**
     * 获取保存进度值（预估值）。
     */
    fun getSaveProgressValue(): Int {
        return if (SeedlingSavingTimer.processValue == -1) {
            0
        } else {
            SeedlingSavingTimer.processValue
        }
    }

    fun changeAsrLanguage(language: String) {
        recorderService?.changeAsrLanguage(language)
    }

    fun registerRtAsrListener(listener: OnRealtimeListener) {
        recorderService?.registerRtAsrListener(listener)
    }

    fun unregisterRtAsrListener(listener: OnRealtimeListener) {
        recorderService?.unregisterRtAsrListener(listener)
    }

    fun startTranslationConfig() {
        recorderService?.startTranslationConfig()
    }

    fun getAllAsrContent(): List<ConvertContentItem> {
        return recorderService?.allAsrContent ?: mutableListOf()
    }

    fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        return recorderService?.realtimeAsrStatus ?: RealTimeAsrStatus.ASR_DEFAULT
    }

    fun externalInitAsr() {
        recorderService?.externalInitAsr()
    }

    fun getMainRecorderCurrentTime(): Long {
        return recorderService?.getRecordCurrentTime() ?: 0
    }

    fun getSupportLanguageList(callback: (List<String>) -> Unit) {
        asrSupportLanguageList?.let {
            DebugUtil.d(TAG, "getSupportLanguageList from cache result=${it.size} list=$it")
            callback(it)
        } ?: AIAsrManagerAction.getSupportLanguage { langMap ->
            langMap?.let {
                DebugUtil.d(TAG, "getSupportLanguageList from server result=${it.size} list=$it")
                asrSupportLanguageList = it.keys.toMutableList()
                callback(it.keys.toList())
            }
        }
    }
}
