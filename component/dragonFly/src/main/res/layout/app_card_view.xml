<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:forceDarkAllowed="false"
    tools:ignore="SpUsage,ContentDescription"
    tools:targetApi="q">

    <LinearLayout
        android:id="@+id/layoutTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvRecorderName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginTop="@dimen/dp12"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp12"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#D9000000"
            android:textFontWeight="700"
            android:textSize="20dp"
            tools:contentDescription="录音"
            tools:text="录音" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/waveLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp100"
        android:layout_marginTop="@dimen/dp90"
        android:layoutDirection="ltr"
        app:layout_constraintTop_toTopOf="parent">

        <com.soundrecorder.dragonfly.view.wave.WaveRecyclerView
            android:id="@+id/waveRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:ignore="SpeakableTextPresentCheck" />

        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/app_card_shape_wave_gradient_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.3" />

        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/app_card_shape_wave_gradient_center_right"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5" />

        <View
            android:layout_width="@dimen/px2"
            android:layout_height="match_parent"
            android:background="@drawable/app_card_center_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/app_card_shape_wave_gradient_right"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintWidth_percent="0.3" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvTimeText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_marginTop="26dp"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:lines="1"
        android:textFontWeight="700"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        android:textSize="30dp"
        app:layout_constraintTop_toBottomOf="@+id/waveLayout"
        tools:text="00:00.00" />

    <TextView
        android:id="@+id/tvStateText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="center_horizontal|top"
        android:includeFontPadding="false"
        android:lines="2"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeText"
        tools:contentDescription="开始录音"
        tools:text="开始录音" />

    <com.soundrecorder.dragonfly.view.button.AppCardButton
        android:id="@+id/btnAddTextMark"
        style="@style/Borderless"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="32dp"
        android:alpha="0"
        android:drawableStart="@drawable/ic_mark_normal"
        android:drawablePadding="@dimen/dp12"
        android:elevation="0dp"
        android:textSize="0sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:contentDescription="添加标记" />

    <com.soundrecorder.dragonfly.view.button.AppCardButton
        android:id="@+id/btnSaveFile"
        style="@style/Borderless"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="32dp"
        android:alpha="0"
        android:drawableStart="@drawable/ic_save_normal"
        android:drawablePadding="@dimen/dp12"
        android:elevation="0dp"
        android:textSize="0sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:contentDescription="开始录制" />

    <com.soundrecorder.dragonfly.view.button.AppCardButton
        android:id="@+id/btnSwitchState"
        style="@style/Fill"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="32dp"
        android:drawableStart="@drawable/ic_record_init"
        android:drawablePadding="@dimen/dp12"
        android:elevation="@dimen/dp1"
        android:includeFontPadding="false"
        android:textSize="0sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:contentDescription="保存" />
</androidx.constraintlayout.widget.ConstraintLayout>
