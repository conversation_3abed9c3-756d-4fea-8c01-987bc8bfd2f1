/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CloudGlobalAction
 * Description:
 * Version: 1.0
 * Date: 2024/11/29
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/11/29 1.0 create
 */

package com.soundrecorder.modulerouter.cloudkit

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object CloudGlobalAction {
    const val COMPONENT_NAME = "CloudGlobalAction"

    const val ACTION_INIT_GLOBAL_STATE = "action_initCloudGlobalState"
    const val ACTION_JUDGE_OCLOUD_GLOBAL_STATE = "action_getOCloudGlobalState"
    const val ACTION_REG_GLOBAL_STATE_CALLBACK = "action_registerGlobalStateCallback"
    const val ACTION_UNREG_GLOBAL_STATE_CALLBACK = "action_unRegisterGlobalStateCallback"
    const val ACTION_SHOW_GLOBAL_DISABLE_DIALOG = "action_show_global_disable_dialog"
    const val ACTION_SHOW_GLOBAL_LOADING_DIALOG = "action_show_global_loading_dialog"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun getCloudGlobalState(showErrorTip: Boolean, callback: ICloudGlobalStateCallBack?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_JUDGE_OCLOUD_GLOBAL_STATE)
                    .param(showErrorTip, callback)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun initCloudGlobalState(callback: ICloudGlobalStateCallBack?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_INIT_GLOBAL_STATE)
                    .param(callback)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun registerGlobalStateCallback(callback: ICloudGlobalStateCallBack) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_REG_GLOBAL_STATE_CALLBACK)
                    .param(callback)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun unregisterGlobalStateCallback(callback: ICloudGlobalStateCallBack) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_UNREG_GLOBAL_STATE_CALLBACK)
                    .param(callback)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun showGlobalDisableDialog(activity: Activity?, state: String?, buttonListener: (() -> Unit)? = null): AlertDialog? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_SHOW_GLOBAL_DISABLE_DIALOG)
                    .param(activity, state, buttonListener)
                    .build()
            return OStitch.execute<AlertDialog>(apiRequest).result
        }

        return null
    }

    @JvmStatic
    fun showGlobalLoadingDialog(activity: Activity?): AlertDialog? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_SHOW_GLOBAL_LOADING_DIALOG)
                    .param(activity)
                    .build()
            return OStitch.execute<AlertDialog>(apiRequest).result
        }
        return null
    }
}

interface ICloudGlobalStateCallBack {
    fun onSuccess(changed: Boolean, support: Boolean, state: String?) {}

    fun onFailure() {}
}