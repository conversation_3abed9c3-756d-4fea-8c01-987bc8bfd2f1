/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertThreadManageAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.convertService

import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object ConvertThreadManageAction {
    const val COMPONENT_NAME = "ConvertThreadManage"

    const val ACTION_CANCEL_ALL_TASK = "cancelAllTask"

    const val ACTION_CHECK_IS_TASK_RUNNING = "checkIsTaskRunning"

    const val ACTION_CANCEL_CONVERT = "cancelConvert"

    const val ACTION_CHECK_CAN_ADD_NEW_TASK = "checkCanAddNewTask"

    object ConvertTaskStatus {
        const val ALREADY_RUNNING = 1
        const val OVER_LIMIT = 2
        const val CAN_ADD_NEW = 3
    }

    const val ACTION_UN_REGISTER_CALLBACK = "unregisterCallback"
    const val ACTION_START_OR_RESUME_CONVERT = "startOrResumeConvert"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun cancelAllTask() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CANCEL_ALL_TASK).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun cancelConvert(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CANCEL_CONVERT)
                    .param(mediaId)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun checkIsTaskRunning(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_IS_TASK_RUNNING)
                    .param(mediaId)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun checkCanAddNewTask(mediaId: Long): Int {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_CAN_ADD_NEW_TASK)
                    .param(mediaId)
                    .build()
            return OStitch.execute<Int>(apiRequest).result ?: ConvertTaskStatus.CAN_ADD_NEW
        }
        return ConvertTaskStatus.CAN_ADD_NEW
    }

    @JvmStatic
    fun startOrResumeConvert(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_START_OR_RESUME_CONVERT)
                    .param(mediaId, convertAbilityType, convertAiTitle)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }
}