/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IRealTimeAsrController
 * Description:
 * Version: 1.0
 * Date: 2025/4/1
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/4/1 1.0 create
 */

package com.soundrecorder.recorderservice.asr

import android.net.Uri
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus

interface IRealTimeAsrController {

    companion object {
        const val ERROR_CODE_INIT_ERROR = -1000
        const val ERROR_CODE_NET_ERROR = -1001
        const val ERROR_CODE_SERVER_ERROR = -1002
        const val SUCCESS_CODE = 0
    }

    fun initHandlerThread()

    fun setAsrResultCallback(callback: IRealTimeAsrResultCallback)
    fun startRecord()
    fun pauseRecord()

    fun resumeRecord()

    fun doStopRecord()

    fun cancelRecord()

    fun saveFile(displayName: String, uri: Uri?)
    fun releaseAll()

    fun isError(): Boolean

    fun changeAsrLanguage(language: String)

    fun registerRtAsrListener(listener: OnRealtimeListener)

    fun unregisterRtAsrListener(listener: OnRealtimeListener)

    fun startTranslationConfig()

    fun getAllAsrContent(): List<ConvertContentItem>?

    fun getRealtimeAsrStatus(): RealTimeAsrStatus

    fun externalInitAsr()

    fun externalStopAsr()

    /**
     * 获取ASR是否中断过
     * @return true: ASR中断过 false: ASR未中断过
     */
    fun isASRInterrupted(): Boolean
}

interface IRealTimeAsrResultCallback {
    fun onResult(convert: ConvertRecord, data: List<ConvertContentItem>)

    fun onError(originErrorCode: Int, formatErrorCode: Int, data: List<ConvertContentItem>?)
}