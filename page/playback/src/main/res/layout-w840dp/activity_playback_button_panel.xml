<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/coui_color_white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll_seekbar_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:clipChildren="false"
        android:layout_marginTop="@dimen/play_bottom_seekbar_top"
        android:layout_marginBottom="@dimen/play_seekbar_margin_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_current"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!--<include layout="@layout/layout_playback_seekbar" />-->

    </LinearLayout>

    <TextView
        android:id="@+id/tv_current"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/seekbar_time_margin"
        android:layout_marginBottom="@dimen/playback_tv_duration_marginBottom"
        android:contentDescription=""
        android:gravity="start|center_vertical"
        android:textColor="@color/coui_color_secondary_neutral"
        android:textSize="@dimen/seekbar_time_text_size"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        app:layout_constraintBottom_toTopOf="@id/middle_control"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="00:06" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/seekbar_time_margin"
        android:layout_marginEnd="@dimen/seekbar_time_margin"
        android:layout_marginBottom="@dimen/playback_tv_duration_marginBottom"
        android:contentDescription=""
        android:gravity="end|center_vertical"
        android:textColor="@color/coui_color_secondary_neutral"
        android:textSize="@dimen/seekbar_time_text_size"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        app:layout_constraintBottom_toTopOf="@id/middle_control"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="04:02" />
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/tool_center_guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5"/>

    <RelativeLayout
        android:id="@+id/middle_control"
        android:layout_width="@dimen/circle_record_button_diam"
        android:layout_height="@dimen/circle_record_button_diam"
        android:layout_marginBottom="@dimen/circle_playback_button_margin_bottom"
        android:layout_marginStart="@dimen/play_control_button_marginStart_activity"
        android:layout_marginEnd="@dimen/play_btn_control_marginHorizontal"
        android:importantForAccessibility="no"
        android:transitionName="sharedControlView"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tool_center_guide_line"
        app:layout_constraintEnd_toEndOf="parent">

        <com.soundrecorder.playback.view.PlaybackAnimatedCircleButton
            android:id="@+id/red_circle_icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_pause_icon"
            android:transitionName="sharedRedCircleView"
            app:circle_color="@color/coui_color_container_theme_red"
            app:circle_radius="@dimen/circle_record_button_radius" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/img_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/play_btn_control_marginHorizontal"
        android:background="@drawable/sub_control_item_bg"
        android:src="@drawable/ic_play_setting_off"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/middle_control" />

    <ImageView
        android:id="@+id/img_backward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/sub_control_item_bg"
        android:contentDescription="@string/back_three_seconds"
        android:src="@drawable/ic_backward"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintEnd_toStartOf="@id/middle_control"
        app:layout_constraintStart_toEndOf="@id/img_trim"
        app:layout_constraintTop_toTopOf="@id/middle_control" />

    <ImageView
        android:id="@+id/img_forward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/sub_control_item_bg"
        android:contentDescription="@string/fast_three_seconds"
        android:src="@drawable/ic_forward"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintEnd_toStartOf="@id/img_speed"
        app:layout_constraintStart_toEndOf="@id/middle_control"
        app:layout_constraintTop_toTopOf="@id/middle_control" />

    <ImageView
        android:id="@+id/img_trim"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/play_control_button_marginStart_activity"
        android:background="@drawable/sub_control_item_bg"
        android:contentDescription="@string/cut"
        android:src="@drawable/ic_cut_icon"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintStart_toStartOf="@id/tool_center_guide_line"
        app:layout_constraintTop_toTopOf="@id/middle_control" />

    <include
        layout="@layout/include_activity_bottom_button" />
</androidx.constraintlayout.widget.ConstraintLayout>


