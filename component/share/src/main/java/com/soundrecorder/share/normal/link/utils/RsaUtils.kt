/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - RsaUtils.kt
 ** Description: RsaUtils.
 ** Version: 1.0
 ** Date : 2025/3/13
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/13    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.utils

import android.annotation.SuppressLint
import java.io.ByteArrayOutputStream
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.PublicKey
import java.security.spec.X509EncodedKeySpec
import java.util.Base64
import javax.crypto.Cipher

object RsaUtils {
    private const val KEY_ALGORITHM = "RSA"
    private const val ALGORITHMS = "RSA/None/OAEPPadding"
    private const val PROVIDER = "BC"
    private const val MAX_ENCRYPT_BLOCK = 245

    /**
     * 公钥加密
     *
     * @param data       源数据
     * @param publicKey  公钥
     */
    @JvmStatic
    fun encryptByPublicKeyEncodeBase64(data: String, publicKey: String): String {
        val encryptedData = encryptByPublicKey(data.toByteArray(StandardCharsets.UTF_8), publicKey)
        return Base64.getEncoder().encodeToString(encryptedData)
    }

    /**
     * 公钥加密
     *
     * @param data      源数据
     * @param publicKey 公钥(BASE64编码)
     */
    @JvmStatic
    @SuppressLint("DeprecatedProvider")
    fun encryptByPublicKey(data: ByteArray, publicKey: String): ByteArray {
        val publicK = getPublicK(publicKey)
        val cipher = Cipher.getInstance(ALGORITHMS, PROVIDER)
        cipher.init(Cipher.ENCRYPT_MODE, publicK)
        // 分段进行加密操作
        return encryptAndDecryptOfSubsection(data, cipher, MAX_ENCRYPT_BLOCK)
    }

    @JvmStatic
    private fun getPublicK(publicKey: String): PublicKey {
        val encodedKeySpec = X509EncodedKeySpec(Base64.getDecoder().decode(publicKey))
        return KeyFactory.getInstance(KEY_ALGORITHM).generatePublic(encodedKeySpec)
    }

    @JvmStatic
    private fun encryptAndDecryptOfSubsection(data: ByteArray, cipher: Cipher, encryptBlock: Int): ByteArray {
        val inputLen = data.size
        val out = ByteArrayOutputStream()
        var offSet = 0
        var i = 0
        while (inputLen - offSet > 0) {
            val cache = if (inputLen - offSet > encryptBlock) {
                cipher.doFinal(data, offSet, encryptBlock)
            } else {
                cipher.doFinal(data, offSet, inputLen - offSet)
            }
            out.write(cache, 0, cache.size)
            ++i
            offSet = i * encryptBlock
        }
        out.close()
        return out.toByteArray()
    }
}