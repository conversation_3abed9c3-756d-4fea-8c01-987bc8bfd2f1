/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FileAsrManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/21
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/21 1.0 create
 */

package com.soundrecorder.translate.asr

import android.os.Bundle
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.translate.asr.file.AIFileAsrClient
import com.soundrecorder.translate.asr.listener.IFileAsrCallBack
import com.soundrecorder.translate.asr.listener.IFileAsrListener

class FileAsrManager(val mediaId: Long, private var asrCallback: IFileAsrCallBack?) {
    companion object {
        private val asrClient: AIFileAsrClient by lazy {
            AIFileAsrClient(BaseApplication.getAppContext())
        }
    }

    private val logTag = "FileConvertManager"
    private var asrListener: IFileAsrListener? = null

    init {
        if (asrListener == null) {
            asrListener = object : IFileAsrListener {
                override fun onStatus(code: Int?, msg: String?) {
                    asrCallback?.onStatus(code, msg)
                }

                override fun onAsrResult(result: String?) {
                    asrCallback?.onAsrResult(result)
                }
            }
        }
        asrClient.registerAsrListener(mediaId, asrListener)
    }

    fun initAsr(params: Bundle?, initResult: ((result: Boolean) -> Unit)) {
        runCatching {
            asrClient.initAsr(params, initResult)
        }.onFailure {
            DebugUtil.e(logTag, "initAsr error $it")
        }
    }

    fun startAsr(params: Bundle?) {
        runCatching {
            asrClient.startAsr(params)
        }.onFailure {
            DebugUtil.e(logTag, "startAsr error $it")
        }
        asrCallback?.onAsrStart()
    }

    fun stopAsr(mediaId: Long) {
        runCatching {
            asrClient.stopAsr(mediaId)
        }.onFailure {
            DebugUtil.e(logTag, "stopAsr error $it")
        }
        asrCallback?.onAsrStop()
    }

    fun release(recordId: Long) {
        runCatching {
            asrClient.unRegisterAsrListener(mediaId, asrListener)
            asrClient.release("$recordId")
        }.onFailure {
            DebugUtil.e(logTag, "release error $it")
        }
        asrListener = null
    }
}