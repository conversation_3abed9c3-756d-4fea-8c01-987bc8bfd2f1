package com.soundrecorder.common.utils

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.PopupWindow
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.tooltips.COUIToolTips
import com.coui.appcompat.tooltips.COUIToolTips.ALIGN_BOTTOM
import com.coui.appcompat.tooltips.COUIToolTips.DEFAULT_ALIGN_DIRECTION
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.isFlexibleWindow
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.R
import java.lang.reflect.Method

class TipUtil(private val tipType: String, val lifecycle: Lifecycle) : DefaultLifecycleObserver {

    companion object {
        const val TYPE_MODE = "key_tips_mode_change"
        // 录制模式移动到设置中去，升级引导tip
        const val TYPE_MODE_MOVE = "key_tips_mode_change_move"
        const val TYPE_ROLE = "key_tips_role_change"
        const val TYPE_ROLE_NAME = "key_tips_role_name_change"
        const val TYPE_PICTURE_MARK = "key_tips_picture_mark_change"
        const val TYPE_GUIDE = "key_tips_guide_change"
        const val TYPE_SUMMARY_TIPS = "key_tips_summary"
        private const val DELAY_TIME = 200L
        private const val TIPS_SHOWED: Int = 1
        private const val TAG: String = "TipUtil"

        private val mUtilMap by lazy { hashMapOf<String, TipUtil>() }
        private val context by lazy { BaseApplication.getAppContext() }
        private val tipsHandler by lazy { Handler(Looper.getMainLooper()) }

        @JvmStatic
        fun checkShow(
            anchorCallBack: () -> View?,
            tipType: String = TYPE_MODE,
            /**
             * delayMillis 为null是默认值
             */
            delayMillis: Long? = null,
            lifecycle: Lifecycle,
            isInMultiWindowMode: Boolean,
            onFinish: (() -> Unit)? = null,
            offsetX: Int = 0,
            offsetY: Int = 0
        ) {
            var tipUtil = mUtilMap[tipType]
            if (tipUtil == null) {
                tipUtil = TipUtil(tipType, lifecycle)
                mUtilMap[tipType] = tipUtil
                lifecycle.addObserver(tipUtil)
            } else {
                if (tipUtil.isShowing()) {
                    DebugUtil.i(TAG, "tip is showing return")
                    if (lifecycle != tipUtil.lifecycle) {
                        onFinish?.invoke()
                    }
                    return
                }
            }

            if (isInMultiWindowMode) {
                DebugUtil.i(TAG, "In multi window,tip is not need show return.")
                /**
                 *新手提示在分屏下触发了，不会显示；回到大屏也不会再显示了
                 */
                saveShowedTip(tipType)
                onFinish?.invoke()
                return
            }
            if (hasShowTip(tipType)) {
                DebugUtil.i(TAG, "tip has show return.")
                onFinish?.invoke()
                return
            }

            tipUtil.anchorCallBack = anchorCallBack
            tipUtil.onFinish = onFinish

            when (tipType) {
                TYPE_ROLE_NAME -> {
                    tipUtil.show(lifecycle, delayMillis, ALIGN_BOTTOM, offsetX = offsetX, offsetY = offsetY)
                }
                TYPE_GUIDE -> {
                    tipUtil.show(lifecycle, delayMillis, hasIndicator = true, offsetX = offsetX, offsetY = offsetY)
                }
                else -> {
                    tipUtil.show(lifecycle, delayMillis, offsetX = offsetX, offsetY = offsetY)
                }
            }
        }

        @JvmStatic
        fun dismissSelf(tipType: String = TYPE_MODE) {
            mUtilMap[tipType]?.dismiss()
        }

        @JvmStatic
        fun hasShowTip(tipType: String): Boolean =
            PrefUtil.getInt(context, tipType, 0) == TIPS_SHOWED

        @JvmStatic
        fun saveShowedTip(tipType: String) {
            DebugUtil.w(TAG, "saveShowedTip tipType = $tipType.")
            PrefUtil.putInt(context, tipType, TIPS_SHOWED)
        }

        @JvmStatic
        fun View.setEnableShow(enable: Boolean) {
            setTag(R.id.tag_anchor_is_enable_show_tip, enable)
        }

        @JvmStatic
        fun isShowingTip(tipType: String = TYPE_MODE): Boolean {
            return mUtilMap[tipType]?.isShowing() == true
        }

        @JvmStatic
        fun View.isEnableShow() = (getTag(R.id.tag_anchor_is_enable_show_tip) as? Boolean) ?: true
    }

    private var mTips: COUIToolTips? = null
    private val tipActions = mutableListOf<TipAction>()
    private var anchorCallBack: (() -> View?)? = null
    private var onFinish: (() -> Unit)? = null
    private fun isShowing(): Boolean = mTips?.isShowing == true

    private val mOnDismissListener = PopupWindow.OnDismissListener {
        removeCallbackAndRelease()
    }

    private fun show(
        lifecycle: Lifecycle,
        delayMillis: Long? = null,
        direction: Int = DEFAULT_ALIGN_DIRECTION,
        hasIndicator: Boolean = true,
        offsetX: Int = 0,
        offsetY: Int = 0
    ) {
        removeCallback()
        tipsHandler.postDelayed(TipAction {
            if (isShowing()) {
                DebugUtil.i(TAG, "tip is showing,return")
                if (this.lifecycle != lifecycle) {
                    onFinish?.invoke()
                }
                return@TipAction
            }
            if (hasShowTip(tipType)) {
                DebugUtil.i(TAG, "tip has show,return")
                onFinish?.invoke()
                return@TipAction
            }
            val anchor = anchorCallBack?.invoke()
            if (anchor == null) {
                DebugUtil.i(TAG, "anchor is null")
                onFinish?.invoke()
                return@TipAction
            }
            val activity = anchor.context as? Activity
            if (activity == null) {
                DebugUtil.i(TAG, "activity is null")
                onFinish?.invoke()
                return@TipAction
            }
            if (!isFlexibleWindow(activity) && activity.isInMultiWindowMode) {
                DebugUtil.i(TAG, "In multi window not show tip.")
                onFinish?.invoke()
                return@TipAction
            }
            val contentResId = getContentRes()
            if (contentResId == 0) {
                DebugUtil.i(TAG, "contentResId is 0 return.")
                onFinish?.invoke()
                return@TipAction
            }
            val content = try {
                activity.resources?.getString(contentResId)
            } catch (e: Exception) {
                DebugUtil.i(TAG, "content getString happen exception")
                onFinish?.invoke()
                return@TipAction
            }

            showTipsView(activity, anchor, content, direction, hasIndicator, offsetX, offsetY)
        }, if (delayMillis == null) DELAY_TIME else (delayMillis + DELAY_TIME))
    }

    private fun showTipsView(
        activity: Activity,
        anchor: View,
        content: String?,
        direction: Int = DEFAULT_ALIGN_DIRECTION,
        hasIndicator: Boolean = true,
        offsetX: Int = 0,
        offsetY: Int = 0
    ) {
        try {
            mTips = COUIToolTips(
                COUIContextUtil.getCOUIThemeContext(BaseApplication.getAppContext())
            ).apply {
                setDismissOnTouchOutside(true)
                setContent(content)
            }
            DebugUtil.d(
                TAG,
                "windowToken=${anchor.windowToken} anchor.isEnableShow()=${anchor.isEnableShow()} anchor.isShown=${anchor.isShown}"
            )
            DebugUtil.i(TAG, "type is $tipType , anchor is $anchor ")
            if (anchor.isEnableShow() && !activity.isFinishing && (anchor.windowToken != null) && anchor.isShown) {
                mTips?.showWithDirection(anchor, direction, hasIndicator, offsetX, offsetY)
                mTips?.setOnDismissListener(mOnDismissListener)
                saveShowedTip(tipType)
            } else {
                onFinish?.invoke()
            }
        } catch (e: Exception) {
            DebugUtil.w(TAG, "showTipsView exception: activity = $activity, anchor = $anchor, content = $content")
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        dismiss()
        owner.lifecycle.removeObserver(this)
    }

    private fun dismiss() {
        DebugUtil.i(
            TAG,
            "dismiss call tip : $mTips isShowing is ${isShowing()}, mOnDismissListener is $mOnDismissListener"
        )
        if (isShowing()) {
            invokeDismissPopupWindow()
        }
        removeCallbackAndRelease()
    }

    /**
     * The dismiss() method of COUIToolTips has an exit animation.
     * When Activity is rebuilt,
     * there will be a problem with Window leak,
     * so reflects the dismiss() method to call the PopupWindow.
     */
    private fun invokeDismissPopupWindow() {
        try {
            val clazz = COUIToolTips::class.java
            val dismissPopupWindow: Method? = clazz.getDeclaredMethod("dismissPopupWindow")
            dismissPopupWindow?.isAccessible = true
            dismissPopupWindow?.invoke(mTips)
        } catch (e: Exception) {
            DebugUtil.e(TAG, " invoke dismissPopupWindow error.", e)
        }
    }

    private fun removeCallbackAndRelease() {
        DebugUtil.i(TAG, "removeCallbackAndRelease  tipType = $tipType")
        removeCallback()
        mUtilMap.remove(tipType)
        mTips?.setOnDismissListener(null)
        onFinish?.invoke()
        anchorCallBack = null
        onFinish = null
        mTips = null
    }

    private fun removeCallback() {
        DebugUtil.i(TAG, "removeCallback  tipType = $tipType")
        tipActions.forEach {
            tipsHandler.removeCallbacks(it)
        }
        tipActions.clear()
    }

    private fun getContentRes() = when (tipType) {
        TYPE_MODE -> R.string.tips_change_mode
        TYPE_MODE_MOVE -> R.string.tip_set_record_mode
        TYPE_ROLE -> R.string.tips_change_role
        TYPE_ROLE_NAME -> R.string.tips_change_role_name
        TYPE_PICTURE_MARK -> R.string.tips_change_picture_mark
        TYPE_GUIDE -> R.string.convert_tab_tip
        TYPE_SUMMARY_TIPS -> R.string.tip_click_to_view_summary
        else -> 0
    }

    inner class TipAction(val body: () -> Unit) : Runnable {
        init {
            tipActions.add(this)
        }

        override fun run() {
            body()
            tipActions.remove(this)
        }
    }
}