package com.recorder.cloudkit.util;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.utils.PathUtil;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.databean.Record;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.robolectric.annotation.Config;

import java.io.File;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class PathUtilTest {
    private static final String TEST_FILE_RELATIVE_PATH_WITH_MUSIC = "Music" + File.separator + "Recordings/Standard Recordings";
    private static final String TEST_FILE_RELATIVE_PATH_WITHOUT_MUSIC = "Recordings/Standard Recordings";
    private static final String TEST_FILE_NAME = "标准 1.MP3";
    private static final String TEST_FILE_PATH_WITH_MUSIC = TEST_FILE_RELATIVE_PATH_WITH_MUSIC + File.separator + TEST_FILE_NAME;
    private static final String TEST_FILE_PATH_WITHOUT_MUSIC = TEST_FILE_RELATIVE_PATH_WITHOUT_MUSIC + File.separator + TEST_FILE_NAME;
    private Context mContext;
    private MockedStatic<BaseUtil> mMockStaticBaseUtil;
    MockedStatic<TextUtils> mMockStaticTextUtils;

    @Before
    public void setUp() {
        mMockStaticTextUtils = Mockito.mockStatic(TextUtils.class);
        mMockStaticTextUtils.when(() -> TextUtils.isEmpty(anyString())).thenAnswer((Answer<Boolean>) invocation -> {
            CharSequence a = invocation.getArgument(0);
            return a == null || a.length() == 0;
        });

        mContext = ApplicationProvider.getApplicationContext();
        mMockStaticBaseUtil = mockStatic(BaseUtil.class);
        mMockStaticBaseUtil.when(BaseUtil::isAndroidQOrLater).thenReturn(true);
        mMockStaticBaseUtil.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("0");
    }

    @Test
    public void should_returnPath_when_getRelativePathFroConcatedPath() {
        String path = PathUtil.getRelativePathFroConcatedPath(TEST_FILE_PATH_WITH_MUSIC);
        Assert.assertEquals(TEST_FILE_RELATIVE_PATH_WITH_MUSIC, path);
    }

    @Test
    public void should_returnPath_when_getUploadConcatedPath() {
        Record record = new Record();
        record.setRelativePath(TEST_FILE_RELATIVE_PATH_WITH_MUSIC);
        record.setDisplayName(TEST_FILE_NAME);
        record.setData("0" + File.separator + TEST_FILE_PATH_WITHOUT_MUSIC);

        String path = PathUtil.getUploadConcatedPath(record);
        Assert.assertEquals(TEST_FILE_PATH_WITHOUT_MUSIC, path);
    }

    @Test
    public void should_returnPath_when_getDownloadRelativePath() {
        String path = PathUtil.getDownloadRelativePath(TEST_FILE_PATH_WITH_MUSIC);
        Assert.assertEquals(TEST_FILE_RELATIVE_PATH_WITH_MUSIC + File.separator, path);
    }

    @Test
    public void should_returnPath_when_getDownloadDataPath() {
        String relativePathFromCloud = TEST_FILE_RELATIVE_PATH_WITHOUT_MUSIC + File.separator + TEST_FILE_NAME;
        String path = PathUtil.getDownloadDataPath(mContext, relativePathFromCloud);
        String expect = "0" + File.separator + "Music" + File.separator + relativePathFromCloud;
        Assert.assertEquals(expect, path);
    }

    @Test
    public void should_returnPath_when_getNameFromPath() {
        String path = PathUtil.getNameFromPath(TEST_FILE_PATH_WITH_MUSIC);
        Assert.assertEquals(TEST_FILE_NAME, path);
    }

    @Test
    public void should_returnPath_when_getFilePath() {
        String path = PathUtil.getFilePath(TEST_FILE_RELATIVE_PATH_WITH_MUSIC, TEST_FILE_NAME);
        String expect = "0" + File.separator + TEST_FILE_RELATIVE_PATH_WITH_MUSIC + TEST_FILE_NAME;
        Assert.assertEquals(expect, path);
    }

    @Test
    public void should_returnFormatMill_when_getNewNameSuffixByConflict() {
        String path = PathUtil.getNewNameSuffixByConflict(1653099220L);
        Assert.assertEquals("20220521101340", path);
    }

    @Test
    public void should_returnNewName_when_getNewNameForSyncRecordConflict() {
        MockedStatic<FileUtils> mMockStaticFileUtil = Mockito.mockStatic(FileUtils.class);
        mMockStaticFileUtil.when(() -> FileUtils.isFileExist(anyString(), anyString())).thenReturn(false, true, false);
        mMockStaticFileUtil.when(() -> FileUtils.getNewDisplayName(anyString(), anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.calRequireLengthTitle(anyString(), anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.getFileNameNoEx(anyString())).thenCallRealMethod();

        long date = 1653099220L;
        Record record = new Record();
        record.setDateModied(date);
        record.setDateCreated(date);
        record.setRelativePath("music/Recordings/Standard Recordings/");
        record.setDisplayName("标准录音 1.mp3");

        String newName = PathUtil.getNewNameForSyncRecordConflict(record);
        Assert.assertEquals("标准录音 1_20220521101340.mp3", newName);


        record.setDisplayName("标准录音10000011111222223333344444555556666677777.mp3");
        newName = PathUtil.getNewNameForSyncRecordConflict(record);
        Assert.assertEquals("标准录音1000001111122222333334444455555_202205211013_1.mp3", newName);


        mMockStaticFileUtil.close();
    }

    @Test
    public void should_notNull_when_getRelativePathFroConcatedPath() {
        Assert.assertNotNull(PathUtil.getRelativePathFroConcatedPath(TEST_FILE_PATH_WITH_MUSIC));
    }

    @Test
    public void should_notNull_when_getUploadConcatedPath() {
        Record record = Mockito.mock(Record.class);
        doReturn("111").when(record).getData();
        doReturn("ssss").when(record).getConcatRelativePath();
        PathUtil.getUploadConcatedPath(record);
        Assert.assertNotNull(PathUtil.getUploadConcatedPath(record));
    }

    @Test
    public void should_notNull_when_getDownloadRelativePath() {
        Assert.assertNotNull(PathUtil.getDownloadRelativePath(TEST_FILE_PATH_WITH_MUSIC));
    }

    @Test
    public void should_notNull_when_getDownloadDataPath() {
        Assert.assertNotNull(PathUtil.getDownloadDataPath(mContext, TEST_FILE_PATH_WITH_MUSIC));
    }

    @Test
    public void should_notNull_when_getNameFromPath() {
        Assert.assertNotNull(PathUtil.getNameFromPath(TEST_FILE_PATH_WITH_MUSIC));
    }

    @After
    public void release() {
        if (mMockStaticBaseUtil != null) {
            mMockStaticBaseUtil.close();
            mMockStaticBaseUtil = null;
        }
        if (mMockStaticTextUtils != null) {
            mMockStaticTextUtils.close();
            mMockStaticTextUtils = null;
        }
        mContext = null;
    }
}
