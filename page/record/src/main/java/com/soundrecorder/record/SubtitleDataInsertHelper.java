/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 * File: - SubtitleDataInsertHelper.java
 * Description:
 *     The helper class for ui process of subtitle data.
 * Version: 1.0
 * Date: 2025-06-06
 * Author: <EMAIL>
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-30   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.app.Activity;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Rect;
import android.graphics.drawable.AnimationDrawable;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.buryingpoint.BuryingPoint;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache;
import com.soundrecorder.common.utils.RecordFileChangeNotify;
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry;

import java.util.ArrayList;
import java.util.List;

public class SubtitleDataInsertHelper {

    private static final String TAG = "SubtitleDataInsertHelper";
    private static final int THRESHOLD = 5000;
    private static final int NO_NETWORK_ERRORCODE = 100105;
    private static final int SETREQUESTCODE = 211;
    private RenameFileDialog mRenameDialog = null;
    private boolean mIsLoadingSuccessful = false;
    private AnimationDrawable mLoadingCaptionsAnimation;
    private ConstraintLayout mCaptionsGradientView;
    private COUIRecyclerView mCaptionsRecyclerView;
    private LinearLayout mCaptionsLoadingView;
    private ImageView mCaptionsAnimationView;
    private RecordSubtitleAdapter mSubtitleAdapter;
    private Activity mContext;
    private List<ConvertContentItem> mTotalList = new ArrayList<>();
    private boolean mIsOpenSubtitles = false;
    private boolean mIsHoldOn = false;

    public SubtitleDataInsertHelper(Activity context,
                                    ConstraintLayout mCaptionsGradientView,
                                    COUIRecyclerView mCaptionsRecyclerView,
                                    LinearLayout mCaptionsLoadingView,
                                    ImageView mCaptionsAnimationView) {
        this.mContext = context;
        this.mCaptionsGradientView = mCaptionsGradientView;
        this.mCaptionsRecyclerView = mCaptionsRecyclerView;
        this.mCaptionsLoadingView = mCaptionsLoadingView;
        this.mCaptionsAnimationView = mCaptionsAnimationView;
        initGradientRecyclerView();
        openSubtitles();
        initLoadingAnimation();
    }

    public void setAdapterData(List<DisplaySubtitleEntry> subtitles) {
        mContext.runOnUiThread(() -> {
            if (!mIsHoldOn) {
                DebugUtil.i(TAG, "mSubtitleMarkInsertHelper");
                mSubtitleAdapter.setData(subtitles);
                if (mSubtitleAdapter.getItemCount() - 1 > -1) {
                    if (SubtitleRecyclerViewUtils.estimateTotalHeight(mCaptionsRecyclerView)
                            > SubtitleRecyclerViewUtils.getVisibleHeight(mCaptionsRecyclerView)) {
                        mCaptionsRecyclerView.smoothScrollToPosition(mSubtitleAdapter.getItemCount() - 1);
                    }
                }
            }
        });
    }

    public void onSubtitleUpdated(@NonNull IRealtimeSubtitleCache cache) {
        List<ConvertContentItem> completedItems = cache.getGeneratedSubtitles();
        List<ConvertContentItem> processingItem = cache.getTemporySubtitles();
        mTotalList.clear();
        mTotalList.addAll(completedItems);
        mTotalList.addAll(processingItem);
        DebugUtil.i(TAG, "onSubtitleUpdated: " + completedItems.size());
        if (completedItems.isEmpty() && processingItem.isEmpty()) {
            mContext.runOnUiThread(() -> showLoadingAnim(false));
        } else {
            if (mIsOpenSubtitles) {
                mContext.runOnUiThread(() -> showLoadingAnim(true));
            }
        }
    }

    public void onAsrError(int code) {
        DebugUtil.i(TAG, "onError: code=" + code);
        mSubtitleAdapter.setNoNetWork(code == NO_NETWORK_ERRORCODE);
    }

    /**
     * 录音转写功能默认关闭
     */
    private void openSubtitles() {
        openSubtitles(true);
    }

    /**
     * 录音转写功能开关
     *
     * @param isOpenSubtitles 是否开启
     */
    private void openSubtitles(boolean isOpenSubtitles) {
        mIsOpenSubtitles = isOpenSubtitles;
        if (mCaptionsGradientView == null) {
            return;
        }
        if (isOpenSubtitles) {
            mCaptionsGradientView.setVisibility(VISIBLE);
        } else {
            mCaptionsGradientView.setVisibility(GONE);
        }
    }

    private void initGradientRecyclerView() {
        DebugUtil.i(TAG, "=========>initGradientRecyclerView");
        mCaptionsRecyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        mSubtitleAdapter = new RecordSubtitleAdapter(mContext);
        mSubtitleAdapter.setListener(new RecordSubtitleAdapter.OnItemClickListener() {
            @Override
            public void onLongClickStop(int position) {
                DebugUtil.i(TAG, "onLongClickStop");
                mIsHoldOn = false;
            }

            @Override
            public void onItemLongClick(int position) {
                DebugUtil.i(TAG, "onItemLongClick");
                mIsHoldOn = true;
            }

            public void onItemClickSpeaker(int position) {
                DebugUtil.i(TAG, "onItemClickSpeaker");
/*                showRenameDialog(mSubtitleAdapter.getDataList().get(position).getOriginContent().getRoleName(),
                  mSubtitleAdapter.getDataList().get(position).getOriginContent().getRoleId());*/
            }
        });
        mCaptionsRecyclerView.setAdapter(mSubtitleAdapter);
        mSubtitleAdapter.setData(new ArrayList());
    }

    private void initLoadingAnimation() {
        if (mCaptionsAnimationView == null) {
            return;
        }
        mCaptionsAnimationView.setBackgroundResource(R.drawable.subtities_loading_animator);
        mLoadingCaptionsAnimation = (AnimationDrawable) mCaptionsAnimationView.getBackground();
    }

    public void showLoadingAnim(boolean isLoadingSuccessful) {
        mIsLoadingSuccessful = isLoadingSuccessful;
        if (mCaptionsRecyclerView == null || mCaptionsLoadingView == null) {
            DebugUtil.i(TAG, "showLoadingAnim");
            return;
        }
        if (mIsLoadingSuccessful) {
            mCaptionsRecyclerView.setVisibility(VISIBLE);
            mCaptionsLoadingView.setVisibility(GONE);
            mLoadingCaptionsAnimation.stop();
        } else {
            mCaptionsRecyclerView.setVisibility(GONE);
            mCaptionsLoadingView.setVisibility(VISIBLE);
            mLoadingCaptionsAnimation.start();
        }
    }

    public void dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            if (isTouchPointInView(mCaptionsRecyclerView, (int) ev.getRawX(), (int) ev.getRawY())) {
                mIsHoldOn = true;
            }
        } else if (ev.getAction() == MotionEvent.ACTION_UP || ev.getAction() == MotionEvent.ACTION_CANCEL) {
            mCaptionsRecyclerView.postDelayed(() -> mIsHoldOn = false, THRESHOLD);
        }
    }

    private boolean isTouchPointInView(View view, int x, int y) {
        Rect rect = new Rect();
        view.getGlobalVisibleRect(rect);
        return rect.contains(x, y);
    }

    private void showRenameDialog(String text, long recordId) {
        if (mRenameDialog != null && mRenameDialog.isShowing()) {
            DebugUtil.i(TAG, "mRenameDialog is showing");
            return;
        }

        Uri queryUri = MediaDBUtils.BASE_URI;
        Cursor cursor = null;
        String where = MediaStore.Audio.Media._ID + "=?";
        Record mediaRecord = null;
        try {
            cursor = mContext.getContentResolver().query(
                    queryUri,
                    null,
                    where,
                    new String[]{String.valueOf(recordId)},
                    null
            );
            if (cursor != null && cursor.moveToNext()) {
                mediaRecord = new Record(cursor, Record.TYPE_FROM_MEDIA);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "cursor is exception" + e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        String content = "";
        if (text == null) {
            int lastIndex = -1;
            if (!TextUtils.isEmpty(mediaRecord != null ? mediaRecord.getDisplayName() : null)
                    && ((mediaRecord != null ? mediaRecord.getDisplayName().lastIndexOf(".") : -1) > 0)) {
                lastIndex = mediaRecord != null ? mediaRecord.getDisplayName().lastIndexOf(".") : 0;
                content = mediaRecord != null ? mediaRecord.getDisplayName().substring(0, lastIndex) : "";
                DebugUtil.i(TAG, "showRenameDialog, mRenameDialog content is " + content);
            } else {
                return;
            }
        } else {
            content = text;
        }
        createDialog(content, recordId);
        BuryingPoint.addActionForPlaybackRename();

        if (mRenameDialog != null) {
            mRenameDialog.setRequestCode(SETREQUESTCODE);
            mRenameDialog.setMediaRecord(mediaRecord);
            mRenameDialog.show();
        }
    }

    private void createDialog(String content, long recordId) {
        mRenameDialog = new RenameFileDialog(
                mContext,
                RenameFileDialog.FROM_PLAYBACK_MORE,
                content,
                (displayName, path) -> {
                    DebugUtil.i(TAG, "rename callback displayName: " + displayName + ", path: " + path);
                    int index = -1;
                    if (path == null || (index = path.lastIndexOf(".")) == -1) {
                        return;
                    }

                    for (int i = 0; i < mSubtitleAdapter.getDataList().size(); i++) {
                        if (recordId == mSubtitleAdapter.getDataList().get(i).getOriginContent().getRoleId()) {
                            mSubtitleAdapter.getDataList().get(i).getOriginContent().setRoleName(displayName);
                            mSubtitleAdapter.notifyItemChanged(i);
                            break;
                        }
                    }

                    for (ConvertContentItem item : mTotalList) {
                        if (item.getRoleId() == recordId) {
                            item.setRoleName(displayName);
                            break;
                        }
                    }

                    DebugUtil.i(TAG, "setValue: " + (displayName + path.substring(index)));

                    if (mRenameDialog != null) {
                        mRenameDialog.resetOperating();
                    }

                    notifyRefreshRecordList();
                    BuryingPoint.addActionForPlaybackRenameSuccess();
                }
        );
    }

    private void notifyRefreshRecordList() {
        Intent intent = new Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION);
        intent.putExtra(Constants.FRESH_FLAG, true);
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent);
    }
}
