/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector.media

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MediaSampleDataTest {

    @Test
    fun filterSampleDataTest() {
        val data = ByteArray(100)
        for (i in data.indices) {
            data[i] = i.toByte()
        }

        val sampleData = MuteMediaSampleData(10)
        val shortArray = sampleData.filterSampleData(2, data)
        Assert.assertTrue(shortArray[2] % 2 == 0)
    }

    @Test
    fun extractSampleDataTest() {
        val data = ShortArray(55)
        for (i in data.indices) {
            data[i] = i.toShort()
        }
        val sampleData = MuteMediaSampleData(10)
        sampleData.extractSampleData(data, false, null)
        val destOffset = Whitebox.getInternalState<Int>(sampleData, "destOffset")
        Assert.assertEquals(4, destOffset)
    }

    @Test
    fun initNewDestineArrayTest() {
        val sampleData = MuteMediaSampleData(10)
        Whitebox.invokeMethod<Int>(sampleData, "initNewDestineArray", 55)
        val endIndex = Whitebox.getInternalState<Int>(sampleData, "endIndex")
        Assert.assertEquals(9, endIndex)
    }

    @Test
    fun releaseTest() {
        val sampleData = MuteMediaSampleData(10)
        sampleData.release()
        val destineDataArray = Whitebox.getInternalState<ShortArray>(sampleData, "destineDataArray")
        Assert.assertTrue(destineDataArray == null)
    }
}