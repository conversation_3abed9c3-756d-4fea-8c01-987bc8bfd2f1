<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="177dp"
    android:height="152dp"
    android:viewportWidth="177"
    android:viewportHeight="152">
  <group>
    <clip-path
        android:pathData="M0,0h176.26v151.37h-176.26z"/>
    <path
        android:pathData="M124.64,87.75L128.3,81.08C123.65,76.22 120.93,69.84 120.65,63.13C120.37,56.41 122.54,49.82 126.76,44.59C130.98,39.36 136.96,35.84 143.58,34.7C150.2,33.56 157.02,34.87 162.75,38.38C168.48,41.89 172.73,47.37 174.71,53.8C176.7,60.22 176.27,67.14 173.52,73.27C170.77,79.41 165.88,84.33 159.77,87.12C153.66,89.91 146.74,90.38 140.3,88.44C137.9,87.71 135.61,86.66 133.49,85.32L126.84,89.74C126.56,89.95 126.21,90.06 125.86,90.04C125.51,90.03 125.18,89.89 124.92,89.66C124.66,89.42 124.49,89.1 124.43,88.75C124.39,88.4 124.46,88.05 124.64,87.75Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.6"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.42,76.95C152.42,77.78 152.17,78.59 151.71,79.29C151.25,79.98 150.59,80.52 149.82,80.84C149.05,81.16 148.21,81.24 147.39,81.08C146.57,80.91 145.82,80.51 145.23,79.92C144.64,79.34 144.24,78.59 144.08,77.77C143.92,76.95 144,76.11 144.32,75.34C144.64,74.57 145.18,73.91 145.87,73.45C146.56,72.98 147.38,72.74 148.21,72.74C148.76,72.74 149.31,72.84 149.82,73.06C150.33,73.27 150.8,73.58 151.19,73.97C151.58,74.36 151.89,74.82 152.1,75.33C152.31,75.85 152.42,76.39 152.42,76.95Z"
        android:fillColor="#8D65AC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M159.09,55.06C158.89,57.03 158.16,58.91 156.97,60.49C155.82,62.05 154.27,63.29 152.48,64.06C152.19,64.19 151.96,64.41 151.8,64.68C151.62,64.94 151.53,65.26 151.53,65.58V66.71C151.55,67.15 151.49,67.59 151.34,68.01C151.18,68.43 150.95,68.81 150.64,69.13C150.34,69.45 149.97,69.71 149.56,69.88C149.16,70.06 148.72,70.15 148.27,70.15C147.83,70.15 147.39,70.06 146.99,69.88C146.58,69.71 146.21,69.45 145.91,69.13C145.6,68.81 145.37,68.43 145.21,68.01C145.06,67.59 144.99,67.15 145.02,66.71V65.71C144.97,64.12 145.41,62.56 146.27,61.23C147.11,59.84 148.35,58.74 149.82,58.06H149.88C150.61,57.77 151.25,57.27 151.7,56.63C152.17,56 152.47,55.26 152.56,54.48C152.63,53.7 152.49,52.91 152.15,52.21C151.82,51.49 151.3,50.88 150.65,50.44C149.99,50 149.23,49.75 148.44,49.73C147.66,49.67 146.88,49.85 146.2,50.23C145.49,50.58 144.9,51.14 144.5,51.82C144.13,52.5 143.95,53.28 143.97,54.06C143.97,54.92 143.63,55.76 143.01,56.37C142.4,56.98 141.57,57.33 140.7,57.33C139.83,57.33 139,56.98 138.39,56.37C137.77,55.76 137.43,54.92 137.43,54.06C137.43,52.09 137.97,50.15 138.98,48.46C140,46.78 141.45,45.39 143.19,44.47C144.49,43.77 145.92,43.35 147.39,43.22C148.86,43.1 150.34,43.28 151.74,43.75C153.14,44.21 154.43,44.96 155.52,45.95C156.62,46.93 157.51,48.13 158.13,49.47C158.95,51.21 159.28,53.14 159.09,55.06Z"
        android:fillColor="#8D65AC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M19.78,124.37L9.84,131.08L9.65,131.16L2.55,133C2.03,133.13 1.55,133.37 1.15,133.72C0.75,134.07 0.44,134.51 0.24,135C0.21,135.09 0.19,135.18 0.21,135.28C0.22,135.37 0.25,135.46 0.3,135.54C0.36,135.62 0.43,135.68 0.51,135.73C0.59,135.77 0.69,135.8 0.78,135.8H21"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M50.97,119.74L63.52,118.44"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74,61.44L74.02,61.4L76.25,62.43L76.67,63.36C76.59,67.12 76.49,71.41 76.35,76.22C75.85,93.65 70,92.65 70,92.65L69.78,94.84C69.76,95.08 69.67,95.32 69.54,95.52C69.41,95.72 69.23,95.89 69.02,96.01C68.81,96.14 68.57,96.2 68.33,96.21C68.08,96.22 67.84,96.17 67.62,96.07C65.51,94.96 63.43,93.77 61.41,92.51L45.35,73.82C45.39,73.06 45.47,72.24 45.57,71.34C45.7,70.21 45.98,68.84 46.35,67.3L63.19,67.57C63.83,67.61 64.48,67.52 65.09,67.3C65.49,67.16 65.87,66.96 66.22,66.72C66.62,66.59 67,66.39 67.34,66.13C68.84,64.97 71.82,60.83 71.85,60.8L74,61.44ZM76.74,60.35C76.74,60.37 76.74,60.39 76.74,60.41L76.38,60.21L76.74,60.35ZM75.6,25.68C70.98,28.81 66.41,34.49 66.37,34.54C66.42,34.54 70.85,34.59 72.06,36.21C73.71,38.43 76.45,48.52 76.93,50.31C76.92,51.08 76.9,51.91 76.89,52.79L76.74,52.75C76.51,52.64 76.26,52.57 76.01,52.54L73.28,51.8C73.03,51.75 72.77,51.77 72.52,51.83C72.28,51.9 72.05,52.02 71.86,52.19L64.35,57.53C64.12,57.47 63.89,57.44 63.65,57.42L49.74,55.62C51.52,49.73 53.14,43.99 52.98,40.98C52.94,40.05 52.85,37.93 52.85,37.11C52.71,33 54.63,30.25 58.71,28.93C62.73,27.63 72.34,26.16 75.6,25.68Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M38.05,123.21L38.61,136.17"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M99.29,30.17C99.97,30.31 100.64,30.53 101.28,30.81C102.64,31.39 103.48,33.62 104,35.02C104.03,35.08 104.05,35.13 104.07,35.19C105.09,37.84 105.48,40.68 105.23,43.5C105.02,45.72 101.57,61.17 100.05,67.93C99.66,69.69 99.4,70.86 99.36,71.06C99.09,72.39 99.23,76.56 99.36,80.3C99.44,82.59 99.51,84.73 99.48,85.96C99.4,89.55 97.4,90.67 96.08,91.4C95.61,91.67 95.22,91.89 95.04,92.15C94.82,92.49 94.47,93.49 94.1,94.59C93.58,96.14 92.99,97.87 92.57,98.21C86.64,103 80.15,104.21 78,103.89C77.28,103.79 75.21,97.14 78.16,94.11C83.23,88.9 83.46,81.77 83.56,75.43C83.63,71.86 84.11,68.31 85,64.85C85.64,62.29 86.44,59.77 87.3,57.26C87.28,57.33 87.27,57.4 87.25,57.46C87.35,57.35 87.48,57.2 87.65,57C89.96,54.35 98.62,44.41 99,38.33C99.11,36.33 104.68,39.98 104.68,39.98C104.68,39.98 101.65,34.27 99.15,30.39L99.29,30.17Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M101.46,31.15C106.75,31.15 111.04,35.99 111.04,41.96C111.04,47.93 106.75,52.77 101.46,52.77C98.5,52.77 95.86,51.26 94.1,48.88C96.58,45.32 98.81,41.35 99,38.33C99.11,36.34 104.65,39.96 104.68,39.98C104.68,39.98 102.11,35.12 99.74,31.32C100.3,31.21 100.87,31.15 101.46,31.15ZM92.01,42.35C91.97,42.46 91.94,42.58 91.9,42.7C91.89,42.53 91.88,42.35 91.88,42.18L92.01,42.35Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M93.15,35.24C93.27,36.33 93.22,37.45 93.04,38.52C92.55,41.38 87.94,54.65 87.25,57.46C88.66,55.81 98.59,44.85 99,38.33C99.11,36.33 104.68,39.98 104.68,39.98C104.68,39.98 101.66,34.28 99.15,30.39"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.88,23.7C79.76,23.73 79.65,23.76 79.53,23.79C74,25.05 66.37,34.54 66.37,34.54C66.37,34.54 70.84,34.58 72.06,36.21C73.87,38.65 77,50.58 77,50.58C77.55,46.58 77.47,42.51 76.76,38.53C76.17,36.48 76.28,34.29 77.06,32.3C77.52,31.2 78.05,30.27 78.81,29.64"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.15,43.58C68.11,37.94 65.65,31.96 60.66,30.22C55.66,28.48 50.02,31.64 48.05,37.28C46.09,42.92 48.55,48.9 53.55,50.64C58.54,52.38 64.18,49.22 66.15,43.58Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M22.43,125.9L23.47,130.09L20,138.31H19.7C19.27,138.34 18.87,138.54 18.58,138.85C18.29,139.16 18.13,139.58 18.13,140.01C18.13,140.43 18.29,140.85 18.58,141.16C18.87,141.47 19.27,141.67 19.7,141.7H35.6C36.05,141.7 36.48,141.52 36.8,141.2C37.12,140.88 37.3,140.45 37.3,140C37.29,139.77 37.24,139.55 37.15,139.35C37.05,139.14 36.91,138.96 36.74,138.81V137.81C36.74,136.37 36.51,134.94 36.08,133.57L34.52,128.66L34.1,125.9"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.01,18.11C102.73,19.88 102.46,21.65 102.05,23.4C103.61,21.57 104.65,19.27 104.87,16.68C104.89,16.46 104.91,16.23 104.94,16.01L104.94,16.01L104.94,16.01C104.98,15.6 105.03,15.2 105.03,14.8C105.7,6.91 99.66,1.21 91.97,0.8H91.59C83.9,0.25 77.18,4.54 76.54,12.23C76.54,12.23 76.37,13.65 76.37,14.14C76.47,19.02 77.95,22.98 80.28,25.52C80.4,25.11 80.51,24.69 80.62,24.28C80.11,23.93 79.66,23.5 79.3,23C78.2,21.58 77.3,18.94 78.56,17.7C79.05,17.3 79.66,17.07 80.29,17.04C80.93,17.02 81.55,17.2 82.07,17.56C82.66,18.01 83.07,18.66 83.23,19.39L84.73,11.34C84.78,10.67 84.96,10.02 85.27,9.43C85.58,8.84 86.01,8.32 86.53,7.9C87.05,7.48 87.65,7.17 88.29,6.99C88.94,6.81 89.61,6.77 90.27,6.86C90.4,6.88 90.52,6.91 90.64,6.95L99.64,8.58L100.02,8.63C100.67,8.77 101.29,9.05 101.83,9.45C102.37,9.85 102.82,10.35 103.15,10.93C103.48,11.51 103.69,12.15 103.76,12.82C103.83,13.48 103.76,14.15 103.56,14.79C103.36,15.89 103.18,17 103.01,18.11L103.01,18.11Z"
        android:fillColor="#525559"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M80.61,24.32C80.17,26.05 79.63,27.76 78.99,29.43C77.76,30.8 77.08,32.59 77.09,34.43C77.09,36.43 77.88,38.34 79.3,39.75C80.71,41.17 82.62,41.96 84.62,41.96C86.62,41.96 88.53,41.17 89.94,39.75C91.12,38.58 91.87,37.06 92.08,35.43"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.56,14.79C102.95,18.07 102.62,21.43 101.73,24.66C100.07,30.66 95.53,36.03 91.07,35.3C90.84,35.27 90.61,35.21 90.39,35.13C88.04,34.58 84.96,32.13 83.11,28.91C82.33,27.56 81.7,26.13 81.23,24.64C80.46,24.26 79.8,23.69 79.3,23C78.2,21.58 77.3,18.94 78.56,17.7C79.05,17.3 79.66,17.07 80.29,17.04C80.92,17.02 81.55,17.2 82.07,17.56C82.66,18.01 83.07,18.66 83.23,19.39L84.73,11.34C84.78,10.67 84.96,10.02 85.27,9.43C85.58,8.84 86.01,8.32 86.53,7.9C87.05,7.48 87.65,7.17 88.29,6.99C88.93,6.81 89.61,6.77 90.27,6.86C90.39,6.88 90.52,6.91 90.64,6.95L99.64,8.58L100.02,8.63C100.67,8.77 101.29,9.05 101.83,9.45C102.36,9.85 102.82,10.35 103.15,10.93C103.48,11.51 103.69,12.15 103.76,12.82C103.83,13.48 103.76,14.15 103.56,14.79Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.59,16.24C82.59,16.24 84.65,16.24 86.98,13.89C91.52,9.36 91,5.31 91,5.31C91,5.31 86.36,4.92 84.21,7.48C81.87,10.34 82.59,16.24 82.59,16.24Z"
        android:fillColor="#525559"/>
    <path
        android:pathData="M89.25,6.44C89.98,8.87 91.5,10.98 93.58,12.44C95.11,13.48 96.88,14.12 98.73,14.31L97.5,9.89L103.5,17.32C103.5,17.32 106.65,7.12 95.71,4.88C92.37,4.2 89.25,6.44 89.25,6.44Z"
        android:fillColor="#525559"/>
    <path
        android:pathData="M78.72,19.77C78.92,19.57 79.19,19.44 79.47,19.41C79.76,19.37 80.05,19.44 80.29,19.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.78,21.15C90.78,21.75 90.36,22.21 89.96,22.15C89.56,22.09 89.28,21.59 89.33,20.99C89.38,20.39 89.75,19.94 90.15,19.99C90.55,20.04 90.83,20.55 90.78,21.15Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M108.89,105.16H108.85H96.38L95.25,49.42L95.12,49.01L111,40.94L113.51,105.64L108.89,105.17L108.89,105.16Z"
        android:fillColor="#5966A9"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M93.2,26.39C93.48,26.56 93.8,26.68 94.14,26.72C94.47,26.75 94.8,26.72 95.12,26.62"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.78,19.1C89,18.84 89.27,18.64 89.58,18.51C89.88,18.38 90.22,18.33 90.55,18.35C91.04,18.42 91.5,18.67 91.82,19.05"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.59,22C97.5,22.55 97.74,23 98.11,23.1C98.48,23.2 98.86,22.76 98.95,22.22C99.04,21.68 98.8,21.22 98.43,21.12C98.06,21.02 97.68,21.41 97.59,22Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M100.46,19.82C100.14,19.51 99.72,19.33 99.28,19.3C98.78,19.28 98.29,19.44 97.91,19.76"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M94.31,29.74C93.38,29.77 92.46,29.62 91.59,29.31C90.82,29.03 90.1,28.63 89.45,28.12"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.34,15.94C87.56,15.28 88.96,15.02 90.34,15.2C91.61,15.37 92.81,15.92 93.77,16.78"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.39,17.07C98.18,16.46 99.17,16.18 100.16,16.28C100.8,16.35 101.4,16.6 101.91,16.99"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.83,15.21C83.93,16.1 83.93,16.99 83.83,17.88C83.73,18.56 83.57,19.23 83.33,19.88L82.42,15.8L83.83,15.21Z"
        android:fillColor="#525559"/>
    <path
        android:pathData="M81.53,22.47L83.01,23.79C83.07,23.85 83.15,23.88 83.24,23.87C83.32,23.87 83.4,23.83 83.46,23.77L83.77,23.42C83.82,23.36 83.85,23.28 83.85,23.19C83.84,23.11 83.8,23.03 83.74,22.97L82.38,21.76"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.26,19.65C82.53,19.98 82.67,20.4 82.66,20.83C82.65,21.26 82.49,21.67 82.21,21.99C81.92,22.31 81.53,22.51 81.11,22.57C80.68,22.63 80.25,22.53 79.89,22.3C79.62,21.97 79.48,21.55 79.49,21.12C79.5,20.69 79.66,20.28 79.94,19.96C80.23,19.64 80.62,19.44 81.04,19.38C81.47,19.32 81.9,19.42 82.26,19.65Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.15,35.25C92.02,36.32 91.65,37.34 91.07,38.25C89.24,37.52 87.62,36.36 86.34,34.87C85.05,33.38 84.15,31.6 83.71,29.68C85.58,32.41 88.29,34.48 90.41,34.98C90.63,35.06 90.86,35.12 91.09,35.15C91.44,35.22 91.79,35.26 92.15,35.25Z"
        android:fillColor="#808080"/>
    <path
        android:pathData="M51.86,81.31C57.32,80.89 62.68,79.44 67.64,77C65.75,80.06 63.11,82.59 59.96,84.34C58.69,85.04 57.36,85.62 55.98,86.04"
        android:strokeLineJoin="round"
        android:strokeWidth="0.41"
        android:fillColor="#00000000"
        android:strokeColor="#525559"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.26,51.39C87.41,63.45 86.1,71.1 86.07,84.11C86.07,85.85 85.92,87.58 85.77,89.32C85.4,93.65 81.77,96.13 81.4,97.14C79.97,100.63 80.8,102.71 81.4,103.62"
        android:strokeLineJoin="round"
        android:strokeWidth="0.41"
        android:fillColor="#00000000"
        android:strokeColor="#525559"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.08,35.19C105.1,37.83 105.49,40.68 105.24,43.5C104.98,46.3 99.56,70.1 99.37,71.06C98.94,73.21 99.57,82.73 99.49,85.96C99.38,90.84 95.72,91.15 95.05,92.15C94.51,92.96 93.3,97.62 92.58,98.21"
        android:strokeLineJoin="round"
        android:strokeWidth="0.41"
        android:fillColor="#00000000"
        android:strokeColor="#525559"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.97,60.83L80.3,65.46C80.39,65.63 80.54,65.77 80.73,65.85C80.91,65.92 81.11,65.94 81.3,65.88C81.5,65.81 81.66,65.66 81.76,65.48C81.84,65.55 81.93,65.62 82.03,65.67C82.04,65.67 82.05,65.67 82.07,65.68L75.32,74.79C75.19,74.97 75.02,75.13 74.83,75.24C74.64,75.36 74.43,75.44 74.2,75.47C73.98,75.51 73.75,75.5 73.53,75.44C73.32,75.39 73.11,75.29 72.93,75.16L66.05,70.06C65.7,69.8 65.46,69.41 65.38,68.98C65.31,68.55 65.4,68.11 65.64,67.74C65.64,67.71 65.64,67.69 65.64,67.66L66.38,66.66C66.72,66.53 67.05,66.36 67.34,66.13C68.84,64.97 71.83,60.83 71.85,60.8L74,61.44L74.03,61.4L76.25,62.43L77.75,65.69C77.84,65.87 77.99,66.01 78.17,66.1C78.35,66.18 78.56,66.19 78.75,66.13C78.86,66.1 78.97,66.04 79.06,65.96C79.15,65.88 79.22,65.78 79.27,65.67C79.32,65.57 79.35,65.45 79.35,65.33C79.35,65.21 79.33,65.09 79.28,64.98L77.88,61.24C77.8,61.05 77.66,60.9 77.48,60.79H77.42L76.38,60.21L77.97,60.83ZM80.59,56.47L85.27,60.27C85.43,60.42 85.64,60.5 85.85,60.5C85.87,60.5 85.89,60.49 85.91,60.49L83.26,64.07L81.18,57.33C81.09,57.06 80.91,56.82 80.66,56.68L80.54,56.61L80.17,56.4L80.59,56.47ZM80.06,49.11C80.51,49.04 80.96,49.16 81.33,49.43L88.21,54.52C88.39,54.65 88.54,54.82 88.66,55.01C88.77,55.21 88.85,55.42 88.88,55.64C88.92,55.86 88.9,56.09 88.85,56.31C88.79,56.53 88.7,56.73 88.56,56.91L86.69,59.43C86.68,59.38 86.67,59.34 86.65,59.3C86.61,59.19 86.54,59.08 86.46,59L81.96,54.35C81.85,54.23 81.71,54.14 81.55,54.1H81.39L76.74,52.75C76.74,52.75 76.73,52.75 76.73,52.74L78.93,49.78C79.2,49.42 79.61,49.18 80.06,49.11Z"
        android:fillColor="#525559"/>
    <path
        android:pathData="M81.88,65.32L79.26,58.91C79.19,58.73 79.05,58.59 78.88,58.51L75.4,56.87"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.22,56.46L76.77,54.95"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.06,60.1L74.26,59.32"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M23.14,51.04C27.06,47.23 33.32,47.33 37.12,51.25C40.73,54.97 40.83,60.8 37.47,64.64L32.34,58.66C32.2,58.5 32.05,58.34 31.9,58.19C31.33,57.5 30.68,56.93 29.85,56.34C28.56,55.42 27.03,54.78 25.49,54.42C24.15,54.11 22.37,54.14 20.9,54.33C21.4,53.12 22.14,52 23.14,51.04Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M62.3,49.97L37.76,64.97L32.34,58.66C31.17,57.31 29.73,56.24 28.13,55.51L24.25,50.11L52.35,31.51L62.3,49.97Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M58.05,54.12C58.31,54.17 58.56,54.26 58.78,54.41C59,54.55 59.2,54.74 59.35,54.96C59.5,55.18 59.6,55.42 59.66,55.68C59.71,55.94 59.71,56.21 59.66,56.47L57.66,68.09C57.59,68.59 57.32,69.04 56.93,69.36C56.53,69.67 56.03,69.83 55.53,69.79L41.24,69.02L32.34,58.66C31.87,58.12 31.36,57.63 30.82,57.19L32.11,48.85L58.05,54.12Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M62.16,55.99L59.99,55.71C59.36,55.63 58.79,56.08 58.71,56.71L57.27,68.14C57.19,68.77 57.64,69.35 58.27,69.43L60.43,69.7C61.06,69.78 61.64,69.33 61.72,68.7L63.15,57.27C63.23,56.64 62.79,56.06 62.16,55.99Z"
        android:fillColor="#5966A9"/>
    <path
        android:pathData="M32.11,48.86L58,54.12C58.4,54.2 58.76,54.39 59.05,54.68C59.33,54.97 59.53,55.33 59.61,55.73C59.72,55.72 59.84,55.72 59.95,55.73L62.11,56C62.41,56.04 62.69,56.2 62.87,56.44C63.06,56.68 63.15,56.99 63.11,57.29L61.71,68.71C61.67,69.01 61.51,69.29 61.27,69.48C61.03,69.66 60.72,69.75 60.42,69.71L58.27,69.44C58.08,69.42 57.91,69.35 57.76,69.25C57.6,69.14 57.48,69 57.4,68.83C57.21,69.15 56.94,69.42 56.62,69.59C56.29,69.77 55.92,69.85 55.55,69.83L45.87,69.31"
        android:strokeLineJoin="round"
        android:strokeWidth="0.41"
        android:fillColor="#00000000"
        android:strokeColor="#525559"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M59.5,57.44L57.68,68.09"
        android:strokeLineJoin="round"
        android:strokeWidth="0.41"
        android:fillColor="#00000000"
        android:strokeColor="#525559"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M58.79,30.4C63.79,32.14 66.25,38.12 64.28,43.75C63.38,45.84 62.08,47.74 60.45,49.34L55.45,53.51"
        android:strokeLineJoin="round"
        android:strokeWidth="0.41"
        android:fillColor="#00000000"
        android:strokeColor="#525559"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M36.87,106.34L34.26,66.18"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M20.61,121.8L35.57,121.51"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.24,14.12C103.94,14.17 104.61,14.41 105.18,14.81C105.75,15.21 106.21,15.75 106.5,16.39C107.18,10.76 106.76,8.24 103.74,5.57C104.22,5.28 104.61,4.87 104.89,4.39C105.17,3.9 105.31,3.35 105.31,2.79C103.22,3.39 101,3.25 99,2.4C97.05,1.56 95.04,0.83 93,0.23C91.97,-0.06 90.89,-0.12 89.83,0.07C88.78,0.26 87.78,0.69 86.92,1.33C86.92,2.19 87,3.07 87.05,3.93C90.35,4.28 93.52,5.38 96.33,7.14C99.14,8.91 101.5,11.3 103.24,14.12Z"
        android:fillColor="#525559"/>
    <path
        android:pathData="M34.21,125.8H35.56C35.84,125.8 36.12,125.69 36.32,125.49C36.52,125.29 36.64,125.01 36.64,124.73V122.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M63.4,57.46L64.28,57.57L71.85,52.14C72.04,51.97 72.27,51.85 72.52,51.78C72.77,51.71 73.03,51.7 73.28,51.75L76,52.49C76.25,52.53 76.49,52.62 76.72,52.74L81.43,54.1H81.59C81.74,54.15 81.88,54.24 81.99,54.35L86.46,59C86.54,59.08 86.61,59.18 86.65,59.29C86.7,59.39 86.72,59.51 86.72,59.62C86.72,59.74 86.7,59.86 86.65,59.96C86.61,60.07 86.54,60.17 86.46,60.25C86.3,60.39 86.09,60.47 85.87,60.47C85.66,60.47 85.45,60.39 85.29,60.25L80.62,56.45L80.25,56.39L80.52,56.54L80.63,56.6C80.88,56.74 81.06,56.97 81.15,57.25L83.37,64.46C83.43,64.7 83.4,64.96 83.28,65.18C83.17,65.4 82.97,65.57 82.74,65.65C82.57,65.7 82.4,65.71 82.23,65.67C82.06,65.62 81.91,65.53 81.79,65.41C81.69,65.6 81.53,65.74 81.33,65.81C81.14,65.87 80.94,65.86 80.75,65.78C80.57,65.7 80.42,65.56 80.33,65.39L77.99,60.76L76.21,60.06L77.45,60.75H77.51C77.69,60.86 77.82,61.02 77.91,61.21L79.23,65C79.31,65.21 79.3,65.44 79.21,65.64C79.12,65.84 78.95,66 78.74,66.08C78.55,66.14 78.34,66.13 78.16,66.05C77.98,65.97 77.83,65.83 77.74,65.65L76.24,62.39L74.05,61.39L71.89,60.76C71.89,60.76 68.89,64.93 67.39,66.09C67.05,66.36 66.66,66.56 66.25,66.7C65.37,67.31 64.31,67.6 63.25,67.52H62.11"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M36.63,136.16H38.39C38.83,136.62 39.43,136.9 40.07,136.94L69.4,138.37M92.82,98.22L93.46,103.11C94.03,103.44 94.59,103.81 95.11,104.21C95.49,104.5 95.86,104.81 96.21,105.13M64.22,95.8L32.34,58.66C31,57.01 29.27,55.72 27.29,54.93C25.32,54.14 23.17,53.87 21.07,54.15C18.96,54.43 16.96,55.24 15.25,56.51C13.55,57.79 12.2,59.48 11.34,61.42C10.29,63.64 9.94,66.12 10.34,68.54L17.53,119.29C17.62,119.94 17.88,120.56 18.27,121.08C18.67,121.61 19.2,122.03 19.8,122.29V124.42C19.8,124.79 19.95,125.14 20.2,125.39C20.46,125.65 20.81,125.8 21.18,125.8H34.18M40.18,120.8C39.7,120.85 39.24,121.04 38.86,121.35C38.49,121.66 38.22,122.08 38.09,122.55H36.64V120.76C36.65,120.72 36.65,120.68 36.64,120.64C36.99,120.25 37.26,119.8 37.43,119.31C37.61,118.82 37.68,118.3 37.64,117.78L36.87,106.29L41.87,112.98L42.4,113.7C44.58,116.44 47.71,118.28 51.17,118.84C51.56,119.05 51.96,119.23 52.38,119.38L52.78,119.51L40.18,120.8Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M69.78,94.84C69.78,94.84 70.78,95.07 72.99,95.55C74.73,95.91 76.92,96.21 76.92,96.21"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M69.45,105.16H108.89L108.85,151.17L71.85,145C71.22,145 70.61,144.75 70.16,144.3C69.71,143.85 69.45,143.24 69.45,142.61V123.06"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.04,123.92H134.7V107.77L141.04,123.92Z"
        android:fillColor="#525559"/>
    <path
        android:pathData="M132.48,143.22L108.85,151.17V105.17L134.7,107.78V140.84C134.7,141.44 134.47,142.02 134.05,142.46C133.64,142.9 133.08,143.18 132.48,143.22Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.33"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.64,147.46V127.02L125.96,126.25V145.41L120.64,147.46Z"
        android:fillColor="#A3AACC"/>
    <path
        android:pathData="M102.67,125.61L61.84,122.39L69.41,105.16H108.85L102.67,125.61Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
  </group>
</vector>
