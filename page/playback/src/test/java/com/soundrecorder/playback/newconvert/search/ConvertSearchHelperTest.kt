/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search

import android.os.Build
import android.util.SparseArray
import androidx.annotation.IntRange
import androidx.core.util.isEmpty
import androidx.core.util.isNotEmpty
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ConvertSearchHelperTest {

    private var helper: ConvertSearchHelper? = null

    @Before
    fun setUp() {
        val list = genConvertContentList()
        helper = ConvertSearchHelper(list)
    }

    private fun genConvertContentList(): List<ConvertContentItem> {
        val list = mutableListOf<ConvertContentItem>()
        list.add(
            genConvertContentItem(
                "我们将继续清除一切侵蚀党的健康机体的变化和大力营造风清气正的政治生态与全党的强大正能量，在全社会凝聚起推动中国发展进步的磅礴力量.",
                2
            )
        )
        list.add(
            genConvertContentItem(
                "党的十九大对全面从严治党作出战略部署，党中央系统总结百年来党坚持自我革命的宝贵经验，深刻把握党的十八大以来全" +
                        "面从严治党的重要经验不松劲，不填数，一以贯之全面，从严治党一刻不停推进反腐败斗争，在反腐败高压态势下，仍然有一些党员干部不收敛，不收手，对于这些人无论是身居要位的高级干部。",
                3
            )
        )
        return list
    }

    /**
     * 生成ConvertContentItem
     */
    private fun genConvertContentItem(
        text: String,
        @IntRange(from = 1) count: Int
    ): ConvertContentItem {
        val subSentences = genSubSentence(text, count)
        var item = ConvertContentItem(textContent = text, listSubSentence = subSentences)
        item.parceNewTextOrImageItems()
//        println("genConvertContentItem $item")
        return item
    }

    /**
     * 生成子句
     * @param text 整段内容
     * @param count 分为多少个字句, count > 0
     */
    private fun genSubSentence(
        text: String,
        @IntRange(from = 1) count: Int
    ): MutableList<ConvertContentItem.SubSentence> {

        val list = mutableListOf<ConvertContentItem.SubSentence>()
        val subLen = text.length / count
        for (i in 0 until count) {
            val start = i * subLen
            var end = start + subLen
            if (i == count - 1) { //最后一个时
                end = text.length
            }
            val subText = text.substring(start, end)
            list.add(
                ConvertContentItem.SubSentence(
                    startCharSeq = start,
                    endCharSeq = end,
                    time = 0f,
                    text = subText
                )
            )
        }
        return list
    }

    @After
    fun tearDown() {
        helper = null
    }

    @Test
    fun should_return_SearchList_when_queryByKeyWord() {
        helper?.let {
            var list = it.queryByKeyWord("")
            Assert.assertEquals(0, list.size)

            list = it.queryByKeyWord("全")
            Assert.assertEquals(5, list.size)
        }
    }

    @Test
    fun should_return_searchBean_when_getSearchBean() {
        helper?.let {
            it.queryByKeyWord("")
            var searchBean = it.getSearchBean(0)
            Assert.assertNull(searchBean)

            var list = it.queryByKeyWord("全")
            Assert.assertEquals(5, list.size)
            searchBean = it.getSearchBean(0)
            Assert.assertNotNull(searchBean)

            searchBean = it.getSearchBean(3)
            Assert.assertNotNull(searchBean)

            searchBean = it.getSearchBean(5)
            Assert.assertNull(searchBean)
        }
    }

    @Test
    fun should_when_queryKeyWordInConvertText() {
        helper?.let {
            val convertContent = genConvertContentItem(
                "我们将继续清除一切侵蚀党的健康机体的变化和大力营造风清气正的政治生态与全党的强大正能量，在全社会凝聚起推动中国发展进步的磅礴力量.",
                2
            )
            var list = Whitebox.invokeMethod<List<ConvertSearchBean>>(
                it,
                "queryKeyWordInConvertText",
                "我们大家",
                convertContent,
                0
            )
            Assert.assertEquals(0, list.size)

            list = Whitebox.invokeMethod<List<ConvertSearchBean>>(
                it,
                "queryKeyWordInConvertText",
                "量",
                convertContent,
                0
            )
            Assert.assertEquals(2, list.size)
        }
    }

    @Test
    fun should_when_queryKeyWordInTextImg() {
        helper?.let {
            val convertText = "我们将继续清除一切侵蚀党的健康机体的变化和大力营造风清气正的政治生态与全党的强大正能量，在全社会凝聚起推动中国发展进步的磅礴力量."
            var contentItem = genConvertContentItem(convertText, 2)
            var textOrImage = contentItem.mTextOrImageItems?.get(1)

            var list = Whitebox.invokeMethod<List<ConvertSearchBean>>(
                it,
                "queryKeyWordInTextImg",
                "我们",
                textOrImage,
                1
            )
            Assert.assertEquals(1, list.size)

            list = Whitebox.invokeMethod<List<ConvertSearchBean>>(
                it,
                "queryKeyWordInTextImg",
                "我们大家",
                textOrImage,
                1
            )
            Assert.assertEquals(0, list.size)
        }
    }

    @Test
    fun should_when_filterSearchResult() {
        helper?.let {
            it.queryByKeyWord("")
            var result = it.filterSearchResult(0, 0)
            Assert.assertTrue(result.isEmpty())

            it.queryByKeyWord("全")
            result = it.filterSearchResult(2, 0)
            Assert.assertTrue(result.isEmpty())

            result = it.filterSearchResult(0, 0)
            Assert.assertTrue(result.isEmpty())
        }
    }

    @Test
    fun should_when_filterCurrentPositionSearchResult() {
        helper?.let {
            it.queryByKeyWord("")
            var list = Whitebox.invokeMethod<MutableList<ConvertSearchBean>>(
                it,
                "filterCurrentPositionSearchResult",
                0,
                0
            )
            Assert.assertTrue(list.isEmpty())

            it.queryByKeyWord("全")
            list = Whitebox.invokeMethod<MutableList<ConvertSearchBean>>(
                it,
                "filterCurrentPositionSearchResult",
                1,
                0
            )
            Assert.assertTrue(list.isNotEmpty())
            Assert.assertTrue(list.get(0).focus)

            list = Whitebox.invokeMethod<MutableList<ConvertSearchBean>>(
                it,
                "filterCurrentPositionSearchResult",
                1,
                1
            )
            Assert.assertTrue(list.isNotEmpty())
            Assert.assertFalse(list.get(0).focus)
        }
    }

    @Test
    fun should_when_splitSearchResultByTextItem() {
        helper?.let {
            var list = mutableListOf<ConvertSearchBean>()
            var result = Whitebox.invokeMethod<SparseArray<MutableList<ConvertSearchBean>>>(
                it,
                "splitSearchResultByTextItem",
                list
            )
            Assert.assertTrue(result.isEmpty())

            list.add(ConvertSearchBean("测试", 0, 0, true))
            list.add(ConvertSearchBean("测试", 0, 1, false))
            list.add(ConvertSearchBean("测试", 0, 2, false))
            list.add(ConvertSearchBean("测试", 1, 0, false))
            list.add(ConvertSearchBean("测试", 2, 1, false))
            list.add(ConvertSearchBean("测试", 2, 2, false))

            result = Whitebox.invokeMethod<SparseArray<MutableList<ConvertSearchBean>>>(
                it,
                "splitSearchResultByTextItem",
                list
            )
            Assert.assertTrue(result.isNotEmpty())
            Assert.assertEquals(3, result.size())
            Assert.assertEquals(3, result.get(0).size)
            Assert.assertEquals(1, result.get(1).size)
            Assert.assertEquals(2, result.get(2).size)
        }
    }
}