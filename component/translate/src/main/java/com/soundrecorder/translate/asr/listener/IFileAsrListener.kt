/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IFileAsrListener
 * Description:
 * Version: 1.0
 * Date: 2025/3/24
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/24 1.0 create
 */

package com.soundrecorder.translate.asr.listener


interface IFileAsrListener {
    fun onStatus(code: Int?, msg: String?)

    fun onAsrResult(result: String?)
}