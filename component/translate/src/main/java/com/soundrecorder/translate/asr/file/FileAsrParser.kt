/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FileAsrParser
 * Description:
 * Version: 1.0
 * Date: 2025/4/15
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/4/15 1.0 create
 */

package com.soundrecorder.translate.asr.file

import com.soundrecorder.translate.AsrConstants

class FileAsrParser {
    companion object {
        enum class FileAsrStatus {

            /**读取文件上传文件失败*/
            AI_PHONE_READ_FILE_UPLOAD_FAIL,

            /**文件上传*/
            AI_PHONE_READ_FILE_UPLOAD_LOADING,

            /**文件上传成功*/
            AI_PHONE_READ_FILE_UPLOAD_SUCCESS,

            /**文件类型不支持*/
            AI_PHONE_READ_FILE_FORMAT_UNSUPPORT,

            /**无网络 上传文件*/
            AI_PHONE_READ_FILE_NO_NET_UPLOAD,

            /**无网络 重试*/
            AI_PHONE_READ_FILE_NO_NET_RETRY,

            /**无网络 消费摘要次数*/
            AI_PHONE_READ_FILE_NO_NET_SUMMARY_CONSUME_COUNT,

            /**无网络 摘要剩余次数查询*/
            AI_PHONE_READ_FILE_NO_NET_SUMMARY_QUERY,

            /**无网络 查询asr文本*/
            AI_PHONE_READ_FILE_NO_NET_QUERY_ASR,

            AI_PHONE_READ_SUMMARY_CONSUME_COMPLETE,

            AI_PHONE_READ_SUMMARY_CONSUME_QUERY,

            AI_PHONE_READ_SUMMARY_STOP,

            AI_PHONE_READ_FILE_QUERY_LOADING,

            AI_PHONE_READ_FILE_QUERY_FAIL,

            AI_PHONE_READ_FILE_QUERY_COMPLETE,

            UNABLE_TO_RECOGNIZE_LANGUAGE,

            THIS_LANGUAGE_IS_NOT_SUPPORTED,

            THIS_LANGUAGE_EMPTY_TEXT,

            FILE_ASR_NET_STATUS_HAS_NET,

            FILE_ASR_NET_STATUS_NO_NET,
        }
    }

    fun convertCodeResult(name: String?, contentStr: String?): AsrConstants.Status {

        return when (name) {
            FileAsrStatus.AI_PHONE_READ_FILE_UPLOAD_FAIL.name -> AsrConstants.Status.ASR_READ_FILE_UPLOAD_FAIL
            FileAsrStatus.AI_PHONE_READ_FILE_UPLOAD_LOADING.name -> AsrConstants.Status.ASR_READ_FILE_UPLOAD_LOADING
            FileAsrStatus.AI_PHONE_READ_FILE_UPLOAD_SUCCESS.name -> AsrConstants.Status.ASR_READ_FILE_UPLOAD_SUCCESS
            FileAsrStatus.AI_PHONE_READ_FILE_FORMAT_UNSUPPORT.name -> AsrConstants.Status.ASR_READ_FILE_FORMAT_UNSUPPORT
            FileAsrStatus.AI_PHONE_READ_FILE_NO_NET_UPLOAD.name -> AsrConstants.Status.ASR_READ_FILE_NO_NET_UPLOAD
            FileAsrStatus.AI_PHONE_READ_FILE_NO_NET_RETRY.name -> AsrConstants.Status.ASR_READ_FILE_NO_NET_RETRY
            FileAsrStatus.AI_PHONE_READ_FILE_NO_NET_SUMMARY_CONSUME_COUNT.name -> AsrConstants.Status.ASR_READ_FILE_NO_NET_SUMMARY_CONSUME_COUNT
            FileAsrStatus.AI_PHONE_READ_FILE_NO_NET_SUMMARY_QUERY.name -> AsrConstants.Status.ASR_READ_FILE_NO_NET_SUMMARY_QUERY
            FileAsrStatus.AI_PHONE_READ_FILE_NO_NET_QUERY_ASR.name -> AsrConstants.Status.ASR_READ_FILE_NO_NET_QUERY_ASR
            FileAsrStatus.AI_PHONE_READ_SUMMARY_CONSUME_COMPLETE.name -> AsrConstants.Status.ASR_READ_SUMMARY_CONSUME_COMPLETE
            FileAsrStatus.AI_PHONE_READ_SUMMARY_CONSUME_QUERY.name -> AsrConstants.Status.ASR_READ_SUMMARY_CONSUME_QUERY
            FileAsrStatus.AI_PHONE_READ_SUMMARY_STOP.name -> AsrConstants.Status.ASR_READ_SUMMARY_STOP
            FileAsrStatus.AI_PHONE_READ_FILE_QUERY_LOADING.name -> AsrConstants.Status.ASR_READ_FILE_QUERY_LOADING
            FileAsrStatus.AI_PHONE_READ_FILE_QUERY_FAIL.name -> AsrConstants.Status.ASR_READ_FILE_QUERY_FAIL
            FileAsrStatus.AI_PHONE_READ_FILE_QUERY_COMPLETE.name -> AsrConstants.Status.ASR_READ_FILE_QUERY_COMPLETE
            FileAsrStatus.FILE_ASR_NET_STATUS_HAS_NET.name -> AsrConstants.Status.ASR_READ_FILE_NET_STATUS_HAS_NET
            FileAsrStatus.FILE_ASR_NET_STATUS_NO_NET.name -> AsrConstants.Status.ASR_READ_FILE_NET_STATUS_NO_NET
            FileAsrStatus.UNABLE_TO_RECOGNIZE_LANGUAGE.name -> AsrConstants.Status.ASR_NO_RECOGNIZE_LANGUAGE
            FileAsrStatus.THIS_LANGUAGE_IS_NOT_SUPPORTED.name -> AsrConstants.Status.ASR_NOT_SUPPORT_LANGUAGE
            FileAsrStatus.THIS_LANGUAGE_EMPTY_TEXT.name -> AsrConstants.Status.ASR_LANGUAGE_EMPTY_TEXT
            else -> AsrConstants.Status.ASR_ERROR_OTHER
        }
    }
}