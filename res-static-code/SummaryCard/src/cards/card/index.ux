<import name="widget-container" src="./components/container/index.ux"></import>
<import name="card-content" src="./card_content.ux"></import>

<template>
  <widget-container onemit-resize="onResize" is-dark="{{ isDark }}" >
    <card-content is-dark="{{ isDark }}"></card-content>
  </widget-container>
</template>

<script>
/**
 * @file 空白模板 - 主流程模块
 * 注意：
 * 1、卡片运行时不会加载app.ux，请不要在app.ux中添加卡片相关逻辑
 * 2、桌面场景卡片容器尺寸会发生变化，内容布局需要自适应
 */
import configuration from '@system.configuration'

import {
  checkVersion,
  checkPermission,
  checkNetwork,
  getRemoteData,
  routerToWlanManager,
  routerToPlatformMarket,
  routerToPlatformSettings,
} from './common/utils'

export default {
  private: {
    isDark: configuration.getThemeMode() === 1
  },
  onErrorCaptured(err, vm, info) {
    console.error('onErrorCaptured error:', err)
  },
  
  
}
</script>
