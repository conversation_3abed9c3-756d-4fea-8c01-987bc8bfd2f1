/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: IOnInfoListener
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.recorder.listener

import android.media.MediaRecorder
import com.oplus.media.OplusRecorder
import com.oppo.media.OppoRecorder

abstract class IOnInfoListener : OppoRecorder.OnInfoListener, OplusRecorder.OnInfoListener, MediaRecorder.OnInfoListener {

    override fun onInfo(mr: OplusRecorder?, what: Int, extra: Int) {
        onInfo(what)
    }

    override fun onInfo(mr: OppoRecorder?, what: Int, extra: Int) {
        onInfo(what)
    }

    override fun onInfo(mr: MediaRecorder?, what: Int, extra: Int) {
        onInfo(what)
    }

    abstract fun onInfo(what: Int)
}