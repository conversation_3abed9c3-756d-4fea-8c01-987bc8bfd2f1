/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardRunnable
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */


package com.oplus.soundrecorder.breenocardlibrary.runnable

import android.view.View
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.log
import java.lang.ref.WeakReference

@Suppress("TooGenericExceptionCaught")
internal class SmallCardRunnable(itemView: View, private val function: View.() -> Unit) : Runnable {
    private val viewWK = WeakReference(itemView)
    override fun run() {
        try {
            viewWK.get()?.function()
        } catch (e: Exception) {
            e.log()
        }
    }
}