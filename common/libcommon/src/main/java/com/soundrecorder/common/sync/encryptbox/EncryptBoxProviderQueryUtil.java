package com.soundrecorder.common.sync.encryptbox;

import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;

import com.soundrecorder.base.utils.DebugUtil;

public class EncryptBoxProviderQueryUtil {

    private static final String TAG = "EncryptBoxProviderQueryUtil";

    public static boolean checkRecordsExistInEncryBox(Context context, String displayName, String relativePath, String md5) {
        if ((context == null) || TextUtils.isEmpty(displayName) || TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(md5)) {
            DebugUtil.i(TAG, "checkRecordsExistInEncryBox displayName: " + displayName);
            return false;
        }
        boolean isExistForOppo = false;
        boolean isExistForOplus = false;
        Uri oppoUri = Uri.parse(EncryptBoxConstant.ENCRYPT_BOX_PROVIDER_URI);
        Uri oplusUri = Uri.parse(EncryptBoxConstant.ENCRYPT_BOX_PROVIDER_OPLUS_URI);
        String where = EncryptBoxConstant.ENCRYPTBOX_COLUMN_NAME_DISPLAYNAME
                + " = ? AND " + EncryptBoxConstant.ENCRYPTBOX_COLUMN_NAME_RELATIEPATH
                + " = ? AND " + EncryptBoxConstant.ENCRYPTBOX_COLUMN_NAME_MD5 + " = ?";
        String[] whereArgs = new String[]{displayName, relativePath, md5};
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(oppoUri, EncryptBoxConstant.ENCRYPTBOX_QUERY_PROJECTION, where, whereArgs, null);
            if (cursor != null) {
                isExistForOppo = cursor.getCount() > 0;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "checkRecordsExistInEncryBox", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        try {
            cursor = context.getContentResolver().query(oplusUri, EncryptBoxConstant.ENCRYPTBOX_QUERY_PROJECTION, where, whereArgs, null);
            if (cursor != null) {
                isExistForOplus = cursor.getCount() > 0;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "checkRecordsExistInEncryBox", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        DebugUtil.i(TAG, "checkRecordsExistInEncryBox isExistForOppo : " + isExistForOppo + ", isExistForOplus: " + isExistForOplus);
        return isExistForOplus || isExistForOppo;
    }

}
