/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonRecordNotificationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.common

import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.lifecycle.MutableLiveData
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.RecordAction
import com.soundrecorder.notification.CommonNotificationModel
import com.soundrecorder.notification.R
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CommonRecordNotificationTest {

    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_equals_when_getOldChannelId() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            3
        )
        Assert.assertEquals(NotificationUtils.RECORDERSERVICE_OLD_CID, notification.getOldChannelId())
    }

    @Test
    fun should_equals_when_getChannelId() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            3
        )
        Assert.assertEquals(NotificationUtils.RECORDERSERVICE_CID, notification.getChannelId())
    }

    @Test
    fun should_equals_when_getChannelName() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        Assert.assertEquals(context?.resources?.getString(R.string.recording_channel_name), notification.getChannelName())
    }

    @Test
    fun should_contains_when_getJumpIntent() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        val jumpIntent = notification.getJumpIntent()
        Assert.assertNull(jumpIntent?.component?.className)
    }

    @Test
    fun should_equals_when_getContentTitle() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        notification.notificationModel = CommonNotificationModel()
            .also {
            it.setCurTime(MutableLiveData(1000))
        }
        val title = Whitebox.invokeMethod<Pair<String, String>>(notification, "getContentTitle").first
        Assert.assertEquals("00:01", title)
    }

    @Test
    fun should_equals_when_getContentText() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        val playStatus = MutableLiveData(NotificationModel.RECORD_STATUS_PAUSE)
        notification.notificationModel = CommonNotificationModel()
            .also {
            it.setPlayStatus(playStatus)
        }
        var text = Whitebox.invokeMethod<Pair<String, String>>(notification, "getContentText").first
        Assert.assertEquals(context?.resources?.getString(R.string.record_pause_tips), text)

        playStatus.value = NotificationModel.RECORD_STATUS_PLAYING
        text = Whitebox.invokeMethod<Pair<String, String>>(notification, "getContentText").first
        Assert.assertEquals(context?.resources?.getString(R.string.recording_notify), text)
    }

    @Test
    fun should_equals_when_isBtnEnabled() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        Assert.assertEquals(false, Whitebox.invokeMethod(notification, "isBtnEnabled"))
    }

    @Test
    fun should_equals_when_isPlaying() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        notification.notificationModel = CommonNotificationModel()
            .also {
            it.setPlayStatus(MutableLiveData(NotificationModel.RECORD_STATUS_PLAYING))
        }
        Assert.assertEquals(true, Whitebox.invokeMethod(notification, "isPlaying"))
    }

    @Test
    fun should_equals_when_getLayoutId() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        notification.notificationModel = CommonNotificationModel()
            .also {
            it.setPlayStatus(MutableLiveData(NotificationModel.RECORD_STATUS_PLAYING))
        }
        Assert.assertTrue(notification.getLayoutId() > 0)
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)

    @Test
    fun should_equals_when_showNotification() {
        Settings.Global.putInt(BaseApplication.getApplication().contentResolver, "oplus_system_folding_mode", 0)
        val mockFeatureOption = Mockito.mockStatic(FeatureOption::class.java)
        mockFeatureOption.`when`<Boolean> {
            FeatureOption.isHasSupportDragonfly()
        }.thenReturn(true)

        val mockRecordAction = Mockito.mockStatic(RecordAction::class.java)
        mockFeatureOption.`when`<Intent> {
            RecordAction.createRecorderIntent(any(Context::class.java), anyBoolean())
        }.thenReturn(Intent())

        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        notification.showNotification(null)
        Assert.assertNotNull(notification.notification)
        Assert.assertEquals(3, notification.notification?.actions?.size)

        mockFeatureOption.close()
        mockRecordAction.close()
    }


    @Test
    fun should_returnTrue_when_isSaveButtonEnable() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        Assert.assertTrue(notification.isSaveButtonEnable())
    }

    @Test
    fun should_notNull_when_getSaveButtonPendingIntent() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        val intent = notification.getSaveButtonPendingIntent()
        Assert.assertNotNull(intent)
    }

    @Test
    fun should_notNull_when_getSaveButtonAction() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )
        val action = notification.getSaveButtonAction()
        Assert.assertNotNull(action)
    }

    @Test
    fun should_correct_when_isBtnEnabled() {
        val notification = CommonRecordNotification(
            NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID,
            0
        )

        notification.notificationModel = CommonNotificationModel().also {
            it.setBtnDisabled(MutableLiveData(false))
            it.setCurTime(MutableLiveData(1))
        }
        Assert.assertTrue(Whitebox.invokeMethod(notification, "isBtnEnabled"))
    }
}