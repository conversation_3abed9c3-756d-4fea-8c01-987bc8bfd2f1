<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/coui_color_background_elevatedWithCard"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <androidx.recyclerview.widget.COUIRecyclerView
        android:id="@+id/choose_group_recycle_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1.0"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/group_fragment_rv_bottom_padding"
        android:paddingStart="@dimen/group_common_list_horizontal_padding"
        android:paddingTop="@dimen/coui_list_to_ex_top_padding"
        android:paddingEnd="@dimen/group_common_list_horizontal_padding"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

    <View
        android:id="@+id/group_choose_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_divider_height"
        android:alpha="1"
        android:background="?attr/couiColorDivider" />


</LinearLayout>