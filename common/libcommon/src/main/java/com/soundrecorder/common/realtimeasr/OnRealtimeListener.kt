/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: OnRealtimeSubtitleUpdateListener
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: W9085798
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9085798 2025/5/18 1.0 create
 */
package com.soundrecorder.common.realtimeasr

interface OnRealtimeListener {
    /**
     * 通知数据有更新
     */
    fun onSubtitleUpdated(cache: IRealtimeSubtitleCache)

    /**
     * ASR异常
     */
    fun onAsrError(code: Int)

    /**
     * 获取支持语言的错误回调
     */
    fun onTranslationCfgError(errorCode: Int, errorMsg: String?)

    /**
     * 获取支持语言的成功回调
     */
    fun onTranslationCfgSuccess(data: Map<String, String>)
}