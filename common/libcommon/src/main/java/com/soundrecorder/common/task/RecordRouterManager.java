package com.soundrecorder.common.task;

import android.app.ActivityManager;
import android.content.Context;
import android.provider.Settings;
import android.text.TextUtils;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.modulerouter.BrowseFileAction;
import com.soundrecorder.modulerouter.recorder.RecordAction;
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant;

import java.util.List;

public class RecordRouterManager {
    public static final String TAG = "RecordRouterManager";
    public static final String PAGE_FROM_NAME = "from";
    public static final String PAGE_FROM_LAUNCHER = "launcher";
    public static final String PAGE_FROM_SLIDE_BAR = "slideBar";
    public static final String PAGE_FROM_BREENO = "breeno";
    public static final String PAGE_FROM_SMALL_CARD = "smallCard";

    public static final String PAGE_FROM_CUBE_BUTTON = "cubeButton";

    public static final String PAGE_FROM_LOCK_SCREEN = "lockScreen";
    public static final String PAGE_FROM_BRACKET_SPACE = "bracketSpace";
    public static final String PAGE_FROM_DRAGON_FLY = "dragonFly";

    public static final String PAGE_FROM_SEEDLING_CARD = "seedlingCard";
    /**
     * 录音摘要卡
     */
    public static final String PAGE_FROM_SUMMARY_CARD = "summaryCard";
    private static final String SUPER_POWERSAVE_MODE_STATE = "super_powersave_mode_state";
    private static final int SUPER_POWERSAVE_MODE_CLOSE = 0;
    private static final int SUPER_POWERSAVE_MODE_OPEN = 1;


    private volatile static RecordRouterManager sInstance = null;
    /**
     * 录制界面从哪儿启动的
     */
    private String recordFrom = "";

    private RecordRouterManager() {

    }

    public static RecordRouterManager getInstance() {
        if (sInstance == null) {
            synchronized (RecordRouterManager.class) {
                if (sInstance == null) {
                    sInstance = new RecordRouterManager();
                }
            }
        }
        return sInstance;
    }

    public void setRecordFrom(String from) {
        this.recordFrom = from;
    }

    public void resetRecordFrom() {
        this.recordFrom = "";
    }

    /**
     * 根据正在录音的启动来源和当前的启动来源，来判断是否拦截启动录制界面
     *
     * 拦截、不拦截的场景：
     * 1.之前没有启动录制，点击录制按钮时，则不需要拦截
     * 2.上一次是通话启动录制，这次是从侧边栏，桌面进入录制，需要进行拦截
     * 3.上一次是通话启动录制，这次也是从通话启动录制，不需要拦截（实测该场景第二次启动通话时，上一次的录制销毁）
     * 4.上一次是从侧边栏启动录制，这次从桌面进入，不需要拦截，直接进入录制界面
     * 5.上一次是从侧边栏启动录制，没有进入过录制界面，这次从通话进入，点击录制按钮时，需要进行拦截
     * 6.上一次是从侧边栏启动录制，点击状态栏录制图标，进入过录制界面，这次从通话进入，点击录制按钮时，需要进行拦截
     * 7.上一次是从桌面启动录制，这次从通话进入，点击录制按钮时，需要进行拦截
     * 8.上一次是从桌面启动录制，这次从侧边栏启动，不需要拦截，直接进入录制界面
     *
     * 综上，不拦截的情况：
     * 1.之前没有启动过录制
     * 2.上一次是侧边栏或桌面启动录制，这次也是侧边栏或桌面启动录制
     * 3.上一次通话启动录制，这次也是通话启动录制（该场景实测不存在）
     *
     * @param comeFrom 启动来源(可空)
     * @return
     */
    public boolean interceptStartRecord(String comeFrom) {
        if (TextUtils.isEmpty(this.recordFrom)) { // 没有录制,则不进行拦截
            return false;
        }
        DebugUtil.d(TAG, "interceptStartRecord recordFrom " + this.recordFrom);
        if (RecorderDataConstant.PAGE_FROM_CALL.equals(comeFrom)) {
            //从通话启动，则判断之前录制来源是否是通话，如果不是，则进行拦截
            return !TextUtils.equals(this.recordFrom, RecorderDataConstant.PAGE_FROM_CALL);
        } else { //从侧边栏或者小布或者桌面启动，则判断录制来源是否是通话，如果是通话，则进行拦截
            return TextUtils.equals(this.recordFrom, RecorderDataConstant.PAGE_FROM_CALL);
        }
    }

    /**
     * 超级省电模式是否打开
     *
     * @param context
     * @return   false: 0，关闭   true: 1，打开
     */
    public boolean isSuperPowerSaveModeState(Context context) {
        return Settings.System.getInt(context.getContentResolver(),
                SUPER_POWERSAVE_MODE_STATE, SUPER_POWERSAVE_MODE_CLOSE) == SUPER_POWERSAVE_MODE_OPEN;
    }

    /**
     * The activity at the bottom of the stack determines whether the desktop or sidebar start record
     */
    public boolean getBaseActivityIsSelf(Context context) {
        android.app.ActivityManager manager = (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> runningTaskInfos = null;
        if (manager != null) {
            runningTaskInfos = manager.getRunningTasks(1);
        }

        if ((runningTaskInfos != null) && (runningTaskInfos.size() != 0)) {
            String baseActivity = (runningTaskInfos.get(0).baseActivity).toString();
            DebugUtil.d(TAG, "baseActivity is " + baseActivity);
            Class<?> browseFileClass = BrowseFileAction.getBrowseFileClass();
            Class<?>recorderClass = RecordAction.getRecorderActivityClass();
            return (browseFileClass != null && baseActivity.contains(browseFileClass.getName()))
                    || (recorderClass != null && baseActivity.contains(recorderClass.getName()));
        } else {
            return false;
        }
    }
}
