<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 如果业务只需要支持Android T以后，则申请以下权限-->
    <uses-permission android:name="com.oplus.permission.safe.AUTHENTICATE" />
    <!--如果业务需要支持T以前的版本，则可以申请以下权限-->
    <uses-permission android:name="oplus.permission.OPLUS_COMPONENT_SAFE" />
    <application>
        <!-- AIUnit的鉴权标识符 -->
        <meta-data
            android:name="com.oplus.aiunit.auth_style"
            android:value="0" />
        <!-- AIUnit中要接入下面两个下载metaData，代表接入的新流程，支持通过插件下载，和前前确认内销预置也没问题 -->
        <meta-data
            android:name="aiunit_download_enable"
            android:value="true" />
        <meta-data
            android:name="aiunit_download_group"
            android:value="cloud_call_summary,ai_asr_detector,unified_summary,unified_asr_summary" />

    </application>
</manifest>