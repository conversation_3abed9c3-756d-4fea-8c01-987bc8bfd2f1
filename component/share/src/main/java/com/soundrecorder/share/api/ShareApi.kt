/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareApi
 * * Description: Share<PERSON><PERSON>
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.share.api

import android.app.Activity
import android.view.View
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.common.share.IShareListener
import com.soundrecorder.common.share.ShareAction
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.share.ShareType
import com.soundrecorder.share.ShareManager
import com.soundrecorder.share.normal.link.manager.LinkShareTask
import com.soundrecorder.share.normal.link.utils.ShareLinkUtils
import kotlinx.coroutines.CoroutineScope

@Component(ShareAction.COMPONENT_NAME)
object ShareApi {

    /**
     * 分享入口
     * @param shareTextContent 文本数据
     * @param coroutineScope 分享协程作用域
     * @param shareListener 分享状态监听
     * @param type 分享类型，不同的分享可能携带有自己特有的数据
     */
    @Action(ShareAction.FUN_SHARE)
    @JvmStatic
    fun share(
        activity: Activity?,
        shareTextContent: ShareTextContent,
        coroutineScope: CoroutineScope?,
        shareListener: IShareListener?,
        type: ShareType
    ) {
        ShareManager.share(activity, shareTextContent, coroutineScope, shareListener, type)
    }

    @Action(ShareAction.FUN_REGISTER_SHARE_LISTENER)
    @JvmStatic
    fun registerShareListener(listener: IShareListener) {
        ShareManager.registerShareListener(listener)
    }

    @Action(ShareAction.FUN_UNREGISTER_SHARE_LISTENER)
    @JvmStatic
    fun unregisterShareListener(listener: IShareListener) {
        ShareManager.unregisterShareListener(listener)
    }

    @Action(ShareAction.SHOW_SHARE_LINK_PANEL)
    @JvmStatic
    fun showShareLinkPanel(activity: Activity, link: String, anchor: View?) {
        ShareLinkUtils.showShareLinkPanel(activity, link, anchor)
    }

    @Action(ShareAction.CAN_UPLOAD_MORE_AUDIO_FILES)
    @JvmStatic
    fun canUploadMoreAudioFiles(): Boolean {
        return LinkShareTask.canUploadMoreAudioFiles()
    }
}