package io.noties.markwon.ext.tasklist;

import androidx.annotation.NonNull;

import org.commonmark.node.CustomBlock;

/**
 * @since 1.0.1
 */
@SuppressWarnings("WeakerAccess")
public class TaskListItem extends CustomBlock {

    private final boolean isDone;
    private final String task;

    public TaskListItem(boolean isDone, String task) {
        this.isDone = isDone;
        this.task = task;
    }

    public boolean isDone() {
        return isDone;
    }

    public String getTask() {
        return task;
    }

    @Override
    @NonNull
    public String toString() {
        return "TaskListItem{" +
                "isDone=" + isDone +
                ", task=" + task +
                '}';
    }
}
