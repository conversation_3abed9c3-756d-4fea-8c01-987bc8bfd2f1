package com.soundrecorder.common.db

import android.content.ContentValues
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.net.Uri
import android.text.TextUtils
import androidx.annotation.WorkerThread
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_PICTURE
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.imageload.utils.BitmapUtil

object PictureMarkDbUtils {
    const val TAG = "PictureMarkDbUtils"
    const val TABLE_NAME_PICTURE_NAME = "picture_mark"
    const val DATABASE_VERSION_PICTURE_MARK = 8
    const val ID = "_id"
    const val KEY_ID = "key_id"
    const val TIME_IN_MILLS = "time_in_mills"
    const val VERSION = "version"
    const val DEFAULT_NO = "default_no"
    const val MARK_TEXT = "mark_text"
    const val PICTURE_FILE_PATH = "picture_file_path"
    const val TEMP_ID = "temp_id"
    const val PROVIDER_PICTURE_MARK_TYPE = "vnd.android.cursor.dir/picture_mark"

    private val pictureMarkUri = DatabaseConstant.getContentUri(TABLE_NAME_PICTURE_NAME)

    @JvmStatic
    fun createPictureMArkTable(db: SQLiteDatabase) {
        db.execSQL("CREATE TABLE IF NOT EXISTS " + TABLE_NAME_PICTURE_NAME + " ("
                + ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                + KEY_ID + " TEXT,"
                + TIME_IN_MILLS + " BIGINT DEFAULT 0,"
                + VERSION + " INTEGER,"
                + DEFAULT_NO + " INTEGER,"
                + MARK_TEXT + " TEXT,"
                + PICTURE_FILE_PATH + " TEXT"
                + ");")
        DebugUtil.i(TAG, "createPictureMArkTable")
    }

    @JvmStatic
    fun downgradePictureMarkTable(sqLiteDatabase: SQLiteDatabase, toVersion: Int) {
        if (toVersion < DATABASE_VERSION_PICTURE_MARK) {
            sqLiteDatabase.execSQL("DROP TABLE IF EXISTS $TABLE_NAME_PICTURE_NAME")
        }
    }

    @JvmStatic
    fun upgradePictureMarkTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        if ((fromVersion < DATABASE_VERSION_PICTURE_MARK) && (toVersion >= DATABASE_VERSION_PICTURE_MARK)) {
            createPictureMArkTable(db)
            DebugUtil.i(TAG, "upgrade and createPictureMArkTable")
        }
    }


    private fun getContentResolver() = BaseApplication.getAppContext().contentResolver

    @WorkerThread
    @JvmStatic
    fun addPictureMark(keyId: String, data: MarkDataBean) {
        return try {
            val uri = getContentResolver().insert(
                pictureMarkUri,
                ContentValues().apply {
                    put(KEY_ID, keyId)
                    put(TIME_IN_MILLS, data.timeInMills)
                    put(VERSION, data.version)
                    put(DEFAULT_NO, data.defaultNo)
                    put(MARK_TEXT, data.markText)
                    put(PICTURE_FILE_PATH, data.pictureFilePath)
                })
            DebugUtil.i(TAG, "addPictureMark mark succuss $uri, insertMark $data")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "addPictureMark error", e)
        }
    }


    @WorkerThread
    @JvmStatic
    fun addPictureMarks(keyId: String, datas: List<MarkDataBean>) {
        return try {
            datas.forEach {
                val uri = getContentResolver().insert(
                    pictureMarkUri,
                    ContentValues().apply {
                        put(KEY_ID, keyId)
                        put(TIME_IN_MILLS, it.timeInMills)
                        put(VERSION, it.version)
                        put(DEFAULT_NO, it.defaultNo)
                        put(MARK_TEXT, it.markText)
                        put(PICTURE_FILE_PATH, it.pictureFilePath)
                    })
                DebugUtil.i(TAG, "addPictureMarks mark succuss $uri, insetMark: $it")
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "addPictureMarks error", e)
        }
    }

    @WorkerThread
    fun queryPictureMarks(keyId: String): MutableList<MarkDataBean> {
        val where = "$KEY_ID = ?"
        val whereArgs = arrayOf(keyId)
        var cursor: Cursor? = null
        val marks = mutableListOf<MarkDataBean>()
        try {
            cursor = getContentResolver().query(
                pictureMarkUri,
                null,
                where,
                whereArgs,
                null
            )
            var index = -1
            if (cursor != null && cursor.moveToFirst()) {
                do {
                    index = cursor.getColumnIndex(TIME_IN_MILLS)
                    val time = if (index >= 0)  cursor.getLong(index) else -1L
                    if (time != -1L) {
                        val mark = MarkDataBean(time, VERSION_PICTURE)
                        index = cursor.getColumnIndex(DEFAULT_NO)
                        if (index >= 0) mark.defaultNo = cursor.getInt(index)
                        index = cursor.getColumnIndex(MARK_TEXT)
                        if (index >= 0) mark.markText = cursor.getString(index)
                        index = cursor.getColumnIndex(PICTURE_FILE_PATH)
                        if (index >= 0) mark.pictureFilePath = cursor.getString(index)
                        index = cursor.getColumnIndex(KEY_ID)
                        if (index >= 0) mark.keyId = cursor.getString(index)
                        if (mark.pictureFilePath.isEmpty()) {
                            mark.version = com.soundrecorder.common.utils.MarkSerializUtil.VERSION_NEW
                        } else {
                            val realFile = FileUtils.getAppFile(mark.pictureFilePath, false)
                            DebugUtil.i(TAG, "queryAllPictureMarks mark pictureFile $realFile")
                            val bitMapDimen = BitmapUtil.getBitmapWithAndHeight(realFile)
                            mark.pictureWith = bitMapDimen.width
                            mark.pictureHeight = bitMapDimen.height
                            DebugUtil.i(TAG, "queryPictureMarks mark pictureWith ${mark.pictureWith}, pictureHeight ${mark.pictureHeight}")
                        }
                        marks.add(mark)
                    }
                } while (cursor.moveToNext())
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryPictureMarks error", e)
        } finally {
            cursor?.close()
        }
        return marks
    }

    @WorkerThread
    @JvmStatic
    fun queryAllPictureMarks(): MutableList<MarkDataBean> {
        var cursor: Cursor? = null
        val marks = mutableListOf<MarkDataBean>()
        try {
            cursor = getContentResolver().query(
                pictureMarkUri,
                null,
                null,
                null,
                null
            )
            if (cursor != null && cursor.moveToFirst()) {
                val timeIndex = cursor.getColumnIndex(TIME_IN_MILLS)
                val noIndex = cursor.getColumnIndex(DEFAULT_NO)
                val textIndex = cursor.getColumnIndex(MARK_TEXT)
                val picPathIndex = cursor.getColumnIndex(PICTURE_FILE_PATH)
                val keyIdIndex = cursor.getColumnIndex(KEY_ID)
                do {
                    val time = if (timeIndex >= 0)  cursor.getLong(timeIndex) else -1L
                    if (time != -1L) {
                        val mark = MarkDataBean(time, VERSION_PICTURE)
                        if (noIndex >= 0) mark.defaultNo = cursor.getInt(noIndex)
                        if (textIndex >= 0) mark.markText = cursor.getString(textIndex)
                        if (picPathIndex >= 0) mark.pictureFilePath = cursor.getString(picPathIndex)
                        if (keyIdIndex >= 0) mark.keyId = cursor.getString(keyIdIndex)
                        if (mark.pictureFilePath.isEmpty()) {
                            mark.version = MarkSerializUtil.VERSION_NEW
                        } else {
                            val realFile = FileUtils.getAppFile(mark.pictureFilePath, false)
                            DebugUtil.i(TAG, "queryAllPictureMarks mark pictureFile $realFile")
                            val bitMapDimen = BitmapUtil.getBitmapWithAndHeight(realFile)
                            mark.pictureWith = bitMapDimen.width
                            mark.pictureHeight = bitMapDimen.height
                            DebugUtil.i(TAG, "queryAllPictureMarks mark pictureWith ${mark.pictureWith}, pictureHeight ${mark.pictureHeight}")
                        }
                        marks.add(mark)
                    }
                } while (cursor.moveToNext())
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryAllPictureMarks error", e)
        } finally {
            cursor?.close()
        }
        return marks
    }

    @WorkerThread
    fun queryPictureMarksByTimeInMills(keyId: String, timeInMills: Long): Int {
        val where = "$KEY_ID = ? AND $TIME_IN_MILLS = ?"
        val whereArgs = arrayOf(keyId, timeInMills.toString())
        var cursor: Cursor? = null
        var count = -1
        try {
            cursor = getContentResolver().query(
                pictureMarkUri,
                null,
                where,
                whereArgs,
                null
            )
            count = cursor?.count ?: -1
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryPictureMarksByTimeInMills error", e)
        } finally {
            cursor?.close()
        }
        return count
    }


    @WorkerThread
    fun queryPictureMarksCntByPicturePath(pictureFilePath: String): Int {
        val where = "$PICTURE_FILE_PATH = ?"
        val whereArgs = arrayOf(pictureFilePath)
        var cursor: Cursor? = null
        var count = -1
        try {
            cursor = getContentResolver().query(
                pictureMarkUri,
                null,
                where,
                whereArgs,
                null
            )
            count = cursor?.count ?: -1
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryPictureMarksCntByPicturePath error", e)
        } finally {
            cursor?.close()
        }
        return count
    }


    @WorkerThread
    fun deletePictureMark(keyId: String, timeInMills: Long): Int {
        val where = "$KEY_ID = ? AND $TIME_IN_MILLS = ?"
        val whereArgs = arrayOf(keyId, timeInMills.toString())
        return try {
            val count = getContentResolver().delete(
                pictureMarkUri,
                where,
                whereArgs
            )
            DebugUtil.w(TAG, "deletePictureMMark: keyId: $keyId, timeInMills $timeInMills,  deleteCount: $count")
            count
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deletePictureMark error", e)
            0
        }
    }

    @WorkerThread
    fun deletePictureMarkWithMarkPictureFile(keyId: String, timeInMills: Long): Int {
        val where = "$KEY_ID = ? AND $TIME_IN_MILLS = ?"
        val whereArgs = arrayOf(keyId, timeInMills.toString())
        val projection = arrayOf(PICTURE_FILE_PATH)
        var cursor: Cursor? = null
        var pictureFilePath: String?
        val deleteResult: Int = -1
        try {
            cursor = getContentResolver().query(
                pictureMarkUri,
                projection,
                where,
                whereArgs,
                null)
            var fileDeleteCnt = 0
            if (cursor != null && cursor.moveToFirst()) {
                var filPathIndex = -1
                do {
                    filPathIndex = cursor.getColumnIndex(PICTURE_FILE_PATH)
                    pictureFilePath = if (filPathIndex >= 0) cursor.getString(filPathIndex) else ""
                    if (!TextUtils.isEmpty(pictureFilePath)) {
                        val cnt = pictureFilePath?.let { queryPictureMarksCntByPicturePath(it) } ?: 0
                        if (cnt <= 1) {
                            // ==1 表明当前只有一个标记关联该图片文件，需要删除； <1 表明数据库中已经没有记录关联该数据，为脏数据，需要删除
                            val delectFileSuc = FileUtils.getAppFile(pictureFilePath, false).delete()
                            DebugUtil.i(TAG,
                                "delectPictureFileSuc path : $pictureFilePath, delectFileSuc $delectFileSuc")
                            if (delectFileSuc) {
                                fileDeleteCnt++
                            }
                        } else {
                            // >1 表明当前数据库中有多个标记记录关联该文件，只删除数据库，不删除文件
                            DebugUtil.i(TAG, "other record map this picture file , do not delete this picture file")
                        }
                    }
                } while (cursor.moveToNext())
            }

            val deleteResult = getContentResolver().delete(
                pictureMarkUri,
                where,
                whereArgs
            )
            DebugUtil.w(TAG, "deletePictureMMark: KeyId: $keyId, timeInMills $timeInMills,  deleteCount: $deleteResult")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deletePictureMarkWithMarkPictureFile error", e)
        } finally {
            cursor?.close()
        }
        return deleteResult
    }


    @WorkerThread
    fun deletePictureMarks(keyId: String): Int {
        val where = "$KEY_ID = ?"
        val whereArgs = arrayOf(keyId)
        return try {
            val count = getContentResolver().delete(
                pictureMarkUri,
                where,
                whereArgs
            )
            DebugUtil.w(TAG, "deletePictureMarks: keyId: $keyId, deleteCount: $count")
            count
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deletePictureMarks error", e)
            0
        }
    }


    @WorkerThread
    fun deleteOnlyTextMarks(keyId: String): Int {
        val where = "$KEY_ID = ? AND $VERSION < ?"
        val whereArgs = arrayOf(keyId, VERSION_PICTURE.toString())
        return try {
            val count = getContentResolver().delete(
                pictureMarkUri,
                where,
                whereArgs
            )
            DebugUtil.w(TAG, "deletePictureMarks: keyId: $keyId, deleteCount: $count")
            count
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deletePictureMarks error", e)
            0
        }
    }


    /**
     *
     */
    @WorkerThread
    @JvmStatic
    fun deletePictureMarksWithMarkPictureFiles(keyId: String): Int {
        val where = "$KEY_ID = ?"
        val whereArgs = arrayOf(keyId)
        val projection = arrayOf(PICTURE_FILE_PATH)
        var cursor: Cursor? = null
        var pictureFilePath: String?
        var deleteResult: Int = -1
        try {
            cursor = getContentResolver().query(
                pictureMarkUri,
                projection,
                where,
                whereArgs,
                null)
            var fileDeleteCnt = 0
            if (cursor != null && cursor.moveToFirst()) {
                var filPathIndex = -1
                do {
                    filPathIndex = cursor.getColumnIndex(PICTURE_FILE_PATH)
                    pictureFilePath = if (filPathIndex >= 0) cursor.getString(filPathIndex) else ""
                    if (!TextUtils.isEmpty(pictureFilePath)) {
                        val cnt = pictureFilePath?.let { queryPictureMarksCntByPicturePath(it) } ?: 0
                        if (cnt <= 1) {
                            // ==1 表明当前只有一个标记关联该图片文件，需要删除； <1 表明数据库中已经没有记录关联该数据，为脏数据，需要删除
                            val delectFileSuc = FileUtils.getAppFile(pictureFilePath, false).delete()
                            DebugUtil.i(TAG,
                                "delectPictureFileSuc path : $pictureFilePath, delectFileSuc $delectFileSuc")
                            if (delectFileSuc) {
                                fileDeleteCnt++
                            }
                        } else {
                            // >1 表明当前数据库中有多个标记记录关联该文件，只删除数据库，不删除文件
                            DebugUtil.i(TAG, "other record map this picture file , do not delete this picture file")
                        }
                    }
                } while (cursor.moveToNext())
            }
            deleteResult = getContentResolver().delete(
                pictureMarkUri,
                where,
                whereArgs
            )
            DebugUtil.i(TAG,
                "deletePictureMMark: KeyId: $keyId, fileDeleteCnt $fileDeleteCnt, deleteCount: $deleteResult")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deletePictureMarksWithMarkPictureFiles error", e)
        } finally {
            cursor?.close()
        }
        return deleteResult
    }


    @WorkerThread
    fun updatePictureMark(keyId: String, data: MarkDataBean): Int {
        val cv = ContentValues().apply {
            put(KEY_ID, keyId)
            put(TIME_IN_MILLS, data.timeInMills)
            put(VERSION, data.version)
            put(DEFAULT_NO, data.defaultNo)
            put(MARK_TEXT, data.markText)
            put(PICTURE_FILE_PATH, data.pictureFilePath)
        }
        val where = "$KEY_ID = ? AND $TIME_IN_MILLS = ?"
        var updateCnt = 0
        try {
            updateCnt = getContentResolver().update(
                pictureMarkUri,
                cv,
                where,
                arrayOf(keyId, data.timeInMills.toString()))
        } catch (e: Exception) {
            DebugUtil.e(TAG, "updatePictureMark error")
        }
        return updateCnt
    }

    fun getPictureMarkUri(): Uri = pictureMarkUri
}