/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: UsbEnvironmentCompat
 * Description:
 * Version: 1.0
 * Date: 2023/10/7
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/10/7 1.0 create
 */

package com.soundrecorder.base.utils

import android.content.Context
import android.os.UserHandle
import com.oplus.recorderlog.util.AddonAdapterCompat
import java.io.File

object AddonAdapterCompatUtil {

    @JvmStatic
    fun isVolumeMounted(context: Context?, path: String): Boolean {
        return AddonAdapterCompat.isVolumeMounted(context, path)
    }

    @JvmStatic
    fun getInternalSdDirectory(context: Context?): File? {
        return AddonAdapterCompat.getInternalSdDirectory(context)
    }

    @JvmStatic
    fun getInternalSdState(context: Context?): String? {
        return AddonAdapterCompat.getInternalSdState(context)
    }

    @JvmStatic
    fun getInternalPath(context: Context?): String? {
        return AddonAdapterCompat.getInternalPath(context)
    }

    @JvmStatic
    fun getExternalSdDirectory(context: Context?): File? {
        return AddonAdapterCompat.getExternalSdDirectory(context)
    }

    @JvmStatic
    fun getExternalSdState(context: Context?): String? {
        return AddonAdapterCompat.getExternalSdState(context)
    }

    @JvmStatic
    fun getExternalPath(context: Context?): String? {
        return AddonAdapterCompat.getExternalPath(context)
    }

    @JvmStatic
    fun getExternalStorageDirectory(): File? {
        return AddonAdapterCompat.getExternalStorageDirectory()
    }

    @JvmStatic
    fun isInternalSdMounted(context: Context?): Boolean {
        return AddonAdapterCompat.isInternalSdMounted(context)
    }

    @JvmStatic
    fun isExternalSdMounted(context: Context?): Boolean {
        return AddonAdapterCompat.isExternalSdMounted(context)
    }

    @JvmStatic
    fun isMultiSystemUserHandle(userHandle: UserHandle): Boolean {
        return AddonAdapterCompat.isMultiSystemUserHandle(userHandle)
    }

    @JvmStatic
    fun getOplusOSVERSION(): Int {
        return AddonAdapterCompat.getOplusOSVERSION()
    }
    @JvmStatic
    fun getVirtualcommDeviceType(context: Context): Int {
        return AddonAdapterCompat.getVirtualcommDeviceType(context)
    }

    @JvmStatic
    fun addBackgroundRestrictedInfo(packageName: String, pkgList: List<String>) {
        return AddonAdapterCompat.addBackgroundRestrictedInfo(packageName, pkgList)
    }
}