package com.soundrecorder.modulerouter.cloudkit.tipstatus

/**
 * 首页副标题对应不同同步状态
 */
interface ITipStatus {
    companion object {
        const val STATE_NONE = 1
        const val STATE_CLOUD_OFF = 2
        const val STATE_NO_ALL_ACCESS_PERMISSION = 3
        const val STATE_COMPLETED = 4
        const val STATE_QUERY = 5
        const val STATE_SYNCING = 6
        const val STATE_NO_CLOUD_SPACE = 7
        const val STATE_FAILURE = 8
        const val STATE_SETTINGS = 9 //从云同步服务端获取配置tips商业化词条
    }


    var tipIconResId: Int
    var state: Int

    fun tipText(): Pair<String, String>
}