/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AIRealTimeAsrClient
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/6 1.0 create
 */

package com.soundrecorder.translate.asr.realtime

import android.content.Context
import android.os.Bundle
import com.oplus.ai.asrkit.AiAsrKit
import com.oplus.ai.asrkit.callback.AsrCallback
import com.oplus.ai.asrkit.callback.RequestCallback
import com.oplus.ai.asrkit.common.Constants
import com.oplus.ai.asrkit.data.config.BaseConfigData
import com.oplus.ai.asrkit.data.config.InitConfigData
import com.oplus.ai.asrkit.data.config.ReleaseConfigData
import com.oplus.ai.asrkit.data.request.AudioRealtimeAsrRequest
import com.oplus.ai.asrkit.data.request.EndRealtimeAsrRequest
import com.oplus.ai.asrkit.data.request.StartRealTimeAsrRequest
import com.oplus.ai.asrkit.data.request.TranslationRequest
import com.oplus.ai.asrkit.data.response.AsrAckRsp
import com.oplus.ai.asrkit.data.response.AsrAudioAck
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OpenIdUtils
import com.soundrecorder.translate.AsrConstants.APP_TAG
import com.soundrecorder.translate.AsrConstants.SUB_APP_TAG
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener
import java.util.Locale
import java.util.UUID

/**
 * 实时 asr
 * https://odocs.myoas.com/docs/wV3VVxNB4MsDKv3y
 */
class AIRealTimeAsrClient(var context: Context?) : IRealTimeAsrKit {
    companion object {
        private const val TAG = "AIRealTimeAsrClient"

        const val EXTRA_CHANNEL_ID = "channel_id"
        const val EXTRA_FILE_RECORD_ID = "record_id"
        const val EXTRA_SAMPLE_CHANNEL = "channel"
        const val EXTRA_SAMPLE_BYTES = "sampleBytes"
        const val EXTRA_SAMPLE_RATE = "sampleRate"
        const val EXTRA_SOURCE_LANGUAGE = "sourceLanguage"
        const val EXTRA_FORMAT = "format"
    }

    private var asrListener: IRealTimeAsrListener? = null
    private var asrClient: AiAsrKit? = null
    private var asrDataParser: RealTimeAsrParser? = null

    private val deviceDId by lazy {
        OpenIdUtils.INSTANCE.duid.run {
            /*调式中发现重置手机后有概率第一次进入录音，获取到duid为null，这里坐下处理，为null就使用uuid。该字段使用于记录摘要可用次数，若为null当做另外设备*/
            if (isBlank()) {
                DebugUtil.w(TAG, "file asr did is empty")
                return@run OpenIdUtils.INSTANCE.uuid
            }
            return@run this
        }
    }

    /*目前都走云侧*/
    private val asrType = Constants.AsrModelType.CLOUD

    init {
        context?.let { asrClient = AiAsrKit(it, asrType) }
        asrDataParser = RealTimeAsrParser()
    }

    override fun registerAsrListener(listener: IRealTimeAsrListener?) {
        asrListener = listener
    }

    override fun unRegisterAsrListener(listener: IRealTimeAsrListener?) {
        asrListener = null
    }

    override fun initAsr(params: Bundle?) {
        val channelId = params?.getString(EXTRA_CHANNEL_ID)
        DebugUtil.i(TAG, "initAsr,$channelId")
        val config = InitConfigData(
                modelType = asrType, // 云侧or端侧
                channelId = channelId, // 实时长连接channelID
                callId = "", //非实时callId
                appTag = APP_TAG,
                subAppTag = SUB_APP_TAG,
                businessType = Constants.AsrBusinessType.REALTIME,
        )
        asrClient?.initAsr(config, object : AsrCallback {

            override fun onAsr(audioAck: AsrAudioAck) {
                DebugUtil.i(TAG, "init onAsr: type=${audioAck.type}, startOffset=${audioAck.startOffset}," +
                        " speakerId=${audioAck.speakerId}, msgId=${audioAck.msgId}")
                DebugUtil.i(TAG, "init onAsr: text=${audioAck.text}")
                asrDataParser?.onAsr(channelId, audioAck, asrListener)
            }

            override fun onError(errorCode: Int, errorMsg: String?, expends: String?) {
                DebugUtil.w(TAG, "init onError,$errorCode-$errorMsg-$expends")
                asrListener?.onStatus(channelId, null, errorCode, errorMsg)
            }

            override fun onResult(result: AsrAckRsp?) {
                DebugUtil.i(TAG, "init onResult $result")
                asrListener?.onStatus(result?.channelId
                        ?: channelId, result?.bizType, result?.code, result?.message)
            }
        })
    }

    override fun startAsr(channelId: String, extra: Bundle?) {
        val recordId = extra?.getString(EXTRA_FILE_RECORD_ID)
        val sampleRate = extra?.getInt(EXTRA_SAMPLE_RATE)
        val sampleBytes = extra?.getInt(EXTRA_SAMPLE_BYTES)
        val channel = extra?.getInt(EXTRA_SAMPLE_CHANNEL)
        val sourceLanguage = extra?.getString(EXTRA_SOURCE_LANGUAGE) ?: Locale.CHINESE.language
        val format = extra?.getString(EXTRA_FORMAT)
        DebugUtil.i(TAG, "startAsr $recordId, channelId=$channelId")

        asrClient?.startRealTimeAsr(
                StartRealTimeAsrRequest(
                        recordId = recordId ?: "", //必填，需要识别的完整音频的 ID。每个完整音频唯一
                        sourceLanguage = sourceLanguage, //源语言，语言代码(bcp-47)
                        format = format, // 非必填，默认 opus，可选 pcm（需要和提供确认）
                        channel = channel, //单双声道，默认 1；取值：1，2
                        sampleBytes = sampleBytes, //位深，每个采样的字节数，默认 2
                        sampleRate = sampleRate, //采样率，默认 16000
                        frameSize = 640, //pcm/ 每帧音频采样个数，默认 640z。320(20ms)，640(40ms)
                        enableLid = false, //是否开启语种校验，若开启，服务端会根据前1分钟有效音频检测语种，并返回，默认为False
                        enableRisk = true, //是否开启敏感词检测，若开启，服务端会返回敏感词内容。默认为False
                        enableSmooth = !BaseUtil.isEXP(), //是否开启语气词过率，若开启，服务端会返回过滤后的文本。默认为False
                        enableContinue = false, //是否开启连续识别，若开启则返回中间的识别内容，若不开启则返回断句后的内容。默认为False
                        enableNetProbe = false, //是否开启网络探测，若开启，探测评估全链路RTT和网络抖动
                        enableAutoSplit = true, // 是否开启说话人，默认为false，开启传true
                        channelId = channelId, appTag = APP_TAG, extends = null))
    }

    private var i = 0
    override fun processRealTimeData(channelId: String, byteArray: ByteArray?) {
        if (byteArray == null) {
            DebugUtil.w(TAG, "processRealTimeData data is null ")
            return
        }
        asrClient?.processRealTimeAudio(
                AudioRealtimeAsrRequest(
                        audioData = byteArray, channelId = channelId, msgId = "$i", appTag = APP_TAG, extends = null))
        i++
    }

    override fun stopAsr(channelId: String) {
        DebugUtil.i(TAG, "stopAsr $channelId")
        asrClient?.stopRealTimeAsr(EndRealtimeAsrRequest(appTag = APP_TAG, channelId = channelId, extends = null))
    }

    override fun releaseChannel(channelId: String) {
        DebugUtil.i(TAG, "releaseChannel $channelId")
        asrClient?.release(ReleaseConfigData(channelId = channelId,
                appTag = APP_TAG, subAppTag = SUB_APP_TAG, businessType = Constants.AsrBusinessType.REALTIME))
    }

    override fun release() {
        DebugUtil.i(TAG, "release")
        asrClient?.release(BaseConfigData(appTag = APP_TAG, subAppTag = SUB_APP_TAG, businessType = Constants.AsrBusinessType.REALTIME))
        asrClient = null
        asrDataParser = null
        context = null
    }

    override fun getTranslationConfig(channelId: String, isInnerInInvoke: Boolean) {
        DebugUtil.i(TAG, "getTranslationConfig $channelId, isInnerInInvoke=$isInnerInInvoke")
        val translationRequest = TranslationRequest(
            language = "zh",
            duId = deviceDId,
            extends = mapOf()
        )
        asrClient?.getTranslationConfig(translationRequest, object : RequestCallback {
            override fun onError(errorCode: Int, errorMsg: String?, expends: String?) {
                DebugUtil.w(TAG, "getTranslationConfig onError: errorCode=$errorCode, errorMsg=$errorMsg, expends=$expends")
                asrListener?.onTranslationCfgError(channelId, errorCode, errorMsg, isInnerInInvoke)
            }

            override fun onSuccess(jsonString: String?) {
                DebugUtil.i(TAG, "getTranslationConfig onSuccess: jsonString=$jsonString")
                asrDataParser?.onTranslationCfgSuccess(channelId, jsonString, asrListener, isInnerInInvoke)
            }
        })
    }


    /**
     * 单独对外暴露获取ASR支持语种，不走RecorderService那条链路
     *
     * 1. 初始化
     * 2. 获取支持语种
     * 3. 释放
     */
    fun getSupportLanguage(callback: (Map<String, String>?) -> Unit) {

        fun realGetSupportLanguage() {
            DebugUtil.w(TAG, "realGetSupportLanguage")
            val translationRequest = TranslationRequest(
                language = Locale.CHINESE.language,
                duId = deviceDId,
                extends = mapOf()
            )

            asrClient?.getTranslationConfig(translationRequest, object : RequestCallback {
                override fun onError(errorCode: Int, errorMsg: String?, expends: String?) {
                    DebugUtil.w(TAG, "realGetSupportLanguage onError: errorCode=$errorCode, errorMsg=$errorMsg, expends=$expends")
                    callback.invoke(null)
                    release()
                }

                override fun onSuccess(jsonString: String?) {
                    DebugUtil.i(TAG, "realGetSupportLanguage onSuccess: jsonString=$jsonString")
                    val parseData = asrDataParser?.extractSupportLanguage(jsonString) ?: emptyMap()
                    callback.invoke(parseData)
                    release()
                }
            })
        }

        val config = InitConfigData(
            modelType = asrType, // 云侧or端侧
            channelId = UUID.randomUUID().toString(), // 实时长连接channelID
            callId = "", //非实时callId
            appTag = APP_TAG,
            subAppTag = SUB_APP_TAG,
            businessType = Constants.AsrBusinessType.REALTIME,
        )
        asrClient?.initAsr(config, object : AsrCallback {
            override fun onAsr(audioAck: AsrAudioAck) {
                // do nothing
            }

            override fun onResult(result: AsrAckRsp?) {
                if (result?.code == RealTimeAsrParser.STATUS_INIT_SUCCESS) {
                    realGetSupportLanguage()
                } else {
                    callback.invoke(null)
                    release()
                }
            }

            override fun onError(errorCode: Int, errorMsg: String?, expends: String?) {
                callback.invoke(null)
                release()
            }
        })
    }
}