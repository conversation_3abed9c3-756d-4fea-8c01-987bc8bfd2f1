package com.soundrecorder.browsefile.search

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.view.animation.PathInterpolator
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.load.ViewStatus
import com.soundrecorder.browsefile.home.view.behavior.PrimaryTitleBehavior

class SearchAnim {

    companion object {
        private const val TAG = "SearchAnim"
        private const val ANM_ALPHA = "alpha"
        private const val TRANS_DURATION = 250
        private const val TITLE_FADE_DURATION = 60L
        private const val NUMBER_0_5F = 0.5f
        private const val NUMBER_0_3F = 0.3f
        private const val NUMBER_0_9F = 0.9f
    }

    private val mAlphaInterpolator = PathInterpolator(NUMBER_0_3F, 0f, NUMBER_0_9F, 1f)
    private var mSubTitleEnterAnimator: ValueAnimator? = null
    private var mSubTitleExitAnimator: ValueAnimator? = null
    private var mMainTitleAlphaEnterAnimator: ObjectAnimator? = null
    private var mMainTitleAlphaExitAnimator: ObjectAnimator? = null
    private var mSubTitleAlphaEnterAnimator: ObjectAnimator? = null
    private var mSubTitleAlphaExitAnimator: ObjectAnimator? = null
    private var mOnlyMaskEnterAnimator: ObjectAnimator? = null
    private var mOnlyMaskExitAnimator: ObjectAnimator? = null

    private var mSearchFragment: SearchFragment? = null

    constructor(searchFragment: SearchFragment) {
        mSearchFragment = searchFragment
        initAnimators()
    }

    private fun isDoTopMarginAnim(): Boolean {
        val cacheIsDOTopMarginAnimValue = (mSearchFragment?.mViewModel?.isDoTopMarginAnim ?: SearchViewModel.TOP_MARGIN_ANIM_INIT)
        if (cacheIsDOTopMarginAnimValue != SearchViewModel.TOP_MARGIN_ANIM_INIT) {
            return cacheIsDOTopMarginAnimValue == SearchViewModel.TOP_MARGIN_ANIM_DO
        }

        val primaryTitleBehavior: PrimaryTitleBehavior? = getBehavior()
        var isDoTopMarginAnim = false
        if (primaryTitleBehavior != null) {
            val diff = primaryTitleBehavior.getDiffWithSearch() * 1f
            val scrollDistance = primaryTitleBehavior.scrollDistance
            val topRatio = diff / scrollDistance
            isDoTopMarginAnim = topRatio < NUMBER_0_5F
        }
        mSearchFragment?.mViewModel?.isDoTopMarginAnim =
            if (isDoTopMarginAnim) SearchViewModel.TOP_MARGIN_ANIM_DO else SearchViewModel.TOP_MARGIN_ANIM_NOT
        return isDoTopMarginAnim
    }

    private fun getBehavior(): PrimaryTitleBehavior? {
        val layoutParams = mSearchFragment?.searchCacheHolder?.appBarLayout?.layoutParams as? CoordinatorLayout.LayoutParams
        return layoutParams?.behavior as? PrimaryTitleBehavior
    }

    private fun initiateSearchInAnimator(useMaxHeiht: Boolean = false) {
        val subTitleView = mSearchFragment?.searchCacheHolder?.subTitleView
        val mainTitleLayout = mSearchFragment?.searchCacheHolder?.mainTitleLayout
        val subTitleLayout = mSearchFragment?.searchCacheHolder?.subTitleLayout
        val rotateView = mSearchFragment?.searchCacheHolder?.rotateView
        val toolbar = mSearchFragment?.searchCacheHolder?.toolbar
        val toolbarHeight = toolbar?.measuredHeight ?: 0
        val searchBox = mSearchFragment?.searchCacheHolder?.searchBox
        val colorListView = mSearchFragment?.searchCacheHolder?.browseFileList

        val subHeight = if (useMaxHeiht) {
            mSearchFragment?.mViewModel?.isDoTopMarginAnim = SearchViewModel.TOP_MARGIN_ANIM_DO
            mSearchFragment?.searchCacheHolder?.subTitleViewMaxHeight ?: 0
        } else {
            subTitleView?.measuredHeight ?: 0
        }
        var searchBoxPaddingTopFrom = toolbarHeight
        mSubTitleEnterAnimator = ValueAnimator.ofInt(0, subHeight)
        mMainTitleAlphaEnterAnimator = createTitleEnterAlphaAnm(mainTitleLayout)
        mSubTitleAlphaEnterAnimator = createTitleEnterAlphaAnm(subTitleLayout)
        mOnlyMaskEnterAnimator = createTitleEnterAlphaAnm(rotateView)
        val isDoTopMarginAnim: Boolean = isDoTopMarginAnim()
        if (isDoTopMarginAnim) {
            searchBoxPaddingTopFrom += subHeight
        }
        DebugUtil.i(TAG, "initiateSearchInAnimator,isDoTopMarginAnim=$isDoTopMarginAnim,subHeight=$subHeight")
        searchBox?.updatePadding(top = searchBoxPaddingTopFrom)
        mSubTitleEnterAnimator?.addUpdateListener { animation ->
            val animatedValue = animation.animatedValue as Int
            if (isDoTopMarginAnim) {
                val layoutParams = subTitleView?.layoutParams as? MarginLayoutParams
                layoutParams?.topMargin = -animatedValue
                val listLayoutParams = colorListView?.layoutParams as? MarginLayoutParams
                listLayoutParams?.topMargin = -animatedValue
                val searchBoxPaddingTopTo = searchBoxPaddingTopFrom - animatedValue
                searchBox?.updatePadding(top = searchBoxPaddingTopTo)
                subTitleView?.requestLayout()
                colorListView?.requestLayout()
            }
            subTitleView?.alpha = 1 - animation.animatedFraction
        }
        mSubTitleEnterAnimator?.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
            }

            override fun onAnimationEnd(animation: Animator) {
                subTitleView?.visibility = View.GONE
            }
        })
        mSubTitleEnterAnimator?.duration = TRANS_DURATION.toLong()
    }

    private fun createTitleEnterAlphaAnm(view: View?): ObjectAnimator {
        return ObjectAnimator().apply {
            target = view
            setFloatValues(1f, 0f)
            setPropertyName(ANM_ALPHA)
            interpolator = mAlphaInterpolator
            duration = TITLE_FADE_DURATION
        }
    }

    private fun createTitleExitAlphaAnm(view: View?): ObjectAnimator {
        return ObjectAnimator().apply {
            target = view
            setFloatValues(0f, 1f)
            setPropertyName(ANM_ALPHA)
            duration = TITLE_FADE_DURATION
            startDelay = TITLE_FADE_DURATION
        }
    }


    private fun initiateSearchOutAnimator(useMaxHeiht: Boolean = false) {
        val subTitleView = mSearchFragment?.searchCacheHolder?.subTitleView
        val mainTitleLayout = mSearchFragment?.searchCacheHolder?.mainTitleLayout
        val subTitleLayout = mSearchFragment?.searchCacheHolder?.subTitleLayout
        val rotateView = mSearchFragment?.searchCacheHolder?.rotateView
        val colorListView = mSearchFragment?.searchCacheHolder?.browseFileList


        val height: Int = if (useMaxHeiht) {
            mSearchFragment?.mViewModel?.isDoTopMarginAnim = SearchViewModel.TOP_MARGIN_ANIM_DO
            mSearchFragment?.searchCacheHolder?.subTitleViewMaxHeight ?: 0
        } else {
            subTitleView?.measuredHeight ?: 0
        }
        val isDoTopMarginAnim: Boolean = isDoTopMarginAnim()
        mSubTitleExitAnimator = ValueAnimator.ofInt(height, 0)
        mSubTitleExitAnimator?.addUpdateListener(AnimatorUpdateListener { animation ->
            if ((getBehavior()?.getCanScroll() == false)) {
                /*这里的处理是为了，竖屏下进入搜索，然后旋转为横屏，再退出搜索。此时退出只需更新colorListView的topMargin
                不需要更新subTitleView。如果是横屏进入搜索，再退出搜索 isDoTopMarginAnim值为false，不会更新colorListView的topMargin*/
                if (isDoTopMarginAnim) {
                    updateViewMargin(-(animation.animatedValue as Int), colorListView)
                }
            } else {
                val animatedValue = animation.animatedValue as Int
                if (isDoTopMarginAnim) {
                    updateViewMargin(-(animation.animatedValue as Int), colorListView)
                    updateViewMargin(-(animation.animatedValue as Int), subTitleView)
                }
            }
            subTitleView?.alpha = animation.animatedFraction
        })
        mMainTitleAlphaExitAnimator = createTitleExitAlphaAnm(mainTitleLayout)
        mSubTitleAlphaExitAnimator = createTitleExitAlphaAnm(subTitleLayout)
        mOnlyMaskExitAnimator = createTitleExitAlphaAnm(rotateView)
        mSubTitleExitAnimator?.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                subTitleView?.visibility = View.VISIBLE
                if (useMaxHeiht) {
                    getBehavior()?.expandSubTitle()
                }
            }

            override fun onAnimationEnd(animation: Animator) {
                getBehavior()?.isSearch = false
                getBehavior()?.isDoTopMarginAnim = false
                mSearchFragment = null
            }
        })
        mSubTitleExitAnimator?.duration = TRANS_DURATION.toLong()
    }

    private fun updateViewMargin(animatedValue: Int, targetView: View?) {
        val layoutParams = targetView?.layoutParams as? MarginLayoutParams
        layoutParams?.topMargin = animatedValue
        targetView?.requestLayout()
    }

    fun animateSearchIn(useMaxHeiht: Boolean = false) {
        if (mSearchFragment?.searchCacheHolder == null || mSearchFragment?.searchCacheHolder?.mSearchAnimView == null) {
            DebugUtil.i(TAG, "animateSearchIn  mSearchCacheHolder is null.")
            return
        }

        val searchContainer = mSearchFragment?.mBinding?.backgroundMaskContainer
        searchContainer?.visibility = View.VISIBLE
        initAnimators(useMaxHeiht)
        getBehavior()?.isSearch = true
        val doTopAnm = isDoTopMarginAnim()
        getBehavior()?.isDoTopMarginAnim = doTopAnm
        mSubTitleEnterAnimator?.start()
        if (doTopAnm) {
            mMainTitleAlphaEnterAnimator?.start()
            mSubTitleAlphaEnterAnimator?.start()
        } else {
            mOnlyMaskEnterAnimator?.start()
        }
    }

    /**
     * @param runExitAnimator 退出搜索是否要执行动画
     */
    fun animateSearchOut(runExitAnimator: Boolean = true) {
        if (mSearchFragment?.mBinding?.include?.resultContainer == null) {
            DebugUtil.v(TAG, "mResultContainer is null")
            return
        }
        if (mSearchFragment?.searchCacheHolder == null) {
            DebugUtil.v(TAG, "mSearchCacheHolder is null")
            return
        }
        DebugUtil.i(TAG, "animateSearchOut,runExitAnimator= $runExitAnimator ")
        val resultContainer = mSearchFragment?.mBinding?.include?.resultContainer
        if (resultContainer?.visibility == View.VISIBLE) {
            resultContainer.visibility = View.GONE
        }
        val status =  mSearchFragment?.browseViewModel?.browseModel?.liveViewStatus?.value
        DebugUtil.d(TAG, "$status " + (ViewStatus.EMPTY == status))
        mSearchFragment?.searchCacheHolder?.browseFileList?.visibility =
            if (status != ViewStatus.SHOW_CONTENT) View.GONE else View.VISIBLE
        val doTopAnm = isDoTopMarginAnim()
        if (runExitAnimator) {
            mSubTitleExitAnimator?.start()
            if (doTopAnm) {
                mMainTitleAlphaExitAnimator?.start()
                mSubTitleAlphaExitAnimator?.start()
            } else {
                mOnlyMaskExitAnimator?.start()
            }
        } else {
            // 不执行动画，那就直接执行动画结果
            if (doTopAnm) {
                updateViewMargin(0, mSearchFragment?.searchCacheHolder?.browseFileList)
                if (getBehavior()?.getCanScroll() != false) {
                    updateViewMargin(0, mSearchFragment?.searchCacheHolder?.subTitleView)
                }
                mSearchFragment?.searchCacheHolder?.subTitleLayout?.alpha = 1f
                mSearchFragment?.searchCacheHolder?.mainTitleLayout?.alpha = 1f
            } else {
                mSearchFragment?.searchCacheHolder?.rotateView?.alpha = 1f
            }
            mSearchFragment?.searchCacheHolder?.subTitleView?.let {
                it.isVisible = true
                it.alpha = 1f
            }
            if (mSearchFragment?.mViewModel?.browseViewModel?.showSearchUseMaxHeight == true) {
                getBehavior()?.expandSubTitle()
            }
            getBehavior()?.isSearch = false
            getBehavior()?.isDoTopMarginAnim = false
            mSearchFragment = null
        }
    }

    fun isSubTitleInAnimation(): Boolean {
        return mSubTitleEnterAnimator?.isRunning == true || mSubTitleExitAnimator?.isRunning == true
    }

    private fun initAnimators(useMaxHeiht: Boolean = false) {
        DebugUtil.d(TAG, "----------initAnimators------------")
        initiateSearchInAnimator(useMaxHeiht)
        initiateSearchOutAnimator(useMaxHeiht)
    }
}