/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MakeFileUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2019/9/27
 * * Author      : LI Kun
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils;

import java.io.File;
import java.io.IOException;

public class MakeFileUtils {

    private static final String TAG = "MakeFileUtils";

    public static boolean delAllFile(String path) {
        boolean flag = false;
        File file = new File(path);
        if (!file.exists()) {
            return flag;
        } else if (!file.isDirectory()) {
            return flag;
        } else {
            String[] tempList = file.list();
            if (tempList != null) {
                File temp = null;
                for (int i = 0; i < tempList.length; ++i) {
                    if (path.endsWith(File.separator)) {
                        temp = new File(path + tempList[i]);
                    } else {
                        temp = new File(path + File.separator + tempList[i]);
                    }

                    if (temp.isFile()) {
                        temp.delete();
                    }

                    if (temp.isDirectory()) {
                        delAllFile(path + File.separator + tempList[i]);
                        File folder = new File(path + File.separator + tempList[i]);
                        folder.delete();
                        flag = true;
                    }
                }
            }

            return flag;
        }
    }

    public static void delFolder(String folderPath) {
        if (folderPath != null) {
            delAllFile(folderPath);
            File file = new File(folderPath);
            file.delete();
        }
    }

    public static File makeFile(String path, String fileDisplayName) {
        File folder = new File(path);
        folder.mkdirs();
        File file = new File(path + File.separator + fileDisplayName);
        try {
            file.createNewFile();
        } catch (IOException e) {
            DebugUtil.w(TAG, "makeFile error: " + e, false);
        }
        return file;
    }
}
