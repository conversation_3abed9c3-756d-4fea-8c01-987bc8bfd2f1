/***********************************************************
 ** Copyright (C), 2010-2024, Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: - NoteSearchRepository.kt
 ** Description: Annotation method that needs to be mapped.
 ** Version: 1.0
 ** Date : 2024/03/21
 ** Author: 80351967
 **
 ** ---------------------Revision History: ---------------------
 ** <author>    <data>    <version >    <desc>
 ** xxx@TAG    2024/03/21    1.0    build this file
 ****************************************************************/
package com.soundrecorder.browsefile.search.load

import android.net.Uri
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.db.NoteDbUtils.queryAllNotes
import org.json.JSONObject

object NoteSearchRepository {
    private const val SUMMARY_TEXT_MAX_LENGTH = 10
    private val SUMMARY_SEARCH_RESULT_CHAR_LENGTH_AFTER_KEYWORD =
        BaseApplication.getAppContext().resources.getInteger(
            R.integer.search_result_text_length_after_keywords
        )
    private const val TAG = "NoteSearchRepository"
    private const val NOTE_QUERY_URI =
        "content://com.nearme.note/query_notes_no_limit?local_id_list="
    private const val KEY_WORD = "&keyword="
    private const val CALL_UUID = "speech_log_id"
    private const val NOTE_ID = "local_id"
    private const val EXTRA_INFO = "extra_info"
    private const val TEXT = "text"
    private const val AUDIO_INFO = "audio_info"
    private const val MEDIA_ID = "mediaId"
    private const val ELLIPSIS = "..."
    private const val ELLIPSIS_LENGTH = 3
    fun query(
        searchValues: MutableMap<String, String>?,
    ): SearchResultWrapper {
        return if (!FeatureOption.supportRecordSummaryFunction() || searchValues.isNullOrEmpty()) {
            SearchResultWrapper()
        } else {
            val noteQueryResult = SearchResultWrapper()
            val res = searchValues["searchWord"]?.let { trySearchValueFind(it) }
            res?.let {
                noteQueryResult.totalCount = it.size
                it.forEach { item ->
                    noteQueryResult.pageData.add(item)
                }
            }
            noteQueryResult
        }
    }

    /**
     * 便签是否存在
     */
    fun isNoteExist(noteId: String): Boolean {
        val noteIdList = arrayListOf<String>()
        noteIdList.add(noteId)
        val uri = Uri.parse("$NOTE_QUERY_URI${Uri.encode(noteIdList.toString())}")
        // 查询未删除的
        val selection = StringBuilder()
            .append("recycle_time=0")
            .toString()
        val query =
            BaseApplication.getAppContext().contentResolver.query(uri, null, selection, null, null)
        query?.use {
            return it.count > 0
        }
        return false
    }

    private fun findKeywordSubstring(text: String, keyword: String): Pair<String, List<Int>> {
        val index = text.indexOf(keyword)
        if (index < 0) {
            return Pair("", listOf())
        }

        var start = index - SUMMARY_TEXT_MAX_LENGTH
        var end = index + SUMMARY_SEARCH_RESULT_CHAR_LENGTH_AFTER_KEYWORD
        // check boundary
        val startFlag = start > 0
        if (start < 0) {
            start = 0
        }
        if (end > text.length) {
            end = text.length
        }

        val substring = if (startFlag) {
            ELLIPSIS + text.substring(start + ELLIPSIS_LENGTH, end)
        } else {
            text.substring(start, end)
        }
        var keywordStartIndex = substring.indexOf(keyword)
        val keywordIndices = mutableListOf<Int>()

        while (keywordStartIndex >= 0) {
            keywordIndices.add(keywordStartIndex)
            keywordStartIndex = substring.indexOf(keyword, keywordStartIndex + keyword.length)
        }

        return Pair(substring, keywordIndices)
    }

    @Suppress("LongMethod")
    private fun trySearchValueFind(searchValue: String): MutableList<ItemSearchViewModel>? {
        val localSummaryList = queryAllNotes()
        if (localSummaryList.isEmpty()) {
            DebugUtil.i(TAG, "trySearchValueFind localSummaryList is empty")
            return null
        }
        val itemSearchViewModelList: MutableList<ItemSearchViewModel> = mutableListOf()
        val listValue = arrayListOf<String>()
        localSummaryList.forEach {
            it.noteId?.let { it1 -> listValue.add(it1) }
        }
        val uri = Uri.parse(
            "$NOTE_QUERY_URI${Uri.encode(listValue.toString())}$KEY_WORD${
                Uri.encode(searchValue)
            }"
        )
        DebugUtil.d(TAG, "trySearchValueFind:list.size ${listValue.size},searchValue=$searchValue")
        runCatching {
            val query =
                BaseApplication.getAppContext().contentResolver.query(uri, null, null, null, null)
            query?.use {
                DebugUtil.d(TAG, "trySearchValueFind result.count=${it.count}")
                if (it.count > 0 && it.moveToFirst()) {
                    var item: ItemSearchViewModel?
                    val mediaIndex: Int = it.getColumnIndex(EXTRA_INFO)
                    val summaryIndex: Int = it.getColumnIndex(TEXT)
                    val noteIdIndex: Int = it.getColumnIndex(NOTE_ID)
                    val callIdIndex: Int = it.getColumnIndex(CALL_UUID)
                    if (mediaIndex < 0 || summaryIndex < 0) return@use
                    do {
                        item = ItemSearchViewModel()
                        val extraJsonStr = it.getString(mediaIndex)
                        if (extraJsonStr.isNullOrEmpty()) {
                            DebugUtil.d(TAG, "extraJson is null")
                            continue
                        }
                        val noteId = it.getString(noteIdIndex)
                        val callId = it.getString(callIdIndex)
                        val extraJson = JSONObject(extraJsonStr)
                        DebugUtil.d(TAG, "extraJson:$extraJson")
                        val audioInfo = extraJson.optJSONObject(AUDIO_INFO)
                        val mediaID = audioInfo?.optString(MEDIA_ID)?.toLongOrNull()
                        val record = mediaID?.let { id ->
                            MediaDBUtils.queryRecordById(id)
                        } ?: (NoteDbUtils.queryNoteByNoteId(noteId)?.mediaId?.toLongOrNull()
                            ?.let { id ->
                                if (mediaID != id) {
                                    MediaDBUtils.queryRecordById(id)
                                } else null
                            }) ?: continue
                        item.dateModified = record.dateModied
                        item.mDuration = record.duration
                        item.title = record.displayName.title()
                        item.mediaId = record.id
                        item.noteId = noteId
                        item.recordUUID = callId
                        // 去掉便签返回内容中的换行符，替换为空格，否则搜索结果会显示异常
                        item.summaryText = it.getString(summaryIndex).replace("\n", " ")
                        val res = item.summaryText?.let { summaryText ->
                            findKeywordSubstring(summaryText, searchValue)
                        } as Pair
                        if (res.first.isEmpty() || res.second.isEmpty()) {
                            continue
                        }
                        item.summaryText = res.first
                        item.summaryColorIndex = ArrayList()
                        searchValue.let { searchWord ->
                            res.second.forEach { index ->
                                item.summaryColorIndex.add(index)
                                item.summaryColorIndex.add(index + searchWord.length)
                            }
                        }
                        itemSearchViewModelList.add(item)
                    } while (it.moveToNext())
                }
            }
        }.onFailure {
            DebugUtil.e(TAG, "trySearchValueFind error ${it.message}")
        }
        return itemSearchViewModelList
    }
}