/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.privacyPolicy

import android.content.Context
import androidx.fragment.app.Fragment
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object PrivacyPolicyAction {
    const val COMPONENT_NAME = "PrivacyPolicy"

    const val NEW_PRIVACY_POLICY_FRAGMENT = "newPrivacyPolicyFragment"
    const val NEW_PRIVACY_POLICY_INFO_FRAGMENT = "newPrivacyPolicyInfoFragment"
    const val NEW_PRIVACY_POLICY_DELEGATE = "newPrivacyPolicyDelegate"
    const val NEW_FUNCTION_GUIDE_DELEGATE = "newFunctionGuideDelegate"

    const val NEW_COLLECTION_INFO_FRAGMENT = "newCollectionInfoFragment"
    const val NEW_COLLECTION_INFO_CONTENT_FRAGMENT = "newCollectionInfoContentFragment"

    const val NEW_FUNCTION_PRIVACY_DELEGATE = "newFunctionPrivacyDelegate"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun newPrivacyPolicyFragment(): Fragment? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_PRIVACY_POLICY_FRAGMENT).build()
            OStitch.execute<Fragment>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun newPrivacyPolicyInfoFragment(type: Int): Fragment? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_PRIVACY_POLICY_INFO_FRAGMENT)
                .param(type).build()
            OStitch.execute<Fragment>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun newCollectionInfoFragment(type: Int): Fragment? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_COLLECTION_INFO_FRAGMENT)
                .param(type).build()
            OStitch.execute<Fragment>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun newCollectionInfoContentFragment(title: String?, type: Int, collectionType: Int): Fragment? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_COLLECTION_INFO_CONTENT_FRAGMENT)
                .param(title, type, collectionType)
                .build()
            OStitch.execute<Fragment>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun newPrivacyPolicyDelegate(
        context: Context,
        type: Int = IPrivacyPolicyDelegate.POLICY_TYPE_COMMON,
        resultListener: IPrivacyPolicyResultListener?
    ): IPrivacyPolicyDelegate? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_PRIVACY_POLICY_DELEGATE)
                .param(context, type, resultListener)
                .build()
            OStitch.execute<IPrivacyPolicyDelegate>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun newFunctionGuideDelegate(
        context: Context,
        functionClickOk: ((fromUserNotice: Boolean) -> Unit)?
    ): IFunctionGuideDelegate? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_FUNCTION_GUIDE_DELEGATE)
                .param(context, functionClickOk)
                .build()
            OStitch.execute<IFunctionGuideDelegate>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun newFunctionPrivacyDelegate(funcType: Int): IFunctionPrivacyDelegate? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_FUNCTION_PRIVACY_DELEGATE).param(funcType).build()
            OStitch.execute<IFunctionPrivacyDelegate>(apiRequest).result
        } else null
    }
}