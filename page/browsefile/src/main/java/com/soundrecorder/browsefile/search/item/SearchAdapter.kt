/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseAdapter.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.search.item

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.LifecycleOwner
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import com.soundrecorder.base.view.IRecyclerAdapterData
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel

class SearchAdapter(private val mContext: Context?, private val mOwner: LifecycleOwner, private val mBrowseViewListener: IBrowseViewHolderListener) :
    PagingDataAdapter<ItemSearchViewModel, ItemSearchViewHolder>(SEARCH_DIFF_CALLBACK),
    IRecyclerAdapterData {
    companion object {

        private const val TAG = "SearchAdapter"

        val SEARCH_DIFF_CALLBACK = object : DiffUtil.ItemCallback<ItemSearchViewModel>() {
            override fun areItemsTheSame(
                p0: ItemSearchViewModel,
                p1: ItemSearchViewModel
            ): Boolean {
                return p0.mediaId == p1.mediaId
            }

            @SuppressLint("DiffUtilEquals")
            override fun areContentsTheSame(
                p0: ItemSearchViewModel,
                p1: ItemSearchViewModel
            ): Boolean {
                return p0 == p1
            }
        }
    }

    private val mLayoutInflater = LayoutInflater.from(mContext)

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): ItemSearchViewHolder {
        return ItemSearchViewHolder(
            DataBindingUtil.inflate(
                mLayoutInflater,
                R.layout.item_search,
                parent,
                false
            ), mBrowseViewListener
        ).also {
            it.adapterData = this
        }
    }

    override fun onBindViewHolder(holder: ItemSearchViewHolder, position: Int) {
        val taskId = (holder.itemView.context as Activity).taskId
        getItem(position)?.let {
            it.taskId = taskId
            holder.onBindViewHolder(it, mOwner)
        }
    }

    override fun onViewRecycled(holder: ItemSearchViewHolder) {
        holder.onViewRecycled()
        super.onViewRecycled(holder)
    }
}