/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SearchAnimTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.animation.ValueAnimator
import android.content.Context
import android.os.Build
import android.widget.TextView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOS12FeatureUtil::class])
class TransferAnimationTextViewTest {
    private var context: Context? = null
    private var textView: TransferAnimationTextView? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        textView = TransferAnimationTextView(context!!)
    }

    @After
    fun tearDown() {
        context = null
        textView = null
    }

    @Test
    fun check_transferAnimationTextView_when_init() {
        val textView = textView ?: return
        val mAnimView = Whitebox.getInternalState<EffectiveAnimationView>(textView, "mAnimView")
        Assert.assertNotNull(mAnimView)
        val mTextView = Whitebox.getInternalState<TextView>(textView, "mTextView")
        Assert.assertNotNull(mTextView)
        textView.setMaxWidth(1)
        Assert.assertEquals(1, mTextView.maxWidth)
        val animator = Whitebox.invokeMethod<ValueAnimator?>(textView, "genValueAnimator", 1, 1, 1, 1)
        Assert.assertNull(animator)

        val duration = Whitebox.invokeMethod<Long>(textView, "getAnimationDuration", 0, 0, 0)
        Assert.assertEquals(0, duration)

        Whitebox.invokeMethod<Any>(textView, "onDetachedFromWindow")
        val mStartConvertAnimator = Whitebox.getInternalState<ValueAnimator>(textView, "mStartConvertAnimator")
        Assert.assertNull(mStartConvertAnimator)
        val mEndConvertAnimator = Whitebox.getInternalState<ValueAnimator>(textView, "mEndConvertAnimator")
        Assert.assertNull(mEndConvertAnimator)

        val text = textView.getText()
        Assert.assertEquals("", text)
    }
}