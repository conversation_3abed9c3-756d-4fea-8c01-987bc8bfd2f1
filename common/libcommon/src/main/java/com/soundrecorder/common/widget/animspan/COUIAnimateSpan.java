/*********************************************************************
 ** Copyright (C), 2024-2034 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : COUIAnimateTextView
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/17 14:46
 ** Author      : 80342011
 ** OPLUS Java File Skip Rule:IllegalCatch,ParameterNumber,LineLength
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 ** 80262777        2024/7/17       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.widget.animspan;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.SystemClock;
import android.text.style.ReplacementSpan;
import com.coui.appcompat.animation.COUIInEaseInterpolator;

public class COUIAnimateSpan extends ReplacementSpan {

    public static final int TYPE_SEQUENCE = 1;
    public static final int TYPE_GRADIENT = 2;
    public static final int TYPE_OFFSETY = 4;
    public static final double D_ONE = 1.0;
    public static final float FLOAT_HALF = 0.5F;
    public static final float COLOR_MAX = 255.0F;
    public static final double FLOAT_COLOR_LINEAR = 2.2;

    private static final COUIInEaseInterpolator INTERPOLATOR = new COUIInEaseInterpolator();
    private static final int UNSET = -1;

    private final float mTextSize;
    private final int mStableColor;
    private final int mStartColor;
    private final int mEndColor;

    protected int mAnimateType;
    protected long mStartTime = UNSET;

    private long mDuration;
    private long mDelay;
    private float mOffset;
    private Runnable mEndRunnable;
    private UpdateRunnable mUpdateRunnable;

    private float mFraction = 0;

    public COUIAnimateSpan(COUIAnimateSpanParam param) {
        mDuration = param.duration;
        mDelay = param.delay;
        mTextSize = param.textSize;
        mOffset = param.offset;
        mStartColor = param.startColor;
        mEndColor = param.endColor;
        mStableColor = param.stableColor;
        mEndRunnable = param.runEndRunnable;
        mUpdateRunnable = param.updateRunnable;
    }

    public void resetAnimateParams(long duration, long delay, float offset) {
        mDuration = duration;
        mDelay = delay;
        mOffset = offset;
    }

    @Override
    public int getSize(Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
        if (fm != null) {
            paint.getFontMetricsInt(fm);
        }
        return (int) paint.measureText(text.subSequence(start, end).toString());
    }

    @Override
    public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
        if (mStartTime != UNSET) {
            long deltaTime = SystemClock.uptimeMillis() - mStartTime - mDelay;
            if (deltaTime <= 0) {
                mFraction = 0;
            } else if (deltaTime >= mDuration) {
                mFraction = 1;
                mStartTime = UNSET;
                mEndRunnable.run();
            } else {
                mFraction = getInterpolation((float) (deltaTime * D_ONE / mDuration));
            }
            if (mDelay != 0 && mFraction == 0 && ((mAnimateType & TYPE_SEQUENCE) != 0)) {
                return;
            }
        }
        if (mUpdateRunnable != null) {
            mUpdateRunnable.run(mFraction);
        }
        paint.setTextSize(mTextSize);
        paint.setColor((mFraction == 1 || ((mAnimateType & TYPE_GRADIENT) == 0))
                ? mStableColor
                : getGradientColor(mFraction));
        paint.setAlpha((int) (mFraction * Color.alpha(mStableColor)));
        canvas.drawText(text.subSequence(start, end).toString(),
                x,
                ((mAnimateType & TYPE_OFFSETY) != 0) ? (y + (1 - mFraction) * mOffset) : y,
                paint);
    }

    public void playAnimation(int animateType, long startTime) {
        mAnimateType = animateType;
        mStartTime = startTime;
    }

    public void resumeAnimation(long startTime) {
        mStartTime = startTime;
    }

    public void pauseAnimation() {
        mStartTime = UNSET;
    }

    public void cancelAnimation() {
        mStartTime = UNSET;
        mFraction = 1;
    }

    private float getInterpolation(float fraction) {
        if (fraction <= 0 || fraction >= 1) {
            return 1;
        }
        return INTERPOLATOR.getInterpolation(fraction);
    }

    private int getGradientColor(float fraction) {
        if (fraction <= 0 || fraction >= 1) {
            return mStableColor;
        }
        if (fraction <= FLOAT_HALF) {
            return (int) evaluate(mFraction * 2f, mStartColor, mEndColor);
        } else {
            return (int) evaluate((mFraction - FLOAT_HALF) * 2f, mEndColor, mStableColor);
        }
    }


    public Object evaluate(float fraction, Object startValue, Object endValue) {
        int startInt = (Integer) startValue;
        float startA = ((startInt >> 24) & 0xff) / COLOR_MAX;
        float startR = ((startInt >> 16) & 0xff) / COLOR_MAX;
        float startG = ((startInt >> 8) & 0xff) / COLOR_MAX;
        float startB = (startInt & 0xff) / COLOR_MAX;

        int endInt = (Integer) endValue;
        float endA = ((endInt >> 24) & 0xff) / COLOR_MAX;
        float endR = ((endInt >> 16) & 0xff) / COLOR_MAX;
        float endG = ((endInt >> 8) & 0xff) / COLOR_MAX;
        float endB = (endInt & 0xff) / COLOR_MAX;

        // convert from sRGB to linear
        startR = (float) Math.pow(startR, FLOAT_COLOR_LINEAR);
        startG = (float) Math.pow(startG, FLOAT_COLOR_LINEAR);
        startB = (float) Math.pow(startB, FLOAT_COLOR_LINEAR);

        endR = (float) Math.pow(endR, FLOAT_COLOR_LINEAR);
        endG = (float) Math.pow(endG, FLOAT_COLOR_LINEAR);
        endB = (float) Math.pow(endB, FLOAT_COLOR_LINEAR);

        // compute the interpolated color in linear space
        float a = startA + fraction * (endA - startA);
        float r = startR + fraction * (endR - startR);
        float g = startG + fraction * (endG - startG);
        float b = startB + fraction * (endB - startB);

        // convert back to sRGB in the [0..255] range
        a = a * COLOR_MAX;
        r = (float) Math.pow(r, D_ONE / FLOAT_COLOR_LINEAR) * COLOR_MAX;
        g = (float) Math.pow(g, D_ONE / FLOAT_COLOR_LINEAR) * COLOR_MAX;
        b = (float) Math.pow(b, D_ONE / FLOAT_COLOR_LINEAR) * COLOR_MAX;

        return Math.round(a) << 24 | Math.round(r) << 16 | Math.round(g) << 8 | Math.round(b);
    }
}