/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BrowseModelTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/26 1.0 create
 */

package com.soundrecorder.browsefile.home.load

import android.database.Cursor
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.browsefile.shadows.ShadowOplusUsbEnvironment
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.db.GroupInfoDbUtil
import kotlinx.coroutines.Job
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusUsbEnvironment::class]
)
class BrowseModelTest {

    @Test
    fun should_correct_when_refresh() {
        val model = BrowseModel()
        Assert.assertFalse(Whitebox.getInternalState(model, "mQuerying"))
        model.refresh(GroupInfoDbUtil.genDefaultAllGroupInfo(), false, null, null)
    }

    @Test
    fun should_correct_when_register() {
        val model = BrowseModel()
        Assert.assertFalse(Whitebox.getInternalState(model, "mQuerying"))
        model.register(object : OnDataReadyCompletedListener<ItemBrowseRecordViewModel, GroupInfo> {
            override fun onDataReadyCompleted(
                model: AbsModel<ItemBrowseRecordViewModel, GroupInfo>,
                data: List<ItemBrowseRecordViewModel>,
                args: GroupInfo,
                callerName: String?,
                job: Job?
            ) {

            }
        })
        Assert.assertNotNull(Whitebox.getInternalState(model, "mRefListener"))
    }

    @Test
    fun should_correct_when_onRecordersFetched() {
        val model = BrowseModel()
        Assert.assertNull(model.browseListCount)

        model.onRecordersFetched(BrowseListCount(), GroupInfoDbUtil.genDefaultAllGroupInfo(), false, null, null)
        Assert.assertNotNull(model.browseListCount)
        Assert.assertEquals(ViewStatus.EMPTY, model.liveViewStatus.value)

        val mockCursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(mockCursor.getColumnIndexOrThrow(anyString())).thenReturn(0)
        Mockito.`when`(mockCursor.getString(anyInt())).thenReturn("录音")
        Mockito.`when`(mockCursor.getLong(anyInt())).thenReturn(1)
        Mockito.`when`(mockCursor.moveToFirst()).thenReturn(true)
        Mockito.`when`(mockCursor.moveToNext()).thenReturn(true, false)

        model.onRecordersFetched(BrowseListCount().apply {
            cursor = mockCursor
        }, GroupInfoDbUtil.genDefaultAllGroupInfo(), true, null, null)

        Assert.assertEquals(ViewStatus.SHOW_CONTENT, model.liveViewStatus.value)
    }
}