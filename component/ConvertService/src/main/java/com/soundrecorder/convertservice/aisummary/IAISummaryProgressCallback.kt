/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryProgressCallback
 * Description:提供给TaskManager的回调，用于Task队列的管理
 * Version: 1.0
 * Date: 2025/5/13
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/13      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

interface IAISummaryProgressCallback {

    fun preStartAISummary(mediaId: Long)

    fun postAISummary(mediaId: Long)

    fun postCancelAISummary(mediaId: Long)
}