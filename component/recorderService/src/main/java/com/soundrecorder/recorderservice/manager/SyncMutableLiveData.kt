/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SyncMutableLiveData
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/4/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.recorderservice.manager

import androidx.lifecycle.MutableLiveData

class SyncMutableLiveData<T> : MutableLiveData<T>() {

    var syncValue: T? = null
        private set

    override fun postValue(value: T) {
        syncValue = value
        super.postValue(value)
    }

    override fun setValue(value: T) {
        syncValue = value
        super.setValue(value)
    }

    fun getValueSync(): T? {
        return syncValue
    }
}