/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordFileObserver
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.os.FileObserver
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.recorderservice.R

class RecordFileObserver {

    companion object {
        const val TAG = "RecordFileObserver"
    }

    private var mFileObserver: FileObserver? = null

    var mBlockDelete: Boolean = false

    fun startWatchingRecordingsFile(recordFilePath: String?) {
        if (mFileObserver == null && !TextUtils.isEmpty(recordFilePath)) {
            DebugUtil.v(TAG, "start do FileObserver startWatching getRecordFileName():" + FileUtils.getDisplayNameByPath(recordFilePath))
            mFileObserver = object : FileObserver(recordFilePath) {
                override fun onEvent(event: Int, path: String?) {
                    if (event == ATTRIB
                        || event == DELETE || event == MOVED_FROM
                        || event == MOVE_SELF
                    ) {
                        val context = BaseApplication.getAppContext()
                        DebugUtil.d(TAG, "FileObserver onEvent\t event=$event path:$path mIsDeleteBySelf= $mBlockDelete")
                        if (mBlockDelete) {
                            return
                        }

                        ToastManager.showShortToast(context, com.soundrecorder.common.R.string.file_change_or_del)
                        RecordStopExceptionProcessor.dispatchStopEvent(RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDINGFILE_DELETE)
                        //RecorderUtil.sendLocalBroadcast(context, Intent(RecorderService.ACTION_RECORDER_STOP_RECORDER_ABNORMAL))
                        //todo 需要删除本地的db和媒体库的db，同时停止录制。

                        //<EMAIL>()
                    }
                }
            }
        }
        mFileObserver?.startWatching()
    }


    fun stopWatchingRecordingsFile() {
        if (mFileObserver != null) {
            DebugUtil.i(TAG, "stopWatchingRecordingsFile")
            mFileObserver?.stopWatching()
            mFileObserver = null
        }
    }
}