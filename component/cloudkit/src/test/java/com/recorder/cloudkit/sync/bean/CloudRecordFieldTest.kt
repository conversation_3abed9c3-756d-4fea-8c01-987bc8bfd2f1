package com.recorder.cloudkit.sync.bean

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.BaseUtil.getPhoneStorageDir
import com.soundrecorder.base.utils.BaseUtil.isAndroidQOrLater
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.soundrecorder.common.databean.Record
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.MockedStatic.Verification
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CloudRecordFieldTest {
    private var mContext: Context? = null
    private var mMockApplication: MockedStatic<BaseApplication>? = null
    private var mMockStaticBaseUtil: MockedStatic<BaseUtil>? = null


    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
        mMockStaticBaseUtil = Mockito.mockStatic(BaseUtil::class.java)
        mMockStaticBaseUtil?.`when`<Any>(Verification { isAndroidQOrLater })?.thenReturn(true)
        mMockStaticBaseUtil?.`when`<Any>(Verification { getPhoneStorageDir(mContext) })?.thenReturn("0")
    }

    @After
    fun release() {
        mMockApplication?.close()
        mMockApplication = null
        mMockStaticBaseUtil?.close()
        mMockStaticBaseUtil = null
        mContext = null
    }

    @Test
    fun should_success_when_toRecord() {
        val cloudRecordField = getCloudRecordField()
        val record = cloudRecordField.toRecord()

        Assert.assertNotNull(record.uuid)
        Assert.assertTrue(cloudRecordField.itemId == record.uuid)
        Assert.assertNotNull(record.displayName)
        Assert.assertNotNull(record.mD5)
        Assert.assertNotNull(record.data)
        Assert.assertNotNull(record.mimeType)
    }

    @Test
    fun should_success_when_toMetaData() {
        val record = getRecordData()
        val cloudRecordField = CloudRecordField.toMetaData(record)

        Assert.assertTrue(cloudRecordField.itemId == record.uuid)
        Assert.assertTrue(cloudRecordField.fileId == record.fileId)
        Assert.assertTrue(cloudRecordField.fileMD5 == record.mD5)
        Assert.assertTrue(cloudRecordField.relativePath != record.relativePath)
        Assert.assertTrue(cloudRecordField.relativePath?.endsWith(record.displayName) == true)
    }


    private fun getRecordData() = Record().apply {
        uuid = "2443351c-83e0-49ae-a782-d23f1bf54928"
        duration = 4902
        fileId = "oc202205010161597507_2086856095"
        mMD5 = "202d77f57a1c4bade191615529a1e0a7"
        fileSize = 196073
        mimeType = "audio/mpeg"
        mDateCreated = 1651373185
        dateModied = 1651373186
        recordType = 0
        relativePath = "/Music/Recordings/Standard Recordings"
        displayName = "标1.mp3"
        data = "0/Music/Recordings/Standard Recordings/标1.mp3"
    }

    private fun getCloudRecordField() = CloudRecordField().apply {
        itemId = "2443351c-83e0-49ae-a782-d23f1bf54928"
        duration = 4902
        fileId = "oc202205010161597507_2086856095"
        fileMD5 = "202d77f57a1c4bade191615529a1e0a7"
        fileSize = 196073
        mimeType = "audio/mpeg"
        recordTime = 1651373185
        recordUpdateTime = 1651373186
        recordType = "0"
        relativePath = "Recordings/Standard Recordings/标1.mp3"
    }
}