/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrowseFileApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.api

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.browsefile.home.view.group.util.GroupViewModel
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.browsefile.parentchild.BrowsePanelController
import com.soundrecorder.browsefile.search.load.NoteSearchRepository
import com.soundrecorder.browsefile.search.load.center.filechange.CenterFileChangeObserver
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.player.speaker.SpeakerModeController

@Component(BrowseFileAction.COMPONENT_NAME)
object BrowseFileApi {

    @Action(BrowseFileAction.CREATE_BROWSE_FILE_INTENT)
    @JvmStatic
    fun createBrowseFileIntent(context: Context): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClass(context, BrowseFile::class.java)
        return jumpIntent
    }

    @Action(BrowseFileAction.IS_BROWSE_FILE)
    @JvmStatic
    fun isBrowseFile(context: Context): Boolean = context is BrowseFile

    @Action(BrowseFileAction.GET_BROWSE_FILE_CLASS)
    @JvmStatic
    fun getBrowseFileClass(): Class<*> {
        return BrowseFile::class.java
    }

    @Action(BrowseFileAction.ON_FILE_UPDATE_SUCCESS)
    @JvmStatic
    fun onFileUpdateSuccess(rowId: Long, newPath: String?) {
        CenterFileChangeObserver.fileUpdateChangeSuccess(rowId, newPath)
    }

    @Action(BrowseFileAction.HAS_PLAYBACK_RECORD)
    @JvmStatic
    fun hasPlayBackRecord(activity: Activity?): Boolean {
        return (activity as? BrowseFile)?.hasPlayBackRecord() ?: false
    }

    @Action(BrowseFileAction.GET_BROWSE_ACTIVITY_VIEWMODEL)
    @JvmStatic
    fun getBrowseActivityViewModel(activity: AppCompatActivity?): ViewModel? {
        return activity?.let { ViewModelProvider(it)[BrowseFileActivityViewModel::class.java] }
    }

    @Action(BrowseFileAction.GET_VIEWMODEL_SPEAKER_MODE_CONTROLLER)
    @JvmStatic
    fun getViewModelSpeakerModeController(viewModel: ViewModel?): SpeakerModeController? {
        return (viewModel as? BrowseFileActivityViewModel)?.mSpeakerModeController
    }

    @Action(BrowseFileAction.SET_VIEWMODEL_WINDOWTYPE)
    @JvmStatic
    fun setViewModelWindowType(viewModel: ViewModel?, config: Configuration?) {
        (viewModel as? BrowseFileActivityViewModel)?.windowType?.value = ScreenUtil.getWindowType(configuration = config)
    }

    @Action(BrowseFileAction.GET_VIEWMODEL_WINDOWTYPE)
    @JvmStatic
    fun getViewModelWindowType(viewModel: ViewModel?): LiveData<WindowType>? {
        return (viewModel as? BrowseFileActivityViewModel)?.windowType
    }

    @Action(BrowseFileAction.GET_VIEWMODEL_CHILD_ANIM_RUNNING)
    @JvmStatic
    fun getViewModelChildAnimRunning(viewModel: ViewModel?): MutableLiveData<Boolean>? {
        return (viewModel as? BrowseFileActivityViewModel)?.childAnimRunning
    }

    @Action(BrowseFileAction.GET_VIEWMODEL_IS_FROM_OTHER)
    @JvmStatic
    fun getViewModelIsFromOther(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.isFromOtherApp ?: false
    }

    @Action(BrowseFileAction.GET_VIEWMODEL_PLAYDATA)
    @JvmStatic
    fun getViewModelPlayData(viewModel: ViewModel?): LiveData<StartPlayModel?>? {
        return (viewModel as? BrowseFileActivityViewModel)?.mCurrentPlayRecordData
    }

    @Action(BrowseFileAction.GET_LIVE_DATA_DELETE_SUMMARY_NOTE_ID)
    @JvmStatic
    fun getDeleteSummaryNoteIdLiveData(viewModel: ViewModel?): MutableLiveData<String>? {
        return (viewModel as? BrowseFileActivityViewModel)?.beDeleteSummaryNoteId
    }

    @Action(BrowseFileAction.SET_VIEWMODEL_PLAYDATA)
    @JvmStatic
    fun setViewModelPlayData(viewModel: ViewModel?, playData: StartPlayModel?) {
        (viewModel as? BrowseFileActivityViewModel)?.mCurrentPlayRecordData?.value = playData
    }

    @Action(BrowseFileAction.GET_VIEWMODEL_CLICKED_TO_RECORD)
    @JvmStatic
    fun getViewModelClickedToRecord(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.clickedToRecord ?: false
    }

    @Action(BrowseFileAction.FUN_VIEWMODEL_CLEAR_PLAYDATA)
    @JvmStatic
    fun clearViewModelPlayData(viewModel: ViewModel?) {
        (viewModel as? BrowseFileActivityViewModel)?.clearPlayRecordData()
    }

    @Action(BrowseFileAction.FUN_VIEWMODEL_IS_SMALL_WINDOW)
    @JvmStatic
    fun isSmallWindow(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.isSmallWindow() ?: false
    }

    @Action(BrowseFileAction.ON_CONVERT_SEARCH_STATE_CHANGED)
    @JvmStatic
    fun onConvertSearchStateChanged(
        activity: Activity?,
        inSearch: Boolean,
        searchTextNotEmpty: Boolean,
        outSearchFun: (() -> Unit)
    ) {
        (activity as? BrowseFile)?.onConvertSearchStateChanged(
            inSearch,
            searchTextNotEmpty,
            outSearchFun
        )
    }

    @Action(BrowseFileAction.GET_PARENT_PERCENT_DEFAULT)
    @JvmStatic
    fun getParentPercentDefault(): Float {
        return BrowsePanelController.PARENT_DEFAULT_WIDTH_PERCENT
    }

    @Action(BrowseFileAction.GET_BROWSE_FILE_ACTIVITY_NAME)
    @JvmStatic
    fun getBrowseFileActivityName(): String {
        return BrowseFile::class.java.name
    }

    @Action(BrowseFileAction.HAS_FAST_PLAYING_FILE)
    @JvmStatic
    fun hasFastPlayingFile(activity: Activity?): Boolean {
        return (activity as? BrowseFile)?.isFastPlaying() ?: false
    }

    @Action(BrowseFileAction.GET_BROWSE_FILE_ACTIVITY_NEED_REFRESH)
    @JvmStatic
    fun getBrowseFileActivityViewModel(activity: AppCompatActivity?): MutableLiveData<Boolean> {
        return activity?.let { ViewModelProvider(it) }
            ?.get(BrowseFileActivityViewModel::class.java)?.isNeedRefresh ?: MutableLiveData(true)
    }

    @Action(BrowseFileAction.IS_NOTE_EXIST)
    @JvmStatic
    fun isNoteExist(noteId: String): Boolean {
        return NoteSearchRepository.isNoteExist(noteId)
    }

    @Action(BrowseFileAction.FUN_SHOW_GROUP_CHOOSE_FRAGMENT)
    @JvmStatic
    fun showGroupChooseFragment(fragment: Fragment?, record: Record) {
        fragment?.apply {
            val mGroupViewModel: GroupViewModel by activityViewModels<GroupViewModel>()
            mGroupViewModel.mutableSelectRecordings.value = listOf(record)
            fragment.let {
                it.activity?.supportFragmentManager?.let { fragmentManager ->
                    mGroupViewModel.showChooseGroupFragment(
                        fragmentManager
                    )
                }
            }
        }
    }

    @Action(BrowseFileAction.FUN_VIEWMODEL_IS_SUPPORT_SMART_NAME)
    @JvmStatic
    fun isSupportSmartName(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.isSupportSmartName() ?: false
    }
}