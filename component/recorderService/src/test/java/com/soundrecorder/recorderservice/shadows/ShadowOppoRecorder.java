/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ShadowOppoUsbEnvironment.java
 Description:
 Version: 1.0
 Date: 2019/8/22
 Author: LI Kun
 -----------Revision History-----------
 <author> <date> <version> <desc>
 LI Kun 2019/8/22 1.0 create
 */

package com.soundrecorder.recorderservice.shadows;

import com.oppo.media.OppoRecorder;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.io.FileDescriptor;

@Implements(OppoRecorder.class)
public class ShadowOppoRecorder {

    @Implementation
    public void setAudioSource(int audioSource) {

    }

    @Implementation
    public void setOutputFile(FileDescriptor fd) {

    }
}
