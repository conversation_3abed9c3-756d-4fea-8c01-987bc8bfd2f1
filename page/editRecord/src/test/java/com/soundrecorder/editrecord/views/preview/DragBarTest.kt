/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DragBarTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/5/18 1.0 create
 */

package com.soundrecorder.editrecord.views.preview

import android.content.Context
import android.os.Build
import android.view.MotionEvent
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class DragBarTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_null_when_getPreViewBar() {
        val dragBar = DragBar(mContext!!)
        Assert.assertNull(Whitebox.invokeMethod(dragBar, "getPreViewBar"))
        Assert.assertFalse(Whitebox.invokeMethod(dragBar, "isRtl"))
    }

    @Test
    fun should_notNull_when_onTouchEvent() {
        val preViewBar = GloblePreViewBar(mContext)
        preViewBar.amplitudes = mutableListOf<Int?>().apply { add(0) }
        val dragBar = Whitebox.getInternalState<DragBar>(preViewBar, "mDragBar")
        Assert.assertNotNull(Whitebox.getInternalState(dragBar, "mListener"))
        Assert.assertFalse(dragBar.onTouchEvent(null))

        var motionEvent = MotionEvent.obtain(
            System.currentTimeMillis(), 200, MotionEvent.ACTION_DOWN, 200f, 200f, 0)
        dragBar.onTouchEvent(motionEvent)

        motionEvent = MotionEvent.obtain(
            System.currentTimeMillis(), 200, MotionEvent.ACTION_MOVE, 200f, 200f, 0)
        dragBar.onTouchEvent(motionEvent)

        motionEvent = MotionEvent.obtain(
            System.currentTimeMillis(), 200, MotionEvent.ACTION_CANCEL, 200f, 200f, 0)
        dragBar.onTouchEvent(motionEvent)
    }
}