/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameParam
 * * Description: SmartNameParam
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.databean

import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.LanguageUtil
import java.util.UUID

/**
 * 其中dialogs字段为ASR后的内容数组，结构如下:
 * data class DialogContent (
    var id: Int = 1,                                    //序号，从1开始
    val content: String,
    val timemap: Long,
    val contentType: String? = null,                    //内容类型：文字-text，图片-picture，文件-file，url
    val contentDetailType: String? = null,              //内容细分类型
    val speakerType: String,                            //语音类必填（ourside /otherside /thirdSide/videoSide）
    val language: String? = null
)*/
data class SmartNameParam(
    val sessionId: String,
    val dialogs: List<SmartParamsContent>,
    val timeout: Long = TIMEOUT,
    var otherName: String? = "",    //对话者名字
    val inputLanguage: String =  LANGUAGE_DEFAULT,
    val outputLanguage: String = LANGUAGE_DEFAULT,
    val summaryType: Int = SUMMARY_TYPE_SMART_NAME
) {

    companion object {
        const val TIMEOUT = 5 * 60 * 1000L
        private const val TAG = "ConvertTitleParam"
        const val LANGUAGE_DEFAULT = "zh"

        const val SPEAKER_TYPE_OURSIDE = "ourside"
        const val SPEAKER_TYPE_OTHERSIDE = "otherside"
        const val SPEAKER_TYPE_THIRDSIDE = "thirdSide"
        const val SPEAKER_TYPE_VIDEOSIDE = "videoSide"

        /*其中语音类请求，summaryType填入 SummaryResultType.MINI ,则输出标题*/
        const val SUMMARY_TYPE_DEFAULT = 0
        const val SUMMARY_TYPE_SMART_NAME = 1

        fun toJson(param: SmartNameParam): String {
            return GsonUtil.toJson(param)
        }

        /**{
        "id": 1,
        "timestamp": 1696902759369,
        "content": "你好，李总，最近还好吗？明天有没有空？",
        "speakerType": "我方"
        }*/
        fun toConvertSmartNameParam(beanConvertText: BeanConvertText?): SmartNameParam? {
            if (beanConvertText == null || beanConvertText.sublist.isNullOrEmpty()) {
                return null
            }
            val language = LanguageUtil.getCurrentLanguageFromSystem()
            val contents: ArrayList<SmartParamsContent> = arrayListOf()
            beanConvertText.sublist.forEachIndexed { index, subItem ->
                val contentId = index + 1
                contents.add(
                    SmartParamsContent(
                        id = contentId,
                        content = subItem.recgText,
                        timemap = System.currentTimeMillis(),
                        speakerType = SPEAKER_TYPE_OURSIDE,
                        language = language
                    )
                )
            }
            val sessionId = if (beanConvertText.traceId.isNullOrBlank()) {
                UUID.randomUUID().toString()
            } else {
                beanConvertText.traceId ?: UUID.randomUUID().toString()
            }
            if (contents.isNotEmpty()) {
                return SmartNameParam(
                    sessionId = sessionId,
                    dialogs = contents,
                    summaryType = SUMMARY_TYPE_SMART_NAME,
                    inputLanguage = language,
                    outputLanguage = language
                )
            }
            return null
        }

        fun toConvertSmartNameParam(convertContentItems: ArrayList<ConvertContentItem>?): SmartNameParam? {
            if (convertContentItems.isNullOrEmpty()) {
                return null
            }
            val language = LanguageUtil.getCurrentLanguageFromSystem()
            val contents: ArrayList<SmartParamsContent> = arrayListOf()
            convertContentItems.forEachIndexed { index, subItem ->
                val contentId = index + 1
                contents.add(SmartParamsContent(
                    contentId,
                    content = subItem.textContent,
                    timemap = System.currentTimeMillis(),
                    speakerType = SPEAKER_TYPE_OURSIDE,
                    language = language
                ))
            }

            return SmartNameParam(
                sessionId = UUID.randomUUID().toString(),
                dialogs = contents,
                inputLanguage = language,
                outputLanguage = language,
                summaryType = SUMMARY_TYPE_SMART_NAME
            )
        }
    }

    override fun toString(): String {
        return "ConvertTitleParam(sessionId=$sessionId, dialogs=$dialogs, otherName=$otherName, " +
                "inputLanguage=$inputLanguage, outputLanguage:$outputLanguage, summaryType:$summaryType)"
    }
}

data class SmartParamsContent(
    var id: Int = 0,
    var content: String?,
    var contentDetailType: String? = null,
    var contentType: String? = null,
    var language: String? = "zh",
    var speakerType: String = SmartNameParam.SPEAKER_TYPE_OURSIDE,
    var timemap: Long = 0L
) {
    override fun toString(): String {
        return "SmartParamsContent(id=$id, content.length=${content?.length}, contentDetailType=$contentDetailType, " +
                "contentType=$contentType, language:$language, speakerType:$speakerType, timemap:$timemap)"
    }
}
