/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.content.Context
import androidx.annotation.Keep

/**
 * 数据示例
 * 总结：范先生作为中信银行信用卡中心的业务代表，向何魏先生推荐了一笔额外的现金贷款服务。\n核心事件：\n1.范先生表示何魏先生的信用记录良好，因此向他提供了额外的一笔现金贷款。\n2.何魏先生拒绝了这项服务，因为他没有资金需求，并且对中信银行的信用卡体验不佳。\n待办事项：\n1.范先生可能需要记录下何魏先生的反馈，以便后续改进服务或调整营销策略。
 */
@Keep
data class SummaryModel(
    /**
     * 总结
     */
    val summary: String,

    /**
     * 待办事件
     */
    val agentEvents: List<SummaryAgentEvent>,

    /**
     * 实体，可点击
     */
    val entities: List<SummaryEntity>,

    /**
     * 溯源
     */
    val summaryTrace: List<SummaryTrace>
)

/**
 * 待办事件
 */
@Keep
data class SummaryAgentEvent(
    /**
     * 待办描述
     */
    val agent: String,

    /**
     * 是否完成
     */
    var isDone: Boolean,
)

/**
 * 摘要实体
 * 示例：
 * [
 * 	{
 * 		"name": "范先生",
 * 		"turnId": null,
 * 		"index_start": null,
 * 		"index_end": null,
 * 		"timestamp": null,
 * 		"type": "人名",
 * 		"summaryindex": 3
 * 	},
 * 	{
 * 		"name": "范先生",
 * 		"turnId": null,
 * 		"index_start": null,
 * 		"index_end": null,
 * 		"timestamp": null,
 * 		"type": "人名",
 * 		"summaryindex": 52
 * 	}
 *]
 */
@Keep
data class SummaryEntity(
    val name: String,
    val turnId: Int,
    val indexStart: Int,
    val indexEnd: Int,
    val timestamp: Long,
    val type: String,
    val summaryIndex: Int
)

@Keep
data class SummaryTrace(
    /**
     * 录音文本开始时间
     */
    val startTime: Long,

    /**
     * 录音文本结束时间
     */
    val endTime: Long,

    /**
     * 匹配的原文
     */
    val chunkText: String,

    /**
     * 溯源的文本
     */
    val traceText: String
)

@Keep
data class SummaryTheme(
    /**
     * 识别的主题
     */
    val currentTheme: Int,
) {
    companion object {
        const val SAMPLE = -100
        const val DETAIL = -101
        const val PRESS_CONFERENCE = 1001
        const val MEETING = 1002
        const val CLASSROOM = 1003
        const val INTERVIEW = 1004
        const val ACADEMIC_PAPERS = 1005

        @JvmStatic
        fun getTitle(context: Context, code: Int): String {
            return when (code) {
                SAMPLE -> context.getString(com.soundrecorder.common.R.string.summary_style_sample)
                DETAIL -> context.getString(com.soundrecorder.common.R.string.summary_style_detail)
                PRESS_CONFERENCE -> context.getString(com.soundrecorder.common.R.string.summary_style_detail)
                MEETING -> context.getString(com.soundrecorder.common.R.string.summary_style_meeting)
                CLASSROOM -> context.getString(com.soundrecorder.common.R.string.summary_style_course)
                INTERVIEW -> context.getString(com.soundrecorder.common.R.string.summary_style_interviews)
                ACADEMIC_PAPERS -> context.getString(com.soundrecorder.common.R.string.summary_style_detail)
                else -> context.getString(com.soundrecorder.common.R.string.summary_style_detail)
            }
        }
    }
}
