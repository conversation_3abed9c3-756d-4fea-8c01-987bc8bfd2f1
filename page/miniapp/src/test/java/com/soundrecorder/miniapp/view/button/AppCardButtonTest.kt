/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AppCardButtonTest
 * Description:
 * Version: 1.0
 * Date: 2023/4/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/4/18 1.0 create
 */

package com.soundrecorder.miniapp.view.button

import android.graphics.Canvas
import android.os.Build
import android.view.MotionEvent
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class AppCardButtonTest {
    @Test
    fun onDrawTest() {
        val appCardButton = Mockito.spy(AppCardButton(ApplicationProvider.getApplicationContext()))
        appCardButton.fakeDisable = true
        Whitebox.invokeMethod<Unit>(appCardButton, "onDrawForeground", Canvas())
        Assert.assertTrue(appCardButton.fakeDisable)
        appCardButton.fakeDisable = false
        Whitebox.invokeMethod<Unit>(appCardButton, "onDrawForeground", Canvas())
        Assert.assertFalse(appCardButton.fakeDisable)
        appCardButton.fakeDisable = true
        Whitebox.invokeMethod<Unit>(appCardButton, "onDrawForeground", Canvas())
        Assert.assertTrue(appCardButton.fakeDisable)
        appCardButton.fakeDisable = false
        Whitebox.invokeMethod<Unit>(appCardButton, "onDrawForeground", Canvas())
        Assert.assertFalse(appCardButton.fakeDisable)
    }

    @Test
    fun onTouchEventTest() {
        val appCardButton = Mockito.spy(AppCardButton(ApplicationProvider.getApplicationContext()))
        appCardButton.fakeDisable = true
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_DOWN, 0f, 0f, 0))
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_UP, 0f, 0f, 0))
        appCardButton.fakeDisable = false
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_DOWN, 0f, 0f, 0))
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_UP, 0f, 0f, 0))
    }
}