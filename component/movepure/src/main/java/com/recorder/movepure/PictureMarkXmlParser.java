/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: ConvertRecordXmlParser
 ** Description:for parse convert record xml file.
 ** Version:1.0
 ** Date :2019-10-11
 ** Author: tianjun
 **
 ** v1.0, 2019-10-11, tianjun, create
 ****************************************************************/
package com.recorder.movepure;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.IOException;
import java.io.StringReader;
import java.util.HashSet;

import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.constant.DatabaseConstant;

import com.soundrecorder.base.utils.DebugUtil;

import com.soundrecorder.common.databean.markdata.MarkDataBean;

public class PictureMarkXmlParser {
    private final static String TAG = PictureMarkXmlParser.class.getSimpleName();


    public static HashSet<MarkDataBean> parse(String recordString) {
        MarkDataBean markDataBean = null;
        HashSet<MarkDataBean> list = new HashSet<>();
        try {
            XmlPullParserFactory factory = XmlPullParserFactory.newInstance();
            XmlPullParser parser = factory.newPullParser();
            parser.setInput(new StringReader(recordString));

            int eventType = parser.getEventType();
            String tagName = "";
            while (eventType != XmlPullParser.END_DOCUMENT) {
                switch (eventType) {
                    case XmlPullParser.START_DOCUMENT:
                        break;
                    case XmlPullParser.START_TAG:
                        markDataBean = new MarkDataBean(0, 0);
                        tagName = parser.getName();
                        if (tagName.equals(DatabaseConstant.ROOT)) {
                            int attrNum = parser.getAttributeCount();
                            for (int i = 0; i < attrNum; ++i) {
                                String name = parser.getAttributeName(i);
                                String value = parser.getAttributeValue(i);
                                switch (name) {
                                    case PictureMarkDbUtils.TIME_IN_MILLS:
                                        markDataBean.setTimeInMills(Integer.parseInt(value));
                                        break;
                                    case PictureMarkDbUtils.VERSION:
                                        markDataBean.setVersion(Integer.parseInt(value));
                                        break;
                                    case PictureMarkDbUtils.DEFAULT_NO:
                                        markDataBean.setDefaultNo(Integer.parseInt(value));
                                        break;
                                    case PictureMarkDbUtils.MARK_TEXT:
                                        markDataBean.setMarkText(value);
                                        break;
                                    case PictureMarkDbUtils.PICTURE_FILE_PATH:
                                        markDataBean.setPictureFilePath(value);
                                        break;
                                    case PictureMarkDbUtils.KEY_ID:
                                        markDataBean.setKeyId(value);
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        break;

                    case XmlPullParser.END_TAG:
                        if (parser.getName().equals(DatabaseConstant.ROOT) && (markDataBean != null)) {
                            list.add(markDataBean);
                            DebugUtil.i(TAG, "parse markDataBean: " + markDataBean);
                        }
                        break;
                    default:
                        break;
                }

                eventType = parser.next();
            }
        } catch (XmlPullParserException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return list;
    }
}