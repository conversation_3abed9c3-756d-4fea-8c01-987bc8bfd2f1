/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: LanguagePopupMenuManager
 * Description: LanguagePopupMenuManager
 * Version: 1.0
 * Date: 2025/6/6
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/6/6      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.record.views;

import android.content.Context;
import android.view.View;
import android.widget.AdapterView;
import com.coui.appcompat.poplist.COUIPopupListWindow;
import com.coui.appcompat.poplist.PopupListItem;
import com.soundrecorder.base.utils.LanguageUtil;
import com.soundrecorder.recorderservice.api.RecorderViewModelApi;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import kotlin.Unit;

/**
 * 语言选择弹窗管理器
 * 负责管理录音界面的语言选择弹窗功能
 */
public class LanguagePopupMenuManager {
    private static final String TAG = "LanguagePopupMenuManager";

    private Context mContext;
    private COUIPopupListWindow mNormalPopupWindow = null;
    private final List<PopupListItem> mNormalItemList = new ArrayList<>();
    private OnLanguageSelectedListener mLanguageSelectedListener;

    /**
     * 语言选择回调接口
     */
    public interface OnLanguageSelectedListener {
        /**
         * 语言选择回调
         * @param languageCode 选择的语言代码
         * @param languageName 选择的语言名称
         */
        void onLanguageSelected(String languageCode, String languageName);
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    public LanguagePopupMenuManager(Context context) {
        this.mContext = context;
    }

    /**
     * 设置语言选择监听器
     * @param listener 监听器
     */
    public void setOnLanguageSelectedListener(OnLanguageSelectedListener listener) {
        this.mLanguageSelectedListener = listener;
    }
    
    /**
     * 初始化弹窗菜单
     */
    private void initPopMenu() {
        mNormalPopupWindow = new COUIPopupListWindow(mContext);
        mNormalPopupWindow.setDismissTouchOutside(true);
        mNormalPopupWindow.setUseBackgroundBlur(true);
        RecorderViewModelApi.getSupportLanguageList(asrSupportLanguageList -> {
            if (asrSupportLanguageList == null || asrSupportLanguageList.isEmpty()) {
                return Unit.INSTANCE;
            }
            Map<String, String> asrLanguages = LanguageUtil.getAsrLangMap(mContext, asrSupportLanguageList);
            if (mNormalItemList.isEmpty()) {
                initNormalItemList(asrLanguages);
            }
            mNormalPopupWindow.setItemList(mNormalItemList);
            mNormalPopupWindow.setOnItemClickListener(createItemClickListener(asrLanguages));

            return Unit.INSTANCE;
        });
    }

    /**
     * 创建弹窗项目点击监听器
     * @param asrLanguages 语言映射
     * @return 点击监听器
     */
    private AdapterView.OnItemClickListener createItemClickListener(Map<String, String> asrLanguages) {
        return (parent, view, position, id) -> {
            if (mNormalPopupWindow == null || position == 0) {
                return;
            }
            PopupListItem popupListItem = mNormalItemList.get(position);
            String title = popupListItem.getTitle();
            String code = LanguageUtil.getAsrDefaultLanguage();
            for (String key : asrLanguages.keySet()) {
                if (Objects.equals(asrLanguages.get(key), title)) {
                    code = key;
                }
            }
            if (mNormalPopupWindow.isShowing()) {
                handleLanguageSelection(code, title, position);
            }
        };
    }

    /**
     * 处理语言选择逻辑
     * @param languageCode 语言代码
     * @param languageName 语言名称
     * @param position 选择位置
     */
    private void handleLanguageSelection(String languageCode, String languageName, int position) {
        mNormalPopupWindow.getItemList().forEach(item -> item.setChecked(false));
        mNormalPopupWindow.getItemList().get(position).setChecked(true);
        // 回调通知语言选择
        if (mLanguageSelectedListener != null) {
            mLanguageSelectedListener.onLanguageSelected(languageCode, languageName);
        }
        mNormalPopupWindow.dismiss();
    }

    /**
     * 显示弹窗菜单
     */
    public void showPopupMenu(View anchorView) {
        initPopMenu();
        if (mNormalPopupWindow != null && anchorView != null) {
            mNormalPopupWindow.dismiss();
            mNormalPopupWindow.show(anchorView);
        }
    }
    
    /**
     * 初始化普通项目列表
     * @param langMap 语言映射
     */
    private void initNormalItemList(Map<String, String> langMap) {
        mNormalItemList.clear();
        PopupListItem.Builder builder = new PopupListItem.Builder();
        String title = mContext.getString(com.soundrecorder.common.R.string.audio_language_recognition);
        builder.setTitle(title);
        builder.setItemType(PopupListItem.MENU_ITEM_TYPE_HEADER);
        mNormalItemList.add(builder.build());
        builder.reset();
        if (langMap != null && !langMap.isEmpty()) {
            langMap.forEach((k, v) -> {
                builder.setTitle(v);
                builder.setIsChecked(false);
                if (Objects.equals(k, RecorderViewModelApi.getCurSelectedLanguage())) {
                    builder.setIsChecked(true);
                }
                mNormalItemList.add(builder.build());
            });
        }
    }
    
    /**
     * 销毁弹窗
     */
    public void dismiss() {
        if (mNormalPopupWindow != null) {
            mNormalPopupWindow.dismiss();
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        dismiss();
        mNormalPopupWindow = null;
        mNormalItemList.clear();
        mLanguageSelectedListener = null;
        mContext = null;
    }
}
