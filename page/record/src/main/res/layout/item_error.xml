<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp16"
    android:paddingLeft="@dimen/dp24"
    android:paddingRight="@dimen/dp24">
    <LinearLayout
        android:id="@+id/ll_error"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@drawable/background_speaker"
        android:paddingRight="@dimen/dp12"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="MissingConstraints,UseCompoundDrawables">

        <ImageView
            android:id="@+id/iv_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_error_icon" />

        <TextView
            android:id="@+id/tv_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:includeFontPadding="false"
            android:lines="1"
            android:maxWidth="@dimen/dp100"
            android:paddingStart="@dimen/dp8"
            android:paddingTop="@dimen/dp6"
            android:paddingEnd="@dimen/dp10"
            android:paddingBottom="@dimen/dp6"
            android:textColor="@color/percent_85_black"
            android:textSize="@dimen/sp10"
            tools:text="@string/subtitle_network_connect_error" />

        <TextView
            android:id="@+id/tv_retry"
            android:layout_width="@dimen/dp52"
            android:layout_height="@dimen/dp28"
            android:gravity="center"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum"
            android:lines="1"
            android:text="@string/retry"
            android:textColor="@color/directional_button_text_color_light"
            android:textSize="@dimen/sp14" />

    </LinearLayout>

</LinearLayout>