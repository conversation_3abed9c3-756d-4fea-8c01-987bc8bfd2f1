package com.soundrecorder.common.dialog;

import android.app.Activity;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import com.soundrecorder.common.R;
import com.soundrecorder.common.shadows.ShadowAppFeatureUtil;
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.shadows.ShadowCOUIMaxHeightScrollView;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class,
        ShadowAppFeatureUtil.class,
        ShadowCOUIMaxHeightScrollView.class,
        ShadowCOUIVersionUtil.class})
public class LoadingDialogTest {

    private Activity mActivity;

    @Before
    public void setUp() {
        mActivity = Robolectric.buildActivity(Activity.class).get();
    }

    @After
    public void tearDown() {
        mActivity = null;
    }

    /*
    @Test
    public void should_true_when_show() {
        AlertDialog dialog = new COUIAlertDialogBuilder(mActivity, com.support.appcompat.R.style.COUIAlertDialog_Rotating).show();
        LoadingDialog loadingDialog = new LoadingDialog(mActivity);
        Whitebox.setInternalState(loadingDialog, "mDialog", dialog);
        loadingDialog.show(R.string.app_name_main, false, false);
        Assert.assertTrue(dialog.isShowing());
        Assert.assertNotNull(loadingDialog);
        Assert.assertNotNull(dialog);
    }
    */

    @Test
    public void should_false_when_dismiss() {
        LoadingDialog loadingDialog = new LoadingDialog(mActivity);
        loadingDialog.show(R.string.app_name_main, false, false);
        loadingDialog.dismiss();
        Assert.assertFalse(loadingDialog.isShowing());
    }

    @Test
    public void should_false_when_window_detached() throws Exception {
        LoadingDialog loadingDialog = new LoadingDialog(mActivity);
        Assert.assertFalse(loadingDialog.isShowing());
        loadingDialog.show(R.string.app_name_main, false, false);
        Assert.assertTrue(loadingDialog.isShowing());
    }

}
