package com.soundrecorder.wavemark.wave.id3tool;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class AbstractID3v2TagTest {
    AbstractID3v2Tag abstractID3v2Tag;
    byte[] bytes;

    @Before
    public void setup() {
        bytes = new byte[12];
        bytes[AbstractID3v2Tag.MAJOR_VERSION_OFFSET] = 2;
        bytes[AbstractID3v2Tag.MINOR_VERSION_OFFSET] = 2;
        abstractID3v2Tag = new AbstractID3v2Tag() {
            @Override
            protected void unpackFlags(byte[] bytes) {

            }

            @Override
            protected void packFlags(byte[] bytes, int i) {

            }
        };
    }

    @After
    public void tearDown() {
        bytes = null;
        abstractID3v2Tag = null;
    }

    @Test
    public void should_returnValue_when_unpackHeader() throws Exception {
        MockedStatic<BufferTools> mocked = Mockito.mockStatic(BufferTools.class);
        mocked.when(() -> BufferTools.unpackSynchsafeInteger(bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 1],
                bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 2], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 3])).thenReturn(2);
        mocked.when(() -> BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 0, AbstractID3v2Tag.TAG.length())).thenReturn(AbstractID3v2Tag.TAG);
        int size = Whitebox.invokeMethod(abstractID3v2Tag, "unpackHeader", bytes);
        Assert.assertEquals(size, 10);
        mocked.close();
    }


    @Test
    public void should_returnValue_when_unpackFooter() throws Exception {
        MockedStatic<BufferTools> mocked = Mockito.mockStatic(BufferTools.class);
        mocked.when(() -> BufferTools.unpackSynchsafeInteger(bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 1],
                bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 2], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 3])).thenReturn(2);
        mocked.when(() -> BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 0, AbstractID3v2Tag.TAG.length())).thenReturn(AbstractID3v2Tag.TAG);
        mocked.when(() -> BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 2, AbstractID3v2Tag.FOOTER_TAG.length())).thenReturn(AbstractID3v2Tag.FOOTER_TAG);
        int size = Whitebox.invokeMethod(abstractID3v2Tag, "unpackFooter", bytes, 2);
        Assert.assertEquals(size, 10);
        mocked.close();
    }

    @Test
    public void should_returnNotnull_when_toBytes() throws Exception {
        MockedStatic<BufferTools> mocked = Mockito.mockStatic(BufferTools.class);
        mocked.when(() -> BufferTools.unpackSynchsafeInteger(bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 1],
                bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 2], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 3])).thenReturn(2);
        mocked.when(() -> BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 0, AbstractID3v2Tag.TAG.length())).thenReturn(AbstractID3v2Tag.TAG);
        Whitebox.setInternalState(abstractID3v2Tag, "footer", true, AbstractID3v2Tag.class);
        Whitebox.setInternalState(abstractID3v2Tag, "version", "2.4", AbstractID3v2Tag.class);
        Assert.assertNotNull(abstractID3v2Tag.toBytes());
        mocked.close();
    }

    @Test
    public void should_returnNotnull_when_calculateDataLength() throws Exception {
        abstractID3v2Tag = Mockito.mock(AbstractID3v2Tag.class);
        Map<String, ID3v2FrameSet> frameSets = new HashMap<>();
        frameSets.put("TPOS", new ID3v2FrameSet("1"));
        Whitebox.setInternalState(abstractID3v2Tag, "frameSets", frameSets);
        int length = Whitebox.invokeMethod(abstractID3v2Tag, "calculateDataLength");
        Assert.assertEquals(length, 0);
    }

    @Test
    public void should_returnNotnull_when_getTrack() {
        abstractID3v2Tag = Mockito.mock(AbstractID3v2Tag.class);
        Assert.assertNull(abstractID3v2Tag.getTrack());
    }

    @Test
    public void should_returnNotnull_when_setTrack() {
        abstractID3v2Tag.setTrack("test");
        Map<String, ID3v2FrameSet> frameSets = Whitebox.getInternalState(abstractID3v2Tag, "frameSets");
        Assert.assertEquals(frameSets.size(), 1);
    }

    @Test
    public void should_returnNotnull_when_getPartOfSet() {
        Assert.assertNull(abstractID3v2Tag.getPartOfSet());
    }

    @Test
    public void should_returnNotnull_when_getArtist() {
        abstractID3v2Tag.setArtist("2017");
        Assert.assertNotNull(abstractID3v2Tag.getArtist());
        abstractID3v2Tag.setAlbumArtist("2017");
        Assert.assertNotNull(abstractID3v2Tag.getAlbumArtist());
        abstractID3v2Tag.setAlbum("2017");
        Assert.assertNotNull(abstractID3v2Tag.getAlbum());
        abstractID3v2Tag.setTitle("2017");
        Assert.assertNotNull(abstractID3v2Tag.getTitle());
    }

    @Test
    public void should_returnNotnull_when_setYear() {
        abstractID3v2Tag.setYear("2017");
        Assert.assertEquals(abstractID3v2Tag.getYear(), "2017");
        abstractID3v2Tag.setYear("2017");
        Assert.assertEquals(abstractID3v2Tag.getYear(), "2017");
        Assert.assertEquals(abstractID3v2Tag.getGenre(), -1);
        abstractID3v2Tag.setGenre(12);
        Assert.assertEquals(abstractID3v2Tag.getGenre(), 12);
    }

    @Test
    public void should_returnNotnull_when_getDate() {
        abstractID3v2Tag = Mockito.mock(AbstractID3v2Tag.class);
        abstractID3v2Tag.setDate("2017-12-3");
        Assert.assertNull(abstractID3v2Tag.getDate());
    }

    @Test
    public void should_returnValue_when_setBPM() {
        abstractID3v2Tag.setBPM(12);
        Assert.assertEquals(abstractID3v2Tag.getBPM(), 12);
    }

    @Test
    public void should_returnValue_when_setKey() {
        abstractID3v2Tag.setKey("test");
        Assert.assertEquals(abstractID3v2Tag.getKey(), "test");
    }

    @Test
    public void should_returnValue_when_setComposer() {
        abstractID3v2Tag.setComposer("123");
        Assert.assertNotEquals(abstractID3v2Tag.getComposer(), "test");
    }

    @Test
    public void should_returnValue_when_setGenreDescription() {
        abstractID3v2Tag.setGenreDescription("Country");
        Assert.assertEquals(abstractID3v2Tag.getGenreDescription(), "Country");
    }

    @Test
    public void should_returnValue_when_getandset() {
        Assert.assertEquals(abstractID3v2Tag.extractGenreNumber("11"), 11);
        Assert.assertNotEquals(abstractID3v2Tag.extractGenreDescription("(1234)"), 1234);
        abstractID3v2Tag.setPaymentUrl("test");
        Assert.assertEquals(abstractID3v2Tag.getPaymentUrl(), "test");
        abstractID3v2Tag.setPublisherUrl("test");
        Assert.assertEquals(abstractID3v2Tag.getPublisherUrl(), "test");
    }

    @Test
    public void should_returnNotnull_when_setPublisher() throws Exception {
        MockedStatic<BufferTools> mocked = Mockito.mockStatic(BufferTools.class);
        mocked.when(() -> BufferTools.unpackSynchsafeInteger(bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 1],
                bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 2], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 3])).thenReturn(2);
        mocked.when(() -> BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 0, AbstractID3v2Tag.TAG.length())).thenReturn(AbstractID3v2Tag.TAG);
        abstractID3v2Tag = new AbstractID3v2Tag(bytes, false) {
            @Override
            protected void unpackFlags(byte[] bytes) {

            }

            @Override
            protected void packFlags(byte[] bytes, int i) {

            }
        };
        abstractID3v2Tag.setArtistUrl("test");
        Assert.assertNotEquals(abstractID3v2Tag.getArtistUrl(), "test");
        abstractID3v2Tag.setCommercialUrl("test");
        Assert.assertNotEquals(abstractID3v2Tag.getCommercialUrl(), "test");
        abstractID3v2Tag.setCopyrightUrl("test");
        Assert.assertNotEquals(abstractID3v2Tag.getCopyrightUrl(), "test");
        abstractID3v2Tag.setAudiofileUrl("test");
        Assert.assertNotEquals(abstractID3v2Tag.getAudiofileUrl(), "test");
        abstractID3v2Tag.setAudioSourceUrl("test");
        Assert.assertNotEquals(abstractID3v2Tag.getAudioSourceUrl(), "test");
        abstractID3v2Tag.setRadiostationUrl("test");
        Assert.assertNotEquals(abstractID3v2Tag.getRadiostationUrl(), "test");
        mocked.close();
    }

    @Test
    public void should_returnNotnull_when_unpackExtendedHeader() throws Exception {
        MockedStatic<BufferTools> mocked = Mockito.mockStatic(BufferTools.class);
        mocked.when(() -> BufferTools.unpackSynchsafeInteger(bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 1],
                bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 2], bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + 3])).thenReturn(2);
        mocked.when(() -> BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 0, AbstractID3v2Tag.TAG.length())).thenReturn(AbstractID3v2Tag.TAG);
        abstractID3v2Tag = new AbstractID3v2Tag(bytes, false) {
            @Override
            protected void unpackFlags(byte[] bytes) {

            }

            @Override
            protected void packFlags(byte[] bytes, int i) {

            }
        };
        int position = Whitebox.invokeMethod(abstractID3v2Tag, "unpackExtendedHeader", bytes, 0);
        Assert.assertEquals(position, 4);
        mocked.close();
    }

    @Test
    public void should_returnNotnull_when_getLyrics() throws Exception {
        abstractID3v2Tag.setLyrics("test");
        Assert.assertEquals(abstractID3v2Tag.getLyrics(), "test");
        abstractID3v2Tag.setComposer("test");
        Assert.assertEquals(abstractID3v2Tag.getComposer(), "test");
        abstractID3v2Tag.setOriginalArtist("test");
        Assert.assertEquals(abstractID3v2Tag.getOriginalArtist(), "test");
        abstractID3v2Tag.setCopyright("test");
        Assert.assertEquals(abstractID3v2Tag.getCopyright(), "test");
        abstractID3v2Tag.setUrl("http://www.baidu.com");
        Assert.assertEquals(abstractID3v2Tag.getUrl(), "http://www.baidu.com");
        abstractID3v2Tag.setChapters(new ArrayList<>());
        Assert.assertNull(abstractID3v2Tag.getChapters());
        abstractID3v2Tag.setChapterTOC(new ArrayList<>());
        Assert.assertNull(abstractID3v2Tag.getChapterTOC());
        abstractID3v2Tag.setEncoder("test");
        Assert.assertNotNull(abstractID3v2Tag.getEncoder());
        abstractID3v2Tag.setAlbumImage(bytes, "test");
        Assert.assertNotNull(abstractID3v2Tag.getAlbumImage());
        Assert.assertNotNull(abstractID3v2Tag.getAlbumImageMimeType());
        Assert.assertNotNull(abstractID3v2Tag.getWmpRating());

    }
}
