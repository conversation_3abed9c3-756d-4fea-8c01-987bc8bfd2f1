package com.soundrecorder.browsefile.home.load

import android.database.Cursor
import com.soundrecorder.common.databean.Record

class BrowseListCount {
    var cursor: Cursor? = null
    var allCount: Int = 0
    var standardCount: Int = 0
    var meetingCount: Int = 0
    var interviewCount: Int = 0
    var callCount: Int = 0
    var recycleCount: Int = 0
    var dbRecordsList: List<Record>? = null
    var commonCount: Int = 0
    var callerNameCount: Int = 0

    fun addAllCount() {
        allCount++
    }

    fun addStandardCount() {
        standardCount++
    }

    fun addMeetingCount() {
        meetingCount++
    }

    fun addInterviewCount() {
        interviewCount++
    }

    fun addCallCount() {
        callCount++
    }

    fun addRecycleCount() {
        recycleCount++
    }

    fun addCommonCount() {
        commonCount++
    }

    fun addCallerNameCount() {
        callerNameCount++
    }

    fun release() {
        cursor?.close()
        cursor = null
        allCount = 0
        standardCount = 0
        meetingCount = 0
        interviewCount = 0
        callCount = 0
        recycleCount = 0
        commonCount = 0
        callerNameCount = 0
        dbRecordsList = null
    }

    override fun toString(): String {
        return "allRecordCount:" + allCount +
                ",commonRecordCount:" + commonCount +
                ",callRecordCount:" + callCount +
                ",standardRecordCount:" + standardCount +
                ",meetingRecordCount:" + meetingCount +
                ",interviewRecordCount:" + interviewCount +
                ",callRecordCount:" + callCount +
                ",recentlyCount:" + recycleCount
    }
}