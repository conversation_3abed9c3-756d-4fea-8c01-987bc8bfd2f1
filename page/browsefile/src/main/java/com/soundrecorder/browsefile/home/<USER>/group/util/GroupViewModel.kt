/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: GroupViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/02/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/02/10      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.util

import androidx.fragment.app.FragmentManager
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.BaseApplication.getApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.view.group.entity.GroupFactory
import com.soundrecorder.browsefile.home.view.group.view.GroupChooseFragment
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudTipManagerAction
import kotlinx.coroutines.launch
import java.io.File

class GroupViewModel : ViewModel() {

    companion object {
        private const val TAG = "GroupViewModel"
    }

    /*移动分组后的callback*/
    val onMoveGroupCallBack: MutableLiveData<GroupChooseFragment.OnMoveGroupCallBack> =
        MutableLiveData()
    /*选中的音频列表*/
    var mutableSelectRecordings: MutableLiveData<List<Record>> = MutableLiveData()
    var currentGroup = MutableLiveData<GroupInfo>()
     /*自定义分组数据记录*/
    var customGroupInfoList =  MutableLiveData<List<GroupInfo>>().apply {
        value = emptyList()
    }
    var isCloudEnable: () -> Boolean = { CloudTipManagerAction.isCloudSwitchOn() }
    private val folderCacheManager = GroupCacheManager()

    var groupsInDb: MutableLiveData<List<GroupInfo>> = MutableLiveData<List<GroupInfo>>().apply {
        value = emptyList()
    }
    /*是否移动了分组*/
    var isMovedGroup = MutableLiveData(false)

    fun findRichGroupCountInGroup(groupInfo: GroupInfo?): Int {
        //查询该分组下的数量。
        return groupInfo?.mGroupCount ?: 0
    }

    fun findAllGroup(): GroupInfo {
        val inMemory = groupsInDb.value?.find { GroupFactory.isAllRecordingGroup(it) }
        return GroupFactory.regenerateAllGroup(getApplication(), inMemory)
    }

    fun updateCurrentGroup(groupInfo: GroupInfo?, browseFileActivityViewModel: BrowseFileActivityViewModel): Boolean {
        val curFolder = this.currentGroup.value
        if (groupInfo != null && groupInfo.mId != curFolder?.mId) {
            //切换了分组，退出当前页面时，仅通知显示Loading, 刷新数据由首页列表的refreshData()控制
            browseFileActivityViewModel.isShowLoadingWithRefresh.postValueSafe(false)

            browseFileActivityViewModel.currentGroup.postValueSafe(groupInfo)
            this.currentGroup.value = groupInfo
            if (!GroupFactory.isRecentDeleteGroup(groupInfo)) {
                viewModelScope.launch {
                    folderCacheManager.update(
                        getApplication(),
                        groupInfo.mId,
                        groupInfo.mUuId
                    )
                }
            }
            return true
        }
        return false
    }

    fun showChooseGroupFragment(fm: FragmentManager) {
        GroupChooseFragment.show(fm)
    }

    fun getRecordSizeFromDbByGroup(group: GroupInfo): Int {
        val orderClause = "$COLUMN_NAME_DATE_MODIFIED DESC, $COLUMN_NAME_DISPLAY_NAME DESC"
        //自定义分组查询条件，过滤掉已经被删除的记录
        var selection = "${DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID} = ? " +
                "AND ${DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE} != ?"
        var selectionArgs = arrayOf(group.mId.toString(), RecordConstant.RECORD_DELETED.toString())
        var filePathColumnName = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA

        //回收站查询条件
        if (group.isRecentlyDeleteGroup()) {
            selection = "${DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE} = ?"
            selectionArgs = arrayOf(RecordConstant.RECORD_DELETED.toString())
            filePathColumnName = DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH
        }
        val projection = DatabaseConstant.getDistinctRecordProjection(filePathColumnName)
        val resolver = BaseApplication.getAppContext().contentResolver
        val cursor = resolver.query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, projection, selection, selectionArgs, orderClause)
        val filePathSet = HashSet<String>()
        cursor?.use {
            while (it.moveToNext()) {
                kotlin.runCatching {
                    //判断文件是否存在，不存在则不显示
                    val columnIndex = cursor.getColumnIndex(filePathColumnName)
                    if (columnIndex >= 0) {
                        val filePath = cursor.getString(columnIndex)
                        if (!File(filePath).exists()) {
                            return@runCatching
                        }
                        filePathSet.add(filePath)
                    }
                }.onFailure {
                    DebugUtil.e(TAG, "query getRecordsByGroup error, ${it.message}")
                }
            }
        }
        return filePathSet.size
    }
}
