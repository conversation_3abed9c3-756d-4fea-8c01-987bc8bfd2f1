/************************************************************
 * All rights reserved.
 * FileName      : RecorderExpanded.java
 * Version Number: 1.0
 * Description   : extends MediaRecorder with method getTime, pause, resume, setAudioQuality
 * Author        : zhanghr
 * Date          : 2009-12-1
 * History       :(ID,  2009-12-1, zhanghr, Description)
 ************************************************************/

package com.soundrecorder.recorderservice.recorder;

import android.media.MediaRecorder;
import androidx.annotation.NonNull;
import org.jetbrains.annotations.Nullable;
import java.io.FileDescriptor;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.constant.RecorderConstant;
import com.soundrecorder.recorderservice.recorder.listener.IBizRecorder;
import com.soundrecorder.recorderservice.recorder.listener.IOnErrorListener;
import com.soundrecorder.recorderservice.recorder.listener.IOnInfoListener;
import com.oplus.media.OplusRecorder;
import com.soundrecorder.recorderservice.recorder.utils.SimpleTimer;

public class OPlusRecorderExpanded extends OplusRecorder implements IBizRecorder {
    private static final String TAG = "RecorderWithTimer";
    private SimpleTimer mSimpleTimer;
    private volatile boolean mAudioSourceSetted = false;
    private String mSuffix = "";
    private String mMimeType = "";

    public OPlusRecorderExpanded() {
        this(OutputFormat.MP3);
    }

    public OPlusRecorderExpanded(int format) {
        super();
        mSimpleTimer = new SimpleTimer();
        switch (format) {
            case RecorderConstant.RECORDER_AUDIO_FORMAT_MP3:
                initMp3Config();
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_NB:
                initAmrConfig();
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_WAV:
                initWavConfig();
                break;
            default:
                initMp3Config();
                break;
        }
    }

    private void initMp3Config() {
        setAudioSource(OplusRecorder.AudioSource.MIC);
        setAudioSamplingRate(RecorderConstant.SAMPLE_RATE_48000);
        setAudioChannels(2);
        setOutputFormat(OplusRecorder.OutputFormat.MP3);
        setAudioEncoder(OplusRecorder.AudioEncoder.MPEG);
        setAudioEncodingBitRate(320); //change form 320 to 128 to reduce the size of MP3 file
        mSuffix = RecorderConstant.MP3_FILE_SUFFIX;
        mMimeType = RecordConstant.MIMETYPE_MP3;
    }

    private void initAmrConfig() {
        setAudioSource(OplusRecorder.AudioSource.MIC);
        setOutputFormat(OplusRecorder.OutputFormat.AMR_NB);
        setAudioEncoder(OplusRecorder.AudioEncoder.AMR_NB);
        mSuffix = RecorderConstant.AMR_FILE_SUFFIX;
        mMimeType = RecordConstant.MIMETYPE_AMR;
    }

    private void initWavConfig() {
        setAudioSource(OplusRecorder.AudioSource.MIC);
        setOutputFormat(OplusRecorder.OutputFormat.WAV);
        setAudioSamplingRate(RecorderConstant.SAMPLE_RATE_48000); //change sampling rate to 16KHz,8000 16000 44100 48000
        setAudioChannels(2);
        setAudioEncoder(OplusRecorder.AudioEncoder.WAV);
        mSuffix = RecorderConstant.WAV_FILE_SUFFIX;
        mMimeType = RecordConstant.MIMETYPE_WAV;
    }

    @Override
    public void expandFile(String path, int as) {
        mAudioSourceSetted = true;
        super.expandFile(path, as);
    }

    @Override
    public void expandFile(FileDescriptor fd, long offset, long length, int as) {
        mAudioSourceSetted = true;
        super.expandFile(fd, offset, length, as);
    }

    @Override
    public void start() {
        super.start();
        mSimpleTimer.start();
    }

    public void setAppendTime(long time) {
        mSimpleTimer.setAppendTime(time);
    }

    @Override
    public void stop() {
        mAudioSourceSetted = false;
        super.stop();
        mSimpleTimer.stop();
        reset();
    }

    /**
     * 暂停录音
     * OplusRecorder的pause操作，仅仅是不编码、不往mp3文件里面里面继续写编码后的数据而已。
     * 不会改变AudioRecord及AUDIO HAL的状态；
     * AudioRecord只提供 startRecording() stopRecording()  两个状态切换；
     * 在start和stop中间，OplusRecorder通过AudioRecord做read操作。没有其他中间状态。
     * 所以如果暂停通过OplusRecorder.pause会导致底层录音通道一直开启，算法一直运行
     */
    public void stopForPause() {
        super.stop();
        mAudioSourceSetted = false;
        if (!mSimpleTimer.isStopped()) {
            mSimpleTimer.pause();
            super.reset();
        }
    }

    /*
     * Be careful ! If this method is named pause, an error of stack over flow
     * will occur .
     */
    public void pauseNow() {
        mSimpleTimer.pause();
        try {
            OplusRecorder.class.getMethod("pause").invoke(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * Be careful ! If this method is named resume, an error of stack over flow
     * will occur .
     */
    public void resumeNow() {

        mSimpleTimer.resume();

        try {
            OplusRecorder.class.getMethod("resume").invoke(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public long getTime() {
        return mSimpleTimer.getTime();
    }


    public void setFormatToWav() {

    }

    /*
     * Be careful ! If this method is named setAudioQuality, an error of stack
     * over flow will occur .
     */
    public void setAudioQualityNow(int quality) {
        try {
            OplusRecorder.class.getMethod("setAudioQuality", int.class).invoke(this, quality);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setAudioSource(int audioSource) {
        super.setAudioSource(audioSource);
        mAudioSourceSetted = true;
    }

    @Override
    public void reset() {
        mSimpleTimer.reset();
        mAudioSourceSetted = false;
        super.reset();
    }

    // make it safer
    @Override
    public int getMaxAmplitude() {
        if (mAudioSourceSetted) {
            return super.getMaxAmplitude();
        } else {
            return 0;
        }
    }

    @Override
    public void setOnInfoListener(@Nullable IOnInfoListener listener) {
        super.setOnInfoListener(listener);
    }

    @Override
    public void setOnErrorListener(@Nullable IOnErrorListener listener) {
        super.setOnErrorListener(listener);
    }

    @NonNull
    @Override
    public String getRecorderSuffix() {
        return mSuffix;
    }

    @NonNull
    @Override
    public String getRecorderMimeType() {
        return mMimeType;
    }

    @Override
    public boolean isPausedByResetRecorder() {
        return true;
    }

    @Override
    public MediaRecorder getMediaRecorder() {
        return null;
    }
}
