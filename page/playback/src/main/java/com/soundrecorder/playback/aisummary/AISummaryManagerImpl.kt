/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  AISummaryManagerImpl
 * * Description: AI摘要管理器实现类，参考SmartNameManagerImpl
 * * Version: 1.0
 * * Date : 2024/12/19
 * * Author: Assistant
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  Assistant    2024/12/19   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback.aisummary

import android.app.Activity
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.BaseApplication.getAppContext
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryManager
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.playback.cloudconfig.CloudConfigUtils
import com.soundrecorder.common.databean.Record
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * AI摘要管理器实现类
 */
class AISummaryManagerImpl : IAISummaryManager, LifecycleObserver {

    companion object {
        private const val TAG = "AISummaryManagerImpl"
    }

    private var mSelectMediaIdList: MutableList<Long>? = null
    private var mUnifiedSummaryManager: IUnifiedSummaryCallBack? = null
    private var mPermissionCallback: IAISummaryManager? = null

    override fun registerForAISummary(viewModelStoreOwner: Fragment) {
        // 保存权限回调接口
        if (viewModelStoreOwner is IAISummaryManager) {
            mPermissionCallback = viewModelStoreOwner
        }
        viewModelStoreOwner.lifecycle.addObserver(this)
    }

    /**
     * AI摘要入口
     * selectedMediaIdList: 选中的录音
     */
    override fun startAISummaryClickHandle(activity: Activity?, selectedMediaIdList: MutableList<Long>?) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        DebugUtil.d(TAG, "startAISummaryClickHandle, selectedMediaIdList size:${selectedMediaIdList.size}")
        this.mSelectMediaIdList = selectedMediaIdList

        // 检查隐私政策声明
        if (!BaseUtil.isEXP() && !PermissionUtils.isStatementConvertGranted(getAppContext())) {
            showStatementWithFirstUseAISummary(activity)
        } else {
            startOrResumeAISummary(activity, mSelectMediaIdList)
        }
    }

    /**
     * 初次使用AI摘要，需要显示声明弹窗
     */
    private fun showStatementWithFirstUseAISummary(activity: Activity?, forceShow: Boolean = false) {
        (activity as? PrivacyPolicyBaseActivity)?.apply {
            lifecycleScope.launchWhenResumed {
                /* 此方法必须在onResume中执行，onRestoreInstanceState重建机制在onResume之前执行 */
                if (!PermissionUtils.isStatementConvertGranted(getAppContext())) {
                    getPrivacyPolicyDelegate()?.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT, forceShow)
                }
            }
        }
    }

    /**
     * 检查AI摘要插件是否下载
     */
    override fun checkPluginsDownload(activity: Activity?, callback: ((Boolean) -> Unit)?) {
        if (activity == null) {
            return
        }
        if (mUnifiedSummaryManager == null) {
            DebugUtil.d(TAG, "checkPluginsDownload, newUnifiedSummaryManager")
            mUnifiedSummaryManager = SmartNameAction.newUnifiedSummaryManager()
        }
        mUnifiedSummaryManager?.showAiUnitPluginsDialog(activity, AISummaryPluginDownloadCallback(callback))
    }

    /**
     * 开始AI摘要 fragment 中调用
     */
    override fun startOrResumeAISummary(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
    ) {
        if (activity == null || selectedMediaIdList.isNullOrEmpty()) {
            return
        }

        /**
         * 检查网络连接
         * if (NetworkUtils.isNetworkInvalid(getAppContext())) {
         *             showToastMessage("无网络连接，生成失败")
         *             return
         *         }
         */

        checkPluginsDownload(activity, { download ->
            if (download) {
                DebugUtil.d(TAG, "checkPluginsDownload success, thread:${Thread.currentThread()}")
                /* 对每个文件进行检查后启动摘要 */
                startAISummaryWithFileCheck(selectedMediaIdList)
            }
        })
    }

    override fun doClickPermissionAISummaryOK(activity: Activity?) {
        DebugUtil.d(TAG, "doClickPermissionAISummaryOK")

        // 设置权限状态
        PermissionUtils.setConvertGrantedStatus(getAppContext())
        PermissionUtils.setNetWorkGrantedStatus(getAppContext(), true)
        CloudConfigUtils.updateConvertConfig()

        // 通知Fragment权限已确认
        mPermissionCallback?.doClickPermissionAISummaryOK(activity)

        // 继续启动摘要
        startOrResumeAISummary(activity, mSelectMediaIdList)
    }


    override fun release() {
        DebugUtil.d(TAG, "release")
        mSelectMediaIdList?.clear()
        mUnifiedSummaryManager?.releaseAllDialog()
        mUnifiedSummaryManager = null
        mPermissionCallback = null
    }

    override fun releaseAll() {
        DebugUtil.d(TAG, "releaseAll")
        release()
    }

    /**
     * 带文件检查的摘要启动逻辑
     */
    private fun startAISummaryWithFileCheck(selectedMediaIdList: MutableList<Long>) {
        CoroutineScope(Dispatchers.IO).launch {
            val idList = arrayOfNulls<String?>(selectedMediaIdList.size)
            selectedMediaIdList.forEachIndexed { index, id ->
                idList[index] = id.toString()
            }

            val recordList = MediaDBUtils.getMediaRecordsById(getAppContext(), idList)
            recordList?.forEach { record ->
                val mediaId = record.id
                val canSummary = checkCanSummary(record)
                DebugUtil.d(TAG, "startAISummaryWithFileCheck, mediaId:$mediaId, canSummary:$canSummary")
                if (canSummary) {
                    AISummaryAction.startAISummary(mediaId)
                }
            }
        }
    }

    /**
     * 检查文件是否可以生成摘要
     */
    private fun checkCanSummary(mediaRecord: Record): Boolean {
        val mediaId = mediaRecord.id
        /**
         * 网络检查
         * val context = getAppContext()
         *if (NetworkUtils.isNetworkInvalid(context)) {
         *             showToastMessage("无网络连接，生成失败")
         *             return false
         *         }
         */

        DebugUtil.i(TAG, "CheckAISummaryTask start. record:$mediaRecord")

        // 文件格式检查
        val suffix = mediaRecord.data.suffix()
        if (!isSuffixSupport(suffix)) {
            //showToastMessage(R.string.summary_error_record_format_not_support)
            return false
        }

        var fileFormat: String? = null
        var fileDuration: Long? = null
        val fileSize: Long? = mediaRecord.mFileSize

        // 获取文件元数据
        if (PermissionUtils.hasReadAudioPermission()) {
            ConvertDbUtil.updateRecordIdByMediaPath(mediaRecord.data, mediaId)
        }
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        if (convertRecord == null) {
            DebugUtil.i(TAG, "MediaMetadataRetriever start.")
            val mLocalUri = MediaDBUtils.genUri(mediaId)
            val pairFormatAndDuration = MediaDBUtils.getFileFormatAndDurationFromUri(mLocalUri)
            fileFormat = pairFormatAndDuration.first
            fileDuration = pairFormatAndDuration.second
            DebugUtil.i(TAG, "MediaMetadataRetriever end. mFileFormat:$fileFormat, mFileDuration:$fileDuration")
        }

        // 文件大小检查
        fileSize?.let {
            if (!ConvertCheckUtils.isFileSizeMinMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, upload_status_exception")
                //showToastMessage(R.string.summary_error_record_format_not_support)
                return false
            }
            if (!ConvertCheckUtils.isFileSizeMaxMet(fileSize)) {
                //showToastMessage(R.string.convert_error_size_long)
                return false
            }
        }

        // 文件时长检查
        if (!isFileDurationMet(fileDuration)) {
            return false
        }

        // 文件格式检查
        if (!isFileFormatMet(fileFormat)) {
            return false
        }

        return true
    }

    /**
     * 检查文件格式是否支持
     */
    private fun isSuffixSupport(suffix: String?): Boolean {
        DebugUtil.d(TAG, "isSuffixSupport, suffix:$suffix")
        if (suffix.isNullOrEmpty()) {
            return false
        }
        return when (suffix) {
            RecorderConstant.MP3_FILE_SUFFIX,
            RecorderConstant.AAC_FILE_SUFFIX,
            RecorderConstant.WAV_FILE_SUFFIX,
            RecorderConstant.AMR_FILE_SUFFIX,
            RecorderConstant.AMR_WB_FILE_SUFFIX -> true
            else -> false
        }
    }

    /**
     * 检查文件时长是否符合要求
     */
    private fun isFileDurationMet(fileDuration: Long?): Boolean {
        fileDuration?.let {
            if (!ConvertCheckUtils.isFileDurationMinMet(it)) {
                DebugUtil.w(TAG, "mFileDuration <= 0!")
                //showToastMessage(R.string.summary_error_record_format_not_support)
                return false
            }
            if (!ConvertCheckUtils.isFileDurationMaxMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, fileDuration max")
                //showToastMessage(R.plurals.summary_error_record_long)
                return false
            }
        }
        return true
    }

    /**
     * 检查文件格式是否符合要求
     */
    private fun isFileFormatMet(fileFormat: String?): Boolean {
        fileFormat?.let {
            return ConvertCheckUtils.isFileFormatMet(it).apply {
                if (!this) {
                    //showToastMessage(R.string.summary_error_record_format_not_support)
                }
            }
        }
        return true
    }

    /**
     * 显示Toast消息
     *
     * private fun showToastMessage(message: String) {
     *         ToastManager.showShortToast(getAppContext(), message)
     * }
     *
     * private fun showToastMessage(resId: Int) {
     *         ToastManager.showShortToast(BaseApplication.getAppContext(), resId)
     * }
     */


    /**
     * AI摘要插件下载回调
     */
    private class AISummaryPluginDownloadCallback(private val callback: ((Boolean) -> Unit)?) : IPluginDownloadCallback {
        override fun onDownLoadResult(result: Boolean) {
            DebugUtil.d(TAG, "onDownLoadResult result: $result")
            callback?.invoke(result)
        }
    }
}
