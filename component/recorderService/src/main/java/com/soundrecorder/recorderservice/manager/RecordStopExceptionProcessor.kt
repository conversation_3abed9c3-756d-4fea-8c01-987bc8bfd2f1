/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordStopExceptionProcessor
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import com.soundrecorder.base.utils.DebugUtil

object RecordStopExceptionProcessor {

    const val TAG = "RecordStopExceptionProcessor"
    //录制音频文件达到最大时长和最大大小限制，需要停止录音
    const val STOP_EVENT_TYPE_RECORDE_OVERLIMIT = 1L
    //录制音频文件时，相机开始，需要停止录音
    const val STOP_EVENT_TYPE_CAMERA = 2L
    //录制音频文件时，存储控件不够，需要停止录音
    const val STOP_EVENT_TYPE_STORAGE_NOT_ENOUGH = 3L
    //录制音频的时候OnErrorInfoListener中的onError回调，需要停止路易i你
    const val STOP_EVENT_TYPE_RECORDE_ERROR_INFO = 4L
    //录制音频文件时, doStart，doPause，doStop的时候底层Recorder出现Exception，需要停止录音
    const val STOP_EVENT_TYPE_RECORDE_EXCEPTION = 5L
    //录制音频文件时，正在录制的音频文件被异常删除，需要停止录音
    const val STOP_EVENT_TYPE_RECORDINGFILE_DELETE = 6L
    //录制音频文件时，收到关机广播, 需要
    const val STOP_EVENT_TYPE_SHUT_DOWN = 7L

    val STOP_EVENTS = longArrayOf(STOP_EVENT_TYPE_RECORDE_OVERLIMIT,
        STOP_EVENT_TYPE_CAMERA,
        STOP_EVENT_TYPE_STORAGE_NOT_ENOUGH,
        STOP_EVENT_TYPE_RECORDE_ERROR_INFO,
        STOP_EVENT_TYPE_RECORDE_EXCEPTION,
        STOP_EVENT_TYPE_RECORDINGFILE_DELETE,
        STOP_EVENT_TYPE_SHUT_DOWN)


    private var mStopEventListener: IStopEventListener? = null



    fun registerStopEventListener(listener: IStopEventListener) {
        DebugUtil.i(TAG, "registerStopEventListener $listener")
        mStopEventListener = listener
    }

    fun unRegisterStopEventListener() {
        DebugUtil.i(TAG, "unRegisterStopEventListener")
        mStopEventListener = null
    }


    fun dispatchStopEvent(inputEvent: Long) {
        if (inputEvent in STOP_EVENTS) {
            mStopEventListener?.onStopEvent(inputEvent)
        } else {
            DebugUtil.e(TAG, "dispatchStopEvent inputEvent $inputEvent not supported")
        }
    }

    interface IStopEventListener {
        fun onStopEvent(intputEvent: Long)
    }
}