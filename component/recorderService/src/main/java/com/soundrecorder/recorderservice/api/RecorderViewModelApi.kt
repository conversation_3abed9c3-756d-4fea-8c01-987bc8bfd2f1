/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderViewModelApi
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.api

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager
import com.soundrecorder.recorderservice.manager.DiskStorageChecker
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import com.soundrecorder.recorderservice.manager.statusbar.RecordStatusBarUpdater
import org.json.JSONObject

@Component(RecorderViewModelAction.COMPONENT_NAME)
object RecorderViewModelApi {
    @Action(RecorderViewModelAction.START_RECORDER_SERVICE)
    @JvmStatic
    fun startRecorderService(function: Intent.() -> Unit) {
        RecorderViewModel.startRecorderService(function)
    }

    @Action(RecorderViewModelAction.GET_MARK_DATA)
    @JvmStatic
    fun getMarkData(): List<MarkDataBean> {
        return RecorderViewModel.getInstance().getMarkData()
    }

    @Action(RecorderViewModelAction.GET_LAST_MARK_TIME)
    @JvmStatic
    fun getLastMarkTime(): Long {
        return RecorderViewModel.getInstance().getLastMarkTime()
    }

    @Action(RecorderViewModelAction.GET_RECORD_TYPE)
    @JvmStatic
    fun getRecordType(): Int {
        return RecorderViewModel.getInstance().getRecordType()
    }

    @Action(RecorderViewModelAction.GET_AMPLITUDE_LIST)
    @JvmStatic
    fun getAmplitudeList(): List<Int> {
        return RecorderViewModel.getInstance().getAmplitudeList()
    }

    @Action(RecorderViewModelAction.GET_LATEST_AMPLITUDE)
    @JvmStatic
    fun getLatestAmplitude(): Int {
        return RecorderViewModel.getInstance().getLatestAmplitude()
    }

    @Action(RecorderViewModelAction.GET_MAX_AMPLITUDE)
    @JvmStatic
    fun getMaxAmplitude(): Int {
        return RecorderViewModel.getInstance().getMaxAmplitude()
    }

    @Action(RecorderViewModelAction.GET_MARK_ENABLED_LIVE_DATA)
    @JvmStatic
    fun getMarkEnabledLiveData(): LiveData<Boolean>? {
        return RecorderViewModel.getInstance().getMarkEnabledLiveData()
    }

    @Action(RecorderViewModelAction.IS_MARK_ENABLED_FULL)
    @JvmStatic
    fun isMarkEnabledFull(): Boolean {
        return RecorderViewModel.getInstance().isMarkEnabledFull()
    }

    @Action(RecorderViewModelAction.CHECK_MARK_DATA_MORE_THAN_MAX)
    @JvmStatic
    fun checkMarkDataMoreThanMax(): Boolean {
        return RecorderViewModel.getInstance().checkMarkDataMoreThanMax()
    }

    @Action(RecorderViewModelAction.IS_NEED_SHOW_NOTIFICATION_PERMISSION_DENIED_SNACKBAR)
    @JvmStatic
    fun isNeedShowNotificationPermissionDeniedSnackBar(): Boolean {
        return RecorderViewModel.getInstance().isNeedShowNotificationPermissionDeniedSnackBar()
    }

    @Action(RecorderViewModelAction.RESET_NEED_SHOW_NOTIFICATION_PERMISSION_DENIED_SNACKBAR)
    @JvmStatic
    fun resetNeedShowNotificationPermissionDeniedSnackBar() {
        return RecorderViewModel.getInstance().resetNeedShowNotificationPermissionDeniedSnackBar()
    }

    @Action(RecorderViewModelAction.START)
    @JvmStatic
    fun start() {
        RecorderViewModel.getInstance().start()
    }

    @Action(RecorderViewModelAction.RESUME)
    @JvmStatic
    fun resume() {
        RecorderViewModel.getInstance().resume()
    }

    @Action(RecorderViewModelAction.PAUSE)
    @JvmStatic
    fun pause() {
        RecorderViewModel.getInstance().pause()
    }

    @Action(RecorderViewModelAction.CANCEL)
    @JvmStatic
    fun cancel() {
        RecorderViewModel.getInstance().cancel()
    }

    @Action(RecorderViewModelAction.STOP)
    @JvmStatic
    fun stop(): String? {
        return RecorderViewModel.getInstance().stop()
    }

    @Action(RecorderViewModelAction.CANCEL_RECORD_NOTIFICATION)
    @JvmStatic
    fun cancelRecordNotification() {
        RecorderViewModel.getInstance().cancelRecordNotification()
    }

    @Action(RecorderViewModelAction.GET_SAMPLE_URI)
    @JvmStatic
    fun getSampleUri(): Uri? {
        return RecorderViewModel.getInstance().getSampleUri()
    }

    @JvmStatic
    fun getSampleUriStr(): String {
        return getSampleUri()?.toString() ?: ""
    }

    @Action(RecorderViewModelAction.GET_SUFFIX)
    @JvmStatic
    fun getSuffix(): String? {
        return RecorderViewModel.getInstance().getSuffix()
    }

    @Action(RecorderViewModelAction.GET_RECORD_FILE_PATH)
    @JvmStatic
    fun getRecordFilePath(): String? {
        return RecorderViewModel.getInstance().getRecordFilePath()
    }

    @Action(RecorderViewModelAction.GET_RELATIVE_PATH)
    @JvmStatic
    fun getRelativePath(): String? {
        return RecorderViewModel.getInstance().getRelativePath()
    }

    @Action(RecorderViewModelAction.GET_DEFAULT_DISPLAY_NAME)
    @JvmStatic
    fun getSampleDisplayName(genNewNameWhenSampleNull: Boolean = true): String {
        return RecorderViewModel.getInstance().getSampleDisplayName(genNewNameWhenSampleNull)
    }

    /**
     * 获取录制模式名称
     */
    @Action(RecorderViewModelAction.GET_RECORD_MODE_NAME)
    @JvmStatic
    fun getRecordModeName(): String {
        return RecorderViewModel.getInstance().getRecordModeName()
    }

    /**
     * 定向录音开关
     */
    @JvmStatic
    fun setDirectRecordSwitch(isOn: Boolean) {
        RecorderViewModel.getInstance().setDirectRecordingOn(isOn)
    }

    /**
     * 定向录音时间
     */
    @JvmStatic
    fun setDirectRecordTime(directTime: String) {
        RecorderViewModel.getInstance().setDirectRecordTime(directTime = directTime)
    }

    @JvmStatic
    fun getDirectRecordTime(): String? {
        return RecorderViewModel.getInstance().getDirectRecordTime()
    }

    /**
     * 定向录音开关是否可用
     */
    @JvmStatic
    fun getDirectRecordEnable(): Boolean {
        return RecorderViewModel.getInstance().isDirectRecodingEnable()
    }

    /**
     * 定向录音开关状态
     */
    @JvmStatic
    fun getDirectRecordOn(): Boolean {
        return RecorderViewModel.getInstance().isDirectRecodingOn()
    }

    @Action(RecorderViewModelAction.GET_FILE_BEING_RECORDED)
    @JvmStatic
    fun getFileBeingRecorded(): String? {
        return RecorderViewModel.getInstance().getFileBeingRecorded()
    }

    @Action(RecorderViewModelAction.IS_QUICK_RECORD)
    @JvmStatic
    fun isQuickRecord(): Boolean {
        return RecorderViewModel.getInstance().isQuickRecord()
    }

    @Action(RecorderViewModelAction.SAVE_RECORD_INFO)
    @JvmStatic
    fun saveRecordInfo(
        displayName: String? = null,
        originalDisplayName: String? = null,
        saveRecordFromWhere: Int,
        needStopService: Boolean = true
    ) {
        RecorderViewModel.getInstance()
            .saveRecordInfo(displayName, originalDisplayName, saveRecordFromWhere, needStopService)
    }

    @Action(RecorderViewModelAction.HAS_INIT_RECORDER_SERVICE)
    @JvmStatic
    fun hasInitRecorderService(): Boolean {
        return RecorderViewModel.getInstance().hasInitRecorderService()
    }

    @Action(RecorderViewModelAction.STOP_SERVICE)
    @JvmStatic
    fun stopService() {
        RecorderViewModel.getInstance().stopService()
    }

    @Action(RecorderViewModelAction.SWITCH_RECORDER_STATUS)
    @JvmStatic
    fun switchRecorderStatus(from: String) {
        RecorderViewModel.getInstance().switchRecorderStatus(from)
    }

    @Action(RecorderViewModelAction.ADD_MARK)
    @JvmStatic
    fun addMark(mark: MarkMetaData) {
        RecorderViewModel.getInstance().addMark(mark)
    }

    @Action(RecorderViewModelAction.ADD_MULTI_PICTURE_MARK)
    @JvmStatic
    fun addMultiPictureMark(marks: ArrayList<MarkMetaData>): Int {
        return RecorderViewModel.getInstance().addMultiPictureMark(marks)
    }

    @Action(RecorderViewModelAction.REMOVE_MARK)
    @JvmStatic
    fun removeMark(index: Int) {
        RecorderViewModel.getInstance().removeMark(index)
    }

    @Action(RecorderViewModelAction.RENAME_MARK)
    @JvmStatic
    fun renameMark(newText: String, index: Int): Boolean {
        return RecorderViewModel.getInstance().renameMark(newText, index)
    }

    @Action(RecorderViewModelAction.IS_FROM_SLID_BAR)
    @JvmStatic
    fun isFromSlidBar(): Boolean {
        return RecorderViewModel.getInstance().isFromSlidBar()
    }

    @Action(RecorderViewModelAction.IS_FROM_APP_CARD)
    @JvmStatic
    fun isFromAppCard(): Boolean {
        return RecorderViewModel.getInstance().isFromAppCard()
    }

    @Action(RecorderViewModelAction.IS_FROM_MINI_APP)
    @JvmStatic
    fun isFromMiniApp(): Boolean {
        return RecorderViewModel.getInstance().isFromMiniApp()
    }

    @Action(RecorderViewModelAction.IS_FROM_SMALL_CARD)
    @JvmStatic
    fun isFromSmallCard(): Boolean {
        return RecorderViewModel.getInstance().isFromSmallCard()
    }

    @Action(RecorderViewModelAction.IS_FROM_OTHER_APP)
    @JvmStatic
    fun isFromOtherApp(): Boolean {
        return RecorderViewModel.getInstance().isFromOtherApp()
    }

    @Action(RecorderViewModelAction.IS_FROM_BRENO)
    @JvmStatic
    fun isFromBreno(): Boolean {
        return RecorderViewModel.getInstance().isFromBreno()
    }

    @Action(RecorderViewModelAction.IS_FROM_CUBE_BUTTON_OR_LOCK_SCREEN)
    @JvmStatic
    fun isFromCubeButtonOrLockScreen(): Boolean {
        return RecorderViewModel.getInstance().isFromCubeButton() || RecorderViewModel.getInstance().isFromLockScreen()
    }

    @Action(RecorderViewModelAction.ADD_SOURCE_FOR_NOTIFICATION_BTN_DISABLED)
    @JvmStatic
    fun addSourceForNotificationBtnDisabled(addPictureMarking: MutableLiveData<Boolean>) {
        RecorderViewModel.getInstance().addSourceForNotificationBtnDisabled(addPictureMarking)
    }

    @Action(RecorderViewModelAction.ADD_LISTENER)
    @JvmStatic
    fun addListener(listener: RecorderControllerListener) {
        RecorderViewModel.getInstance().addListener(listener)
    }

    @Action(RecorderViewModelAction.REMOVE_LISTENER)
    @JvmStatic
    fun removeListener(listener: RecorderControllerListener) {
        RecorderViewModel.getInstance().removeListener(listener)
    }

    @Action(RecorderViewModelAction.SET_DO_MULTI_PICTURE_MARK_LOADING)
    @JvmStatic
    fun setDoMultiPictureMarkLoading(doMultiPictureMarkLoading: Boolean) {
        RecorderViewModel.getInstance().setDoMultiPictureMarkLoading(doMultiPictureMarkLoading)
    }

    @Action(RecorderViewModelAction.IS_START_SERVICE_FROM_OTHER_APP)
    @JvmStatic
    fun isStartServiceFromOtherApp(): Boolean {
        return RecorderViewModel.getInstance().isStartServiceFromOtherApp()
    }

    @Action(RecorderViewModelAction.GET_CURRENT_STATUS)
    @JvmStatic
    fun getCurrentStatus(): Int {
        return RecordStatusManager.getCurrentStatus()
    }

    @Action(RecorderViewModelAction.GET_RECORD_STATUS_BEFORE_SAVING)
    @JvmStatic
    fun getRecordStatusBeforeSaving(): Int? {
        return RecorderViewModel.getInstance().getRecordStatusBeforeSave()
    }

    @Action(RecorderViewModelAction.GET_LAST_STATUS)
    @JvmStatic
    fun getLastStatus(): Int {
        return RecordStatusManager.getLastStatus()
    }

    @Action(RecorderViewModelAction.IS_ALREADY_RECORDING)
    @JvmStatic
    fun isAlreadyRecording(): Boolean {
        return RecordStatusManager.isAlreadyRecording()
    }

    @Action(RecorderViewModelAction.IS_RECORD_SAVING)
    @JvmStatic
    fun isRecordSaving(): Boolean {
        return RecorderViewModel.getInstance().isRecordSaving()
    }

    @Action(RecorderViewModelAction.CHECK_MODE_CAN_RECORD)
    @JvmStatic
    fun checkModeCanRecord(needToast: Boolean): Boolean {
        return AudioModeChangeManager.checkModeCanRecord(needToast)
    }

    @Action(RecorderViewModelAction.IS_AUDIO_MODE_CHANGE_PAUSE)
    @JvmStatic
    fun isAudioModeChangePause(): Boolean {
        return AudioModeChangeManager.isAudioModeChangePause()
    }

    @Action(RecorderViewModelAction.IS_NEED_RESUME)
    @JvmStatic
    fun isNeedResume(): Boolean {
        return AudioModeChangeManager.isNeedResume()
    }

    @Action(RecorderViewModelAction.CHECK_DIST_BEFORE_START_RECORD)
    @JvmStatic
    fun checkDistBeforeStartRecord(): Boolean {
        return DiskStorageChecker.checkDistBeforeStartRecord()
    }

    @Action(RecorderViewModelAction.HAS_INIT_AMPLITUDE)
    @JvmStatic
    fun hasInitAmplitude(): Boolean {
        return RecorderViewModel.getInstance().hasInitAmplitude()
    }

    @Action(RecorderViewModelAction.GET_AMPLITUDE_CURRENT_TIME)
    @JvmStatic
    fun getAmplitudeCurrentTime(): Long {
        return RecorderViewModel.getInstance().getAmplitudeCurrentTime()
    }

    @Action(RecorderViewModelAction.RECORD_STATUS_BAR_FORCE_HIDE)
    @JvmStatic
    fun forceHideRecordStatusBar(from: String) {
        RecordStatusBarUpdater.forceDismiss(from)
    }

    @Action(RecorderViewModelAction.STATUS_BAR_SEEDLING_DATA)
    @JvmStatic
    fun getSeedlingData(): JSONObject? {
        return RecordStatusBarUpdater.getSeedlingData()
    }

    @JvmStatic
    @Action(RecorderViewModelAction.SHOW_OR_HIDE_STATUS_BAR)
    fun showOrHideStatusBar(from: String) {
        RecordStatusBarUpdater.showOrHide(from)
    }

    @JvmStatic
    @Action(RecorderViewModelAction.ON_SEEDLING_CARD_STATE_CHANGED)
    fun onSeedlingCardStateChanged(isShow: Boolean) {
        RecordStatusBarUpdater.onSeedlingCardStateChanged(isShow)
    }

    @JvmStatic
    @Action(RecorderViewModelAction.ON_FLUID_CARD_DISMISS)
    fun fluidCardDismiss(from: String) {
        RecordStatusBarUpdater.fluidCardDismiss(from)
    }

    @Action(RecorderViewModelAction.GET_SAVE_PROGRESS_VALUE)
    @JvmStatic
    fun getSaveProgressValue(): Int {
        return RecorderViewModel.getInstance().getSaveProgressValue()
    }

    @Action(RecorderViewModelAction.CHANGE_ASR_LANGUAGE)
    @JvmStatic
    fun changeAsrLanguage(language: String) {
        RecorderViewModel.getInstance().changeAsrLanguage(language)
    }

    @Action(RecorderViewModelAction.REGISTER_REALTIME_ASR_LISTENER)
    @JvmStatic
    fun registerRtAsrListener(listener: OnRealtimeListener) {
        RecorderViewModel.getInstance().registerRtAsrListener(listener)
    }

    @Action(RecorderViewModelAction.UNREGISTER_REALTIME_ASR_LISTENER)
    @JvmStatic
    fun unregisterRtAsrListener(listener: OnRealtimeListener) {
        RecorderViewModel.getInstance().unregisterRtAsrListener(listener)
    }

    @Action(RecorderViewModelAction.START_TRANSLATION_CONFIG)
    @JvmStatic
    fun startTranslationConfig() {
        RecorderViewModel.getInstance().startTranslationConfig()
    }

    @Action(RecorderViewModelAction.GET_ALL_ASR_CONTENT)
    @JvmStatic
    fun getAllAsrContent(): List<ConvertContentItem> {
        return RecorderViewModel.getInstance().getAllAsrContent()
    }

    @Action(RecorderViewModelAction.GET_REALTIME_ASR_STATUS)
    @JvmStatic
    fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        return RecorderViewModel.getInstance().getRealtimeAsrStatus()
    }

    @Action(RecorderViewModelAction.EXTERNAL_INIT_ASR)
    @JvmStatic
    fun externalInitAsr() {
        RecorderViewModel.getInstance().externalInitAsr()
    }

    @JvmStatic
    @Action(RecorderViewModelAction.GET_CUR_SELECTED_LANGUAGE)
    fun getCurSelectedLanguage(): String {
        return RecorderViewModel.getInstance().curSelectedLanguage
    }

    @JvmStatic
    @Action(RecorderViewModelAction.SET_CUR_SELECTED_LANGUAGE)
    fun setCurSelectedLanguage(language: String) {
        RecorderViewModel.getInstance().curSelectedLanguage = language
    }

    @Action(RecorderViewModelAction.GET_SUPPORT_LANGUAGE_LIST)
    @JvmStatic
    fun getSupportLanguageList(callback: (List<String>?) -> Unit) {
        RecorderViewModel.getInstance().getSupportLanguageList(callback)
    }
}