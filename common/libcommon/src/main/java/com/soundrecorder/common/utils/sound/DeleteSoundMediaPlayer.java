package com.soundrecorder.common.utils.sound;

import android.content.Context;
import android.database.Cursor;
import android.media.MediaPlayer;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;


import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.DebugUtil;

import static com.soundrecorder.common.utils.sound.DeleteSoundEffectManager.DELETE_SOUND_FILENAME;

public class DeleteSoundMediaPlayer implements IDeleteSoundPlayer {

    private static final String TAG = "DeleteSoundMediaPlayer";

    private MediaPlayer mediaPlayer;
    private Uri mDeleteSoundUri;
    private boolean mPrepared;
    private MediaPlayer.OnErrorListener mErrorListener = new MediaPlayer.OnErrorListener() {
        @Override
        public boolean onError(MediaPlayer mp, int what, int extra) {
            DebugUtil.i(TAG, "PlayError" );
            return false;
        }
    };
    private MediaPlayer.OnCompletionListener mCompleteListener = new MediaPlayer.OnCompletionListener() {
        @Override
        public void onCompletion(MediaPlayer mp) {
            DebugUtil.i(TAG, "onComplete" );
        }
    };
    private MediaPlayer.OnPreparedListener mPreparedListener = new MediaPlayer.OnPreparedListener() {
        @Override
        public void onPrepared(MediaPlayer mp) {
            DebugUtil.i(TAG, "onPrepared" );
            mPrepared = true;
        }
    };

    DeleteSoundMediaPlayer() {
        initData();
    }

    @Override
    public void initData() {
        DebugUtil.i(TAG, "initData");
        if (mediaPlayer == null) {
            mediaPlayer = new MediaPlayer();
        }
        if (mDeleteSoundUri == null) {
            mDeleteSoundUri = getSoundPath(BaseApplication.getAppContext(), DELETE_SOUND_FILENAME);
            DebugUtil.i(TAG, "soundUri: " + mDeleteSoundUri);
        }
        try {
            if (mDeleteSoundUri != null) {
                mediaPlayer.setDataSource(BaseApplication.getAppContext(), mDeleteSoundUri);
            }
            mediaPlayer.setOnCompletionListener(mCompleteListener);
            mediaPlayer.setOnErrorListener(mErrorListener);
            mediaPlayer.setOnPreparedListener(mPreparedListener);
            mediaPlayer.prepare();
            DebugUtil.i(TAG, "prepare");
            mPrepared = true;
        } catch (Exception e) {
            DebugUtil.e(TAG, "initData failed", e);
            mPrepared = false;
        }  finally {
            //do nothing.
        }
    }

    @Override
    public void playDeleteSound() {
        if (mPrepared && (mediaPlayer != null)) {
            mediaPlayer.start();
            DebugUtil.i(TAG, "playDeleteSound start");
        } else {
            DebugUtil.w(TAG, "playDeleteSound mPrepared : " + mPrepared + ", trig initData");
            initData();
        }
    }

    @Override
    public void release() {
        if (mediaPlayer != null) {
            mediaPlayer.release();
        }
        mediaPlayer = null;
        mDeleteSoundUri = null;
        mPrepared = false;
        DebugUtil.i(TAG, "release");
    }



    /**
     * @param context
     * @param oggName ogg name, like "global_delete.ogg"
     * @return
     */
    public Uri getSoundPath(Context context, String oggName) {
        if (TextUtils.isEmpty(oggName)) {
            return null;
        }
        Cursor cursor = null;
        try {
            String[] projection = new String[]{MediaStore.Audio.Media._ID};
            String selection = MediaStore.Audio.Media.DISPLAY_NAME + " like ?";
            String[] selectionArgs = new String[]{oggName};
            cursor = context.getContentResolver().query(MediaStore.Audio.Media.INTERNAL_CONTENT_URI, projection, selection, selectionArgs, null);
            if (cursor != null && cursor.moveToFirst()) {
                return Uri.withAppendedPath(MediaStore.Audio.Media.INTERNAL_CONTENT_URI,
                        String.valueOf(cursor.getInt(0)));
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getSoundPath failed ", e);
        } finally {
            if (null != cursor) {
                cursor.close();
            }
        }
        return null;
    }


}
