/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CallSummaryCheckManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/6/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.setting.callsummary

import android.content.Context
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.BaseUtil.isAndroidVOrLater
import com.soundrecorder.base.utils.BaseUtil.isLightOS
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.buryingpoint.BuryingPoint.addRecordSettingIconClick
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.PACKAGE_NAME_ASSISTANT
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.SUPPORT_AI_VOICESCRIBE
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.getJumpSettingIntentOfAssistant
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.getJumpSettingIntentOfThirdApp
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.isGlobalSummaryAvailable
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.isSupportGlobalSummary
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.isThirdRecordSummaryAvailable
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.isThirdRecordSupportSummary
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.isThirdRecordSwitchStateOn
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean.Companion.isThirdRecordSwitchStateOnByAssistant

object ThirdAppRecordCheckManager {

    private const val TAG = "ThirdAppRecordCheckManager"
    private const val AI_VOICE_META_DATA_VERSION = 1
    /**
     * 三方应用通话录音开关判断
     */
    @JvmStatic
    fun isSupportTripartiteAudioMonitor(): Boolean {
        //realme外销支持三方通话录音
        if (isSupportRmExportAudioMonitor()) {
            return true
        }

        if (BaseUtil.isEXP() || !OS12FeatureUtil.isColorOS14OrLater()) {
            return false
        }
        if (FeatureOption.isHasSupportThirdAppRecord()) {
            return true
        }
        //os15上，非轻量判断语音转文字（安裝了com.coloros.accessibilityassistant），轻量还是判断三方录音
        if (!BaseUtil.isLightOS() && OS12FeatureUtil.isColorOS15OrLater() &&
            AppUtil.isAppInstalled(ThirdRecordResultBean.PACKAGE_NAME_ASSISTANT)) {
            return true
        }
        //realme上OS13，oppo/一加发布范围只有OS14及之后
        return if (BaseUtil.isRealme()) {
            isSupportThirdForRealme()
        } else {
            isSupportThirdForOppo()
        }
    }

    private fun isSupportThirdForOppo(): Boolean {
        return AppUtil.isAppInstalled(ThirdRecordResultBean.PACKAGE_NAME_THIRD_APP_RECORD)
    }

    private fun isSupportThirdForRealme(): Boolean {
        if (OS12FeatureUtil.isColorOs13OrLater() &&
            AppUtil.isAppInstalled(ThirdRecordResultBean.PACKAGE_NAME_THIRD_APP_RECORD)) {
            return true
        }
        return false
    }


    /**
     * 判断realme外销机器是否带三方应用通话录音功能
     */
    fun isSupportRmExportAudioMonitor(): Boolean {
        return (BaseUtil.isEXP()
                && BaseUtil.isRealme()
                && OS12FeatureUtil.isColorOS15OrLater()
                && FeatureOption.isHasSupportThirdAppRecord()
                && AppUtil.isAppInstalled(ThirdRecordResultBean.PACKAGE_NAME_THIRD_APP_RECORD))
    }

    /**
     * 查询智慧语音按钮的状态
     */
    @JvmStatic
    fun queryThirdAppRecordMode(): ThirdRecordResultBean {
        val stateOn: Boolean
        val supportSummaryVersion: Int
        val summaryAvailable: Boolean
        //支持全局语音摘要，读取setting值
        if (isSupportGlobalSummary(BaseApplication.getAppContext())) {
            // 开关状态
            stateOn = isThirdRecordSwitchStateOnByAssistant()
            // 全局语音摘要支持摘要的版本
            supportSummaryVersion = ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_GLOBAL_SUMMARY
            // 全局语音摘要，功能可用（暂时同录音摘要能力保持一致）
            summaryAvailable = isGlobalSummaryAvailable()
        } else { //不支持全局语音摘要，读取老的三方通话录音
            stateOn = isThirdRecordSwitchStateOn()
            // 第三方通话录音支持摘要的版本
            supportSummaryVersion = if (isThirdRecordSupportSummary()) {
                ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_THIRD_SUMMARY
            } else {
                ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_RECORD
            }
            // 全局语音摘要，功能可用
            summaryAvailable = isThirdRecordSummaryAvailable()
        }

        return ThirdRecordResultBean(stateOn, supportSummaryVersion, summaryAvailable)
    }

    /**
     * 获取主标题的名字，即跳转后的app的名字
     * 历史上三方通话录音最开始叫三方通话录音；后来增加摘要后改为智慧语音；再后来功能挪到语音转文字后，语音转文字成了智慧语音，三方改为老名字三方通话录音
     */
    @JvmStatic
    fun getTitle(context: Context, resultBean: ThirdRecordResultBean): String {
        return when (resultBean.supportSummaryVersion) {
            ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_GLOBAL_SUMMARY -> getAssistAppName(context)
            ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_THIRD_SUMMARY -> {
                //15.0上轻量机器，语音转文字不支持，仍旧走三方通话
                if (isAndroidVOrLater && isLightOS()) {
                    context.getString(R.string.record_audio_for_third_app_v2)
                } else {
                    getAssistAppName(context)
                }
            }

            ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_RECORD -> context.getString(R.string.record_audio_for_third_app_v2)
            else -> context.getString(R.string.record_audio_for_third_app_v2)
        }
    }

    /**
     * 获取智慧语音app名称，OS15.0.1及以上智慧语音改为“AI语音摘记”
     */
    private fun getAssistAppName(context: Context): String {
        //智慧语音改名的metaData version >= 1，即为“AI语音摘记”
        val supportAiVoice = AppUtil.metaDataInt(PACKAGE_NAME_ASSISTANT, SUPPORT_AI_VOICESCRIBE)
        if (supportAiVoice >= AI_VOICE_META_DATA_VERSION) {
            val name = AppUtil.getAppName(PACKAGE_NAME_ASSISTANT)
            DebugUtil.d(TAG, "getAssistAppName, name:$name")
            if (!TextUtils.isEmpty(name)) {
                return name
            }
        }
        return context.getString(R.string.third_record_app_name_smart_voice)
    }

    /**
     * 获取副标题的名字，即是否支持摘要、全局摘要
     * 1）支持全局语音摘要，则词条为XXX等第三方通话和会议时进行摘要、录音等功能
     * 2）支持三方摘要，则词条为XXX等第三方通话时进行录音、摘要等功能
     * 3）不支持摘要，则词条为XXX等第三方通话时进行录音
     */
    @JvmStatic
    fun getContentRes(resultBean: ThirdRecordResultBean): Int {
        return if (resultBean.summaryAvailable) { //支持摘要
            when (resultBean.supportSummaryVersion) {
                ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_GLOBAL_SUMMARY ->
                    //全局语音摘要版本
                    R.string.settings_main_summary_V3

                ThirdRecordResultBean.SUPPORT_SUMMARY_VERSION_THIRD_SUMMARY ->
                    //非全局语音摘要版本，三方通话应用
                    R.string.settings_main_summary_V2

                else -> R.string.settings_main_summary_v1
            }
        } else if (isSupportRmExportAudioMonitor()) {
            R.string.settings_main_record_export
        } else { //只支持录音功能
            R.string.settings_main_summary_v1
        }
    }

    /**
     * 查询智慧语音开关的设置页
     */
    @JvmStatic
    fun jumpToThirdAppSetting(context: Context?) {
        kotlin.runCatching {
            val intent =
                if (isSupportGlobalSummary(BaseApplication.getAppContext())) {
                    //支持全局语音摘要，读取setting值
                    getJumpSettingIntentOfAssistant()
                } else {
                    getJumpSettingIntentOfThirdApp()
                }

            kotlin.runCatching {
                DebugUtil.d(TAG, "Jump to third")
                addRecordSettingIconClick(
                    RecorderUserAction.VALUE_NAME_SMART_RECORD,
                    RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT
                )
                FollowHandPanelUtils.startActivity(context!!, intent)
            }.onFailure {
                DebugUtil.e(TAG, "start third app record settings error: $it")
            }
        }
    }
}