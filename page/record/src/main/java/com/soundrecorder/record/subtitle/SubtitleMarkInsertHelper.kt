/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleMarkInsertHelper.kt
 * Description:
 *     The helper class for inserting marks into subtitle text.
 *
 * Version: 1.0
 * Date: 2025-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2025-05-29   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record.subtitle

import android.os.Handler
import android.os.Looper
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.record.subtitle.data.DisplayMark
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.math.max
import kotlin.math.min

/**
 * 此代码文件为AI生成产物，生成工具为Augment
 */
class SubtitleMarkInsertHelper {

    private companion object {
        private const val TAG = "SubtitleMarkInsertHelper"

        /**
         * 标记字符，用于插入到字幕文本当中
         */
        private const val ICON_MARK_FLAG = "\u2691"
        private const val FLOAT_ACCURACY = 0.00001f
    }

    /**
     * 上屏字幕条目列表
     * 使用线程安全的CopyOnWriteArrayList确保多线程访问安全
     */
    private val displaySubtitleEntries = CopyOnWriteArrayList<DisplaySubtitleEntry>()

    /**
     * 已有的标记信息列表
     * 使用线程安全的CopyOnWriteArrayList确保多线程访问安全
     */
    private val markDataBeans = CopyOnWriteArrayList<MarkDataBean>()

    /**
     * 用于异步执行的线程池
     */
    private val executorService: ExecutorService by lazy {
        Executors.newSingleThreadExecutor()
    }

    /**
     * 主线程Handler，用于回调
     */
    private val mainHandler by lazy { Handler(Looper.getMainLooper()) }

    /**
     * 获取上屏字幕条目列表
     *
     * @return 上屏字幕条目列表的只读副本
     */
    fun getDisplaySubtitleEntries(): List<DisplaySubtitleEntry> {
        return displaySubtitleEntries.toList()
    }

    /**
     * 获取标记数据列表
     *
     * @return 标记数据列表的只读副本
     */
    fun getMarkDataBeans(): List<MarkDataBean> {
        return markDataBeans.toList()
    }

    /**
     * 异步更新标记数据
     *
     * 在后台线程中执行updateMarkDataBeans，完成后在主线程回调IAfterUpdatedAction。
     *
     * @param newMarkDataBeans 新的标记数据列表
     * @param afterAction 更新完成后的回调
     */
    fun asyncUpdateMarkDataBeans(
        newMarkDataBeans: List<MarkDataBean>,
        afterAction: IAfterUpdatedAction
    ) {
        executorService.execute {
            runCatching {
                // 在工作线程中执行更新
                updateMarkDataBeans(newMarkDataBeans)

                // 获取更新后的字幕条目列表
                val updatedEntries = getDisplaySubtitleEntries()

                // 在主线程中回调
                mainHandler.post {
                    afterAction.onAfterUpdated(updatedEntries)
                }
            }.onFailure {
                // 异常处理：在主线程中回调空列表
                DebugUtil.e(TAG, "asyncUpdateMarkDataBeans error: ${it.message}")
            }
        }
    }

    /**
     * 更新整个MarkDataBean列表数据
     *
     * 更新标记数据列表，并重新分配所有标记到对应的字幕条目中。
     * 确保displaySubtitleEntries中的每个标记都能与MarkDataBean列表中的项目一一对应。
     *
     * @param newMarkDataBeans 新的标记数据列表
     */
    @WorkerThread
    fun updateMarkDataBeans(newMarkDataBeans: List<MarkDataBean>) {
        // 更新标记数据
        markDataBeans.clear()
        markDataBeans.addAll(newMarkDataBeans)

        // redistributeAllMarks方法会处理标记的清空和重新分配
        redistributeAllMarks()
    }

    /**
     * 异步更新字幕内容
     *
     * 在后台线程中执行updateSubtitleContent，完成后在主线程回调IAfterUpdatedAction。
     *
     * @param completedSubtitles 已经通过ASR识别完成的字幕列表（已按时间戳排序）
     * @param recognizingSubtitles ASR正在识别过程中逐字输出的字幕列表（通常至多一个）
     * @param afterAction 更新完成后的回调
     */
    fun asyncUpdateSubtitleContent(
        completedSubtitles: List<ConvertContentItem>,
        recognizingSubtitles: List<ConvertContentItem>,
        afterAction: IAfterUpdatedAction
    ) {
        executorService.execute {
            runCatching {
                // 在工作线程中执行更新
                updateSubtitleContent(completedSubtitles, recognizingSubtitles)

                // 获取更新后的字幕条目列表
                val updatedEntries = getDisplaySubtitleEntries()

                // 在主线程中回调
                mainHandler.post {
                    afterAction.onAfterUpdated(updatedEntries)
                }
            }.onFailure {
                DebugUtil.e(TAG, "asyncUpdateSubtitleContent error: ${it.message}")
            }
        }
    }

    /**
     * 更新字幕内容
     *
     * 根据ASR识别的两个阶段更新字幕内容：已完成识别的字幕和正在识别中的字幕。
     * 更新后会重新分配所有标记到对应的字幕条目中。
     *
     * @param completedSubtitles 已经通过ASR识别完成的字幕列表（已按时间戳排序）
     * @param recognizingSubtitles ASR正在识别过程中逐字输出的字幕列表（通常至多一个）
     */
    @WorkerThread
    fun updateSubtitleContent(
        completedSubtitles: List<ConvertContentItem>,
        recognizingSubtitles: List<ConvertContentItem>
    ) {
        // 合并两个列表，先处理已完成的字幕，再处理正在识别的字幕
        val allSubtitles = completedSubtitles + recognizingSubtitles

        // 更新字幕条目列表
        allSubtitles.forEach { subtitle ->
            val isFromCompleted = subtitle in completedSubtitles
            addOrUpdateDisplaySubtitleEntry(subtitle, isFromCompleted)
        }

        // 移除不再存在的字幕条目（如果某些字幕被删除了）
        val currentStartTimes = allSubtitles.map { it.startTime }.toSet()
        val entriesToKeep = displaySubtitleEntries.filter { entry ->
            entry.originContent.startTime in currentStartTimes
        }
        displaySubtitleEntries.clear()
        displaySubtitleEntries.addAll(entriesToKeep)

        // 重新分配所有标记到对应的字幕条目中
        redistributeAllMarks()
    }

    /**
     * 添加或更新字幕条目
     *
     * 如果传入的ConvertContentItem的startTime与已有条目相同，则更新已有条目的originContent；
     * 否则创建新的DisplaySubtitleEntry并添加到列表中。
     *
     * @param convertContentItem 要添加的字幕内容项
     * @param isFromCompletedSubtitles 是否来自已完成的字幕列表
     */
    private fun addOrUpdateDisplaySubtitleEntry(
        convertContentItem: ConvertContentItem,
        isFromCompletedSubtitles: Boolean
    ) {
        // 查找是否已存在相同startTime的条目
        val existingEntry = displaySubtitleEntries.find {
            it.originContent.startTime == convertContentItem.startTime
        }

        if (existingEntry != null) {
            // 检查内容是否有实际变化
            val contentChanged = existingEntry.originContent.textContent != convertContentItem.textContent ||
                    existingEntry.originContent.endTime != convertContentItem.endTime ||
                    existingEntry.isFromCompletedSubtitles != isFromCompletedSubtitles

            // 更新已有条目的originContent和来源标志
            existingEntry.originContent = convertContentItem
            existingEntry.isFromCompletedSubtitles = isFromCompletedSubtitles

            // 只有在内容有变化时才清空标记和重置displayContent
            if (contentChanged) {
                existingEntry.insertedMarks.clear()
                existingEntry.displayContent = convertContentItem.textContent
            }
        } else {
            // 创建新的DisplaySubtitleEntry
            val newEntry = DisplaySubtitleEntry(
                originContent = convertContentItem,
                displayContent = convertContentItem.textContent,
                insertedMarks = mutableListOf(),
                isFromCompletedSubtitles = isFromCompletedSubtitles
            )
            displaySubtitleEntries.add(newEntry)

            // 按startTime排序保持列表有序，CopyOnWriteArrayList不支持直接sortBy，需要重新创建排序后的列表
            val sortedEntries = displaySubtitleEntries.sortedBy { it.originContent.startTime }
            displaySubtitleEntries.clear()
            displaySubtitleEntries.addAll(sortedEntries)
        }
    }

    /**
     * 查找标记应该插入的目标字幕条目
     *
     * @param markTimeOffset 标记时间点
     * @return 目标字幕条目，如果找不到则返回null
     */
    private fun findTargetEntryForMark(markTimeOffset: Long): DisplaySubtitleEntry? {
        if (displaySubtitleEntries.isEmpty()) {
            return null
        }

        // 1. 如果标记时间点在第一个字幕之前，分配给第一个字幕
        val firstEntry = displaySubtitleEntries.first()
        if (markTimeOffset < firstEntry.originContent.startTime) {
            return firstEntry
        }

        // 2. 遍历所有字幕条目，找到合适的分配目标
        for (i in displaySubtitleEntries.indices) {
            val entry = displaySubtitleEntries[i]

            // 如果标记在当前字幕的时间范围内，直接分配给该字幕
            if (markTimeOffset >= entry.originContent.startTime &&
                markTimeOffset <= entry.originContent.endTime) {
                return entry
            }

            // 如果标记在当前字幕之后，检查是否在下一个字幕之前
            if (markTimeOffset > entry.originContent.endTime) {
                val nextEntry = displaySubtitleEntries.getOrNull(i + 1)
                if (nextEntry == null || markTimeOffset < nextEntry.originContent.startTime) {
                    // 标记在当前字幕之后且在下一个字幕之前（或没有下一个字幕），分配给当前字幕
                    return entry
                }
            }
        }

        // 3. 如果没有找到合适的条目，分配给最后一个条目
        return displaySubtitleEntries.lastOrNull()
    }

    /**
     * 计算标记在指定字幕条目中的插入位置
     *
     * @param entry 目标字幕条目
     * @param markTimeOffset 标记时间点
     * @return 插入位置
     */
    private fun calculateInsertPositionForEntry(
        entry: DisplaySubtitleEntry,
        markTimeOffset: Long
    ): Int {
        val originContent = entry.originContent

        // 如果标记时间点在第一个ConvertContentItem的startTime之前
        if (markTimeOffset < originContent.startTime) {
            return 0
        }

        // 如果标记时间点在ConvertContentItem的endTime之后
        if (markTimeOffset > originContent.endTime) {
            return originContent.textContent.length
        }

        // 使用已有的计算方法
        return calculateMarkFlagInsertPosition(
            originContent.textContent,
            originContent.startTime,
            originContent.endTime,
            markTimeOffset
        )
    }

    /**
     * 刷新字幕条目的displayContent
     *
     * 根据insertedMarks中的标记，将标记字符插入到textContent的对应位置，
     * 生成新的displayContent。
     *
     * @param entry 要刷新的字幕条目
     */
    private fun refreshDisplayContent(entry: DisplaySubtitleEntry) {
        val originalText = entry.originContent.textContent
        val marks = entry.insertedMarks.sortedBy { it.insertPosition }

        if (marks.isEmpty()) {
            entry.displayContent = originalText
            return
        }

        val result = StringBuilder()
        var currentPosition = 0

        // 按插入位置分组，处理相同位置的多个标记
        val marksByPosition = marks.groupBy { it.insertPosition }

        for (position in marksByPosition.keys.sorted()) {
            // 添加当前位置之前的文本
            if (position > currentPosition) {
                result.append(originalText.substring(currentPosition, position))
            }

            // 添加该位置的所有标记字符
            val marksAtPosition = marksByPosition[position] ?: emptyList()
            repeat(marksAtPosition.size) {
                result.append(ICON_MARK_FLAG)
            }

            currentPosition = position
        }

        // 添加剩余的文本
        if (currentPosition < originalText.length) {
            result.append(originalText.substring(currentPosition))
        }

        entry.displayContent = result.toString()
    }

    /**
     * 重新分配所有标记到对应的字幕条目中
     *
     * 优化：只对需要重新分配的标记进行处理，减少不必要的操作
     * 进一步优化：只有当originContent或insertedMarks有变化时才重新计算和刷新
     */
    private fun redistributeAllMarks() {
        if (displaySubtitleEntries.isEmpty()) {
            return
        }

        val modifiedEntries = mutableSetOf<DisplaySubtitleEntry>()

        // 如果没有标记数据，清空所有标记并刷新displayContent
        if (markDataBeans.isEmpty()) {
            displaySubtitleEntries.forEach { entry ->
                if (entry.insertedMarks.isNotEmpty()) {
                    entry.insertedMarks.clear()
                    modifiedEntries.add(entry)
                }
            }
        } else {
            // 保存每个条目的原始标记状态，用于变化检测
            val originalMarksMap = mutableMapOf<DisplaySubtitleEntry, List<DisplayMark>>()
            displaySubtitleEntries.forEach { entry ->
                originalMarksMap[entry] = entry.insertedMarks.toList() // 创建副本
                entry.insertedMarks.clear()
            }

            // 遍历所有标记数据，重新分配到合适的字幕条目中
            markDataBeans.forEach { markDataBean ->
                val markTimeOffset = markDataBean.timeInMills
                val targetEntry = findTargetEntryForMark(markTimeOffset)

                if (targetEntry != null) {
                    // 检查是否已存在相同时间点的标记（避免重复）
                    val existingMark = targetEntry.insertedMarks.find {
                        it.timeOffset == markTimeOffset
                    }

                    if (existingMark == null) {
                        val insertPosition = calculateInsertPositionForEntry(targetEntry, markTimeOffset)
                        val newMark = DisplayMark(markTimeOffset, insertPosition)
                        targetEntry.insertedMarks.add(newMark)
                    }
                }
            }

            // 检测变化并只标记有变化的条目
            displaySubtitleEntries.forEach { entry ->
                val originalMarks = originalMarksMap[entry] ?: emptyList()
                val currentMarks = entry.insertedMarks.sortedBy { it.timeOffset }
                val originalMarksSorted = originalMarks.sortedBy { it.timeOffset }

                // 检查标记是否有变化（数量、时间点或插入位置）
                val hasChanged = originalMarksSorted.size != currentMarks.size ||
                        originalMarksSorted.zip(currentMarks).any { (original, current) ->
                            original.timeOffset != current.timeOffset ||
                            original.insertPosition != current.insertPosition
                        }

                if (hasChanged) {
                    modifiedEntries.add(entry)
                }
            }
        }

        // 只刷新被修改的条目的displayContent
        modifiedEntries.forEach { entry ->
            refreshDisplayContent(entry)
        }
    }

    /**
     * 计算标记插入位置
     *
     * 根据字幕内容、字幕的开始和结束时间偏移量以及标记的时间偏移量，计算标记在字幕中的插入位置。
     *
     * @param asrContent 字幕内容
     * @param asrStartTimeOffset 字幕的开始时间偏移量（毫秒）
     * @param asrEndTimeOffset 字幕的结束时间偏移量（毫秒）
     * @param markTimeOffset 标记的时间偏移量（毫秒）
     * @return 标记在字幕中的插入位置（字符索引），如果无法计算返回-1
     */
    fun calculateMarkFlagInsertPosition(
        asrContent: String,
        asrStartTimeOffset: Long,
        asrEndTimeOffset: Long,
        markTimeOffset: Long
    ): Int {
        if (asrEndTimeOffset <= asrStartTimeOffset) {
            return -1
        }
        if (markTimeOffset <= asrStartTimeOffset) {
            return 0
        }
        if (markTimeOffset >= asrEndTimeOffset) {
            return asrContent.length
        }
        val asrDuration = (asrEndTimeOffset - asrStartTimeOffset).toFloat()
        val markTimePos = (markTimeOffset - asrStartTimeOffset).toFloat()

        val pos = (markTimePos * asrContent.length / asrDuration + FLOAT_ACCURACY).toInt()
        return max(0, min(pos, asrContent.length))
    }

    /**
     * 释放资源
     *
     * 停止异步工作线程，释放相关资源。
     * 应在Activity销毁时调用此方法。
     */
    fun release() {
        runCatching {
            executorService.shutdown()
        }.onFailure {
            DebugUtil.e(TAG, "release error: ${it.message}")
        }
    }

    fun interface IAfterUpdatedAction {
        @MainThread
        fun onAfterUpdated(subtitles: List<DisplaySubtitleEntry>)
    }
}