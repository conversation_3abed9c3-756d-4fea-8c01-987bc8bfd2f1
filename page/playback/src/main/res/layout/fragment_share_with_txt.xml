<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_share_txt_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/color_load_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:visibility="gone">

        <com.coui.appcompat.progressbar.COUILoadingView
            style="?attr/couiLoadingViewLargeStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/loadingTip"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="10dp" />

        <TextView
            android:id="@+id/loadingTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/oplus_loading_dialog_text_view" />
    </LinearLayout>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:paddingTop="@dimen/dp42"
        app:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/recorder_toolbar_height"
            app:navigationIcon="@drawable/ic_home_back_arrow" />

    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:id="@+id/body"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout">

        <androidx.recyclerview.widget.COUIRecyclerView
            android:id="@+id/rv_share_txt_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/navi_menu_tool_share_txt"
            android:layout_alignParentTop="true"
            android:orientation="vertical"
            android:scrollbars="vertical"
            android:visibility="gone"
            android:paddingStart="@dimen/responsive_ui_margin_large"
            android:paddingEnd="@dimen/responsive_ui_margin_large"
            tools:listitem="@layout/item_share_convert_content_txt" />

        <ScrollView
            android:id="@+id/sv_share_txt_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/navi_menu_tool_share_txt"
            android:layout_alignParentTop="true"
            android:paddingStart="@dimen/responsive_ui_margin_large"
            android:paddingEnd="@dimen/responsive_ui_margin_large"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_share_txt_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp24"
                    android:lineSpacingExtra="3sp"
                    android:textColor="@color/black_color"
                    android:textSize="@dimen/sp14"
                    tools:text="@string/export_save_file_name" />
                <!--    android:transformPivotY="-1.56dp"-->

                <TextView
                    android:id="@+id/tv_share_txt_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp24"
                    android:lineSpacingExtra="3sp"
                    android:textColor="@color/black_color"
                    android:textSize="@dimen/sp14"
                    tools:text="@string/time" />

                <TextView
                    android:id="@+id/tv_share_txt_subject"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="3sp"
                    android:textColor="@color/black_color"
                    android:textSize="@dimen/sp14"
                    tools:text="@string/export_content_person" />

                <TextView
                    android:id="@+id/tv_share_txt_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp28"
                    android:lineSpacingExtra="3sp"
                    android:textColor="@color/black_color"
                    android:textSize="@dimen/sp14"
                    tools:text="@string/app_name" />
            </LinearLayout>
        </ScrollView>

        <com.coui.appcompat.bottomnavigation.COUINavigationView
            android:id="@+id/navi_menu_tool_share_txt"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp56"
            android:layout_alignParentBottom="true"
            app:couiNaviMenu="@menu/bottom_navigation_menu_share_txt"
            app:couiToolNavigationViewBg="@color/share_txt_navigation_color"/>

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>