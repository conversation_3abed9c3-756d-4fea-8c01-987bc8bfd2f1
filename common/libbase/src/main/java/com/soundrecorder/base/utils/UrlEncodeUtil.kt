/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  UrlEncodeUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */

package com.soundrecorder.base.utils

import android.os.Build
import java.net.URLDecoder
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

@Suppress("DEPRECATION", "TooGenericExceptionCaught")
object UrlEncodeUtil {
    private const val TAG = "UrlEncodeUtil"

    @JvmStatic
    fun encode(content: String?): String {
        return if (content.isNullOrEmpty()) {
            ""
        } else {
            try {
                val str = String(content.toByteArray(), StandardCharsets.UTF_8)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    URLEncoder.encode(str, StandardCharsets.UTF_8)
                } else {
                    URLEncoder.encode(str)
                }
            } catch (e: Exception) {
                DebugUtil.w(TAG, "encode error: $e")
                ""
            }
        }
    }

    @JvmStatic
    fun decode(content: String?): String {
        return if (content.isNullOrEmpty()) {
            ""
        } else {
            try {
                val str = String(content.toByteArray(), StandardCharsets.UTF_8)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    URLDecoder.decode(str, StandardCharsets.UTF_8)
                } else {
                    URLDecoder.decode(str)
                }
            } catch (e: Exception) {
                DebugUtil.w(TAG, "encode error: $e")
                ""
            }
        }
    }
}