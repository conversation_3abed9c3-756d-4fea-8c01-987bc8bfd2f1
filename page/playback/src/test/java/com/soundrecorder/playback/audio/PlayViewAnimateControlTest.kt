/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayViewAnimateControlTest
 * Description:
 * Version: 1.0
 * Date: 2023/8/2
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/2 1.0 create
 */

package com.soundrecorder.playback.audio

import android.animation.AnimatorSet
import android.os.Build
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.view.marginEnd
import androidx.core.view.updateLayoutParams
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding
import com.soundrecorder.playback.databinding.IncludeAudioFragmentBottomButtonBinding
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class PlayViewAnimateControlTest {
    private var activityController: ActivityController<PlaybackActivity>? = null
    private var activity: PlaybackActivity? = null
    private var animateControl: PlayViewAnimateControl? = null
    private var layoutBinding: FragmentPlaybackAudioBinding? = null
    private var bottomButtonBinding: IncludeAudioFragmentBottomButtonBinding? = null

    @Before
    fun setUp() {
        activityController =
            Robolectric.buildActivity(PlaybackActivity::class.java).create().start().resume()
        activity = activityController?.get()
        layoutBinding = FragmentPlaybackAudioBinding.bind(
            LayoutInflater.from(activity).inflate(R.layout.fragment_playback_audio, null, false)
        ).apply {
            bottomButtonBinding = IncludeAudioFragmentBottomButtonBinding.bind(root)
        }
        animateControl = PlayViewAnimateControl(activity)
    }

    @After
    fun tearDown() {
        animateControl = null
        activityController?.stop()
        activity = null
        activityController = null
    }

    @Test
    fun should_notNull_when_fragmentCreate() {
        Assert.assertNotNull(animateControl)
        Assert.assertNotNull(layoutBinding)
        Assert.assertNotNull(bottomButtonBinding)
    }

    @Test
    fun should_correct_when_getWavePercent() {
        var percent = Whitebox.invokeMethod<Float>(animateControl, "getWavePercent", true)
        Assert.assertEquals(
            Whitebox.getInternalState(animateControl, "wavePercentShowMark"),
            percent
        )

        percent = Whitebox.invokeMethod(animateControl, "getWavePercent", false)
        Assert.assertEquals(
            Whitebox.getInternalState(animateControl, "wavePercentDefault"),
            percent
        )
    }

    @Test
    fun should_correct_when_correctBottomButtonOffset() {
        val binding = bottomButtonBinding ?: return
        // transferWidth < minWidth
        binding.viewTransferText.updateLayoutParams<FrameLayout.LayoutParams> { width = 1 }
        binding.layoutMarkPhoto.minWidth = 999
        Whitebox.invokeMethod<Unit>(animateControl, "correctBottomButtonOffset",
                binding.viewTransferText, binding.tvMark, binding.layoutMarkPhoto.minWidth)
        Assert.assertTrue(binding.tvMark.marginEnd > 0)
    }

    @Test
    fun should_null_when_release() {
        Whitebox.setInternalState(animateControl, "showMarkAnimation", AnimatorSet())
        Whitebox.setInternalState(animateControl, "hideMarkAnimation", AnimatorSet())

        animateControl?.release()
        Assert.assertNull(Whitebox.getInternalState<AnimatorSet>(animateControl, "showMarkAnimation"))
        Assert.assertNull(Whitebox.getInternalState<AnimatorSet>(animateControl, "hideMarkAnimation"))
    }
}