/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IFunctionPrivacyCallback.kt
 ** Description : Function Privacy Authorization Callback Interface
 ** Version     : 1.0
 ** Date        : 2025/06/04
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/06/04     1.0      create
 ***********************************************************************/
package com.soundrecorder.modulerouter.privacyPolicy

/**
 * 功能隐私授权回调接口
 * 用于处理用户对功能隐私协议的授权结果
 */
interface IFunctionPrivacyCallback {
    /**
     * 用户同意隐私协议时的回调
     * 注意：此时权限已经被设置，可以直接执行后续业务逻辑
     */
    fun onPrivacyAgreed()

    /**
     * 用户拒绝隐私协议时的回调
     */
    fun onPrivacyRejected()
}
