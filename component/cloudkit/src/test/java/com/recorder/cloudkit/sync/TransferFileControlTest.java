package com.recorder.cloudkit.sync;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.heytap.cloudkit.libsync.ext.CloudSyncManager;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.sync.bean.RecordTransferFile;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class TransferFileControlTest {
    private MockedStatic<CloudSyncManager> mManagerMockedStatic;

    @Before
    public void setUp() {
        mManagerMockedStatic = Mockito.mockStatic(CloudSyncManager.class);
        mManagerMockedStatic.when(() -> CloudSyncManager.getInstance()).thenReturn(Mockito.mock(CloudSyncManager.class));
    }

    @After
    public void release() {
        if (mManagerMockedStatic != null) {
            mManagerMockedStatic.close();
        }
        mManagerMockedStatic = null;
    }

    @Test
    public void should_when_uploadFileList() {
        TransferFileControl.TransferFilesCallBack mockCallBack = Mockito.mock(TransferFileControl.TransferFilesCallBack.class);

        TransferFileControl.INSTANCE.uploadFileList(Collections.emptyList(), mockCallBack);
        Mockito.verify(mockCallBack, Mockito.times(1)).batchResult(Collections.emptyList(), Collections.emptyList());

        List<RecordTransferFile> uploadList = new ArrayList<>();
        RecordTransferFile recordTransferFile = Mockito.mock(RecordTransferFile.class);
        uploadList.add(recordTransferFile);
        TransferFileControl.INSTANCE.uploadFileList(uploadList, mockCallBack);
        Mockito.verify(mockCallBack, Mockito.times(1)).onTransferStart();

    }
}
