/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  AIUnitDialog
 * * Description: AIUnitDialog
 * * Version: 1.0
 * * Date : 2025/3/25
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/3/25   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.utils.ViewUtils.updateWindowLayoutParams

class AIUnitDialog(private val mContext: Context) {

    companion object {
        const val SMART_NAME_SWITCH_OPEN = 1
        const val SMART_NAME_SWITCH_CLOSE = 0
        const val SMART_NAME_SWITCH_INVALID = -1
        private const val TAG = "AIUnitDialogUtils"

        private const val MESSAGE_FRESH = 2
        private const val MAX_PROGRESS = 100
        private const val MESSAGE_DELAY = 10L
    }

    /*智能命名dialog*/
    private var mSmartNameGuideDialog: AlertDialog? = null

    fun showSmartNameGuideDialog(callback: ((Boolean) -> Unit)? = null) {
        if (mSmartNameGuideDialog?.isShowing == true) {
            DebugUtil.d(TAG, "showSmartNameGuideDialog isShowing")
            return
        }
        val aiTitleView: View =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_ai_title_guide_layout, null)
        mSmartNameGuideDialog = COUIAlertDialogBuilder(mContext)
            .setBlurBackgroundDrawable(true)
            .setPositiveButton(
                R.string.permission_open_dialog,
                { dialog, which ->
                    DebugUtil.d(TAG, "showSmartNameGuideDialog, which:$which")
                    PrefUtil.putBoolean(mContext, PrefUtil.KEY_SMART_NAME_GUIDE_SHOW, true)
                    //showAiunitUserNoticeDialog()
                    callback?.invoke(true)
                    dialog.dismiss()
                }, true
            )
            .setNegativeButton(
                com.soundrecorder.common.R.string.not_open,
                { dialog, which ->
                    PrefUtil.putBoolean(mContext, PrefUtil.KEY_SMART_NAME_GUIDE_SHOW, true)
                    callback?.invoke(false)
                    dialog.dismiss()
                }, false
            )
            .setView(aiTitleView)
            .setCancelable(true)
            .show()
        updateWindowLayoutParams(mSmartNameGuideDialog?.window)
    }

    fun dismissDialog() {
        dismissSmartNameGuideDialog()
    }

    private fun dismissSmartNameGuideDialog() {
        if (mSmartNameGuideDialog?.isShowing == true) {
            mSmartNameGuideDialog?.dismiss()
        }
        mSmartNameGuideDialog = null
    }
}