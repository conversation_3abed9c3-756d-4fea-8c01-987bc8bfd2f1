package com.soundrecorder.common.fileoperator.rename;

import static com.soundrecorder.base.utils.NumberConstant.NUM_4;
import static com.soundrecorder.modulerouter.SeedlingAction.sendRecordAddEvent;
import static com.soundrecorder.modulerouter.SeedlingAction.sendRecordInnerRenameEvent;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.RecoverableSecurityException;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.IntentSender;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.provider.MediaStore;

import androidx.annotation.RequiresApi;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.R;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.fileoperator.CheckOperatorWithPermission;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.common.utils.ConvertDbUtil;
import com.soundrecorder.common.utils.calling.ContactCallRecordUtil;
import com.soundrecorder.modulerouter.BrowseFileAction;

import java.io.File;

public class NameFileDialogUtil {
    public static final int FAIL_WITH_SECURITY_EXCEPTION = -2;
    public static final int DIALOG_TYPE_RENAME = 1;
    public static final int DIALOG_TYPE_RECORD = 2;
    public static final int DIALOG_TYPE_CUT = 3;
    public static final int DIALOG_TYPE_MARK = 4;

    private final String TAG = "NameFileDialogUtil";
    private CheckOperatorWithPermission mOperatePermission = null;
    private int requestCode = Constants.REQUEST_CODE_RENAME;

    public NameFileDialogUtil(int requestCode) {
        this.requestCode = requestCode;
    }

    public int onPositive(Activity activity, int dialogType, String resultName, Record mediaRecord) {
        int result = -1;
        switch (dialogType) {
            case DIALOG_TYPE_RENAME:
                result = renamePositive(activity, mediaRecord, resultName);
                if (result == -1) { // 重命名成功，需要更新录音摘要出卡意图。
                    sendRecordInnerRenameEvent(mediaRecord.getId());
                }
                break;
            case DIALOG_TYPE_RECORD:
                break;
            case DIALOG_TYPE_CUT:
                result = clipPositive(activity, mediaRecord, resultName);
                if (result == -1) { // 裁剪成功，需要更新录音摘要出卡意图。
                    sendRecordAddEvent();
                }
                break;
            case DIALOG_TYPE_MARK:
                break;
            default:
                break;
        }
        return result;
    }

    //to get rename result
    @SuppressLint("NewApi")
    private int renamePositive(Activity activity, Record mediaRecord, String resultName) {
        if (resultName.trim().length() == 0) {
            return R.string.error_none_filename;
        }
        if (resultName.trim().startsWith(".")) {
            return R.string.notify_illegal_emoji_new;
        }
        if (BaseUtil.isAndroidQOrLater()) {
            String relativePath = mediaRecord.getRelativePath();
            String displayName = mediaRecord.getDisplayName();
            String suffix = displayName.substring(displayName.lastIndexOf("."));
            long rowId = mediaRecord.getId();
            Uri mediaUri = MediaDBUtils.genUri(rowId);
            boolean newFileAlreadyExist = FileUtils.isFileExist(relativePath, resultName + suffix);
            boolean oldFileExist = FileUtils.isFileExist(mediaUri);
            DebugUtil.i(TAG, "rename relativePath: " + relativePath + ", newName: " + resultName + ", suffix: " + suffix
                    + ", oldName: " + displayName + ", newFileAlreadyExist: " + newFileAlreadyExist + ", oldFileExist: " + oldFileExist);
            if (!oldFileExist) {
                return R.string.record_file_not_exist;
            }
            Record mediaRecordReal = MediaDBUtils.getRecordFromMediaByUriId(mediaUri);
            if (mediaRecordReal != null) {
                // 重新查一遍媒体库音频名称，有可能从录音多入口重命名了该文件
                displayName = mediaRecordReal.getDisplayName();
            }
            if (displayName.equals(resultName + suffix)) {
                // 文件名称没改变，点击保存，弹窗消失
                return -1;
            }
            if (newFileAlreadyExist) {
                return R.string.error_title;
            }
            boolean renameSuc = rename(activity,
                    mediaUri,
                    resultName,
                    suffix,
                    mediaRecord.getMimeType(),
                    true);
            boolean updateDbSuc = false;
            if (renameSuc) {
                String newName = resultName + suffix;
                String originalData = mediaRecord.getData();
                String newPath = originalData;
                int lastIndexOfFileDiscriptor = originalData.lastIndexOf(File.separator);
                if (lastIndexOfFileDiscriptor > 0) {
                    newPath = originalData.substring(0, lastIndexOfFileDiscriptor) + File.separator + newName;
                }
                ContactCallRecordUtil.updatePathIfCallingRecord(
                        BaseApplication.getAppContext(), mediaRecord, newPath);
                updateDbSuc = CloudSyncRecorderDbUtil.updateDisplayName(displayName, newName, relativePath, true, mediaRecord.getOriginalName());
                if (updateDbSuc) {
                    ConvertRecord convertRecord = ConvertDbUtil.selectByRecordId(mediaRecord.getId());

                    boolean convertIsEmpty = (convertRecord == null);
                    if (!convertIsEmpty) {
                        convertRecord.setMediaPath(newPath);
                        ConvertDbUtil.update(convertRecord);
                    }
                    BrowseFileAction.onFileUpdateSuccess(rowId, newPath);
                } else {
                    // 本地数据库无该条记录，插入数据库。以保存原始origin name。
                    RecorderDBUtil.insertCallRecordingsIfNeed(originalData, mediaRecordReal, displayName, newName, newPath);
//                    if (insertSuccess) {
//                        CloudSyncAction.trigBackupNow(activity.getApplicationContext());
//                    }
                    DebugUtil.i(TAG, " renameSuc ,but db not exist,insert db ");
                }
                DebugUtil.i(TAG, "renameSuc " + renameSuc + ", rowId: " + rowId + ", originalName : " + displayName + ", newName " + resultName + ", updateDbSuc: " + updateDbSuc);
            } else {
                DebugUtil.i(TAG, "renameSuc " + renameSuc + ", rename failed ");
                return FAIL_WITH_SECURITY_EXCEPTION;
            }
        } else {
            String oldPath = mediaRecord.getData();
            String newPath = core2Full(resultName, oldPath);
            boolean canRename = !new File(newPath).exists();
            boolean isOldFileExist = new File(oldPath).exists();
            if (!isOldFileExist) {
                return R.string.record_file_not_exist;
            }
            if (canRename) {
                canRename = new File(oldPath).renameTo(new File(newPath));
            }
            if (canRename) {
                updateDB(mediaRecord, oldPath, newPath);
            } else {
                return R.string.error_title;
            }
        }
        return -1;
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    public boolean rename(Activity activity,
                          Uri uri,
                          String newName,
                          String suffix,
                          String mimeType,
                          boolean isRenameAndFirst) {
        int result = -1;
        try {
            result = MediaDBUtils.rename(uri, newName, suffix, mimeType);
        } catch (RecoverableSecurityException e) {
            if (!PermissionUtils.hasAllFilePermission() && isRenameAndFirst) {
                if (mOperatePermission == null) {
                    mOperatePermission = new CheckOperatorWithPermission(activity);
                }
                mOperatePermission.renameUri(uri, newName, suffix, mimeType, this);
                return false;
            }
            DebugUtil.e(TAG, "catch delete RecoverableSecurityException", e);
            try {
                activity.startIntentSenderForResult(e.getUserAction().getActionIntent().getIntentSender(), requestCode, null, 0, 0, 0);
            } catch (IntentSender.SendIntentException se) {
                DebugUtil.e(TAG, "start intent sender error", se);
            }
        }
        return result > 0;
    }

    public String getRenameContent() {
        if (mOperatePermission != null) {
            return mOperatePermission.getRenameContent();
        }
        return "";
    }

    public boolean getOperating() {
        if (mOperatePermission != null) {
            return mOperatePermission.getOperating();
        }
        return false;
    }

    public void resetOperating() {
        if (mOperatePermission != null) {
            mOperatePermission.resetContinueOperator();
        }
    }

    private void updateDB(Record mediaRecord, String oldPath, String newPath) {
        try {
            String title = full2Core(newPath);
            String displayName = title + newPath.substring(newPath.length() - 4);
            ContentValues cv = new ContentValues();
            cv.put(MediaStore.Audio.Media.DATA, newPath);
            cv.put(MediaStore.Audio.Media.TITLE, title);
            cv.put(MediaStore.Audio.Media.DISPLAY_NAME, displayName);
            Uri base = MediaDBUtils.BASE_URI;
            String where = MediaStore.Audio.Media.DATA + " COLLATE NOCASE =?";
            String[] whereArgs = new String[]{oldPath};
            Cursor cursor = null;
            long id = -1;
            try {
                cursor = BaseApplication.getAppContext().getContentResolver().query(base, null, where, whereArgs, null);
                if ((cursor != null) && (cursor.getCount() > 0) && cursor.moveToFirst()) {
                    id = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.AudioColumns._ID));
                    DebugUtil.i(TAG, "updateDB mediaDBid: " + id);
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "updateDB", e);
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            if (id != -1) {
                final Context context = BaseApplication.getAppContext();
                Uri uri = ContentUris.withAppendedId(base, id);
                int updateCount = context.getContentResolver().update(uri, cv, null, null);
                if (updateCount > 0) {
                    ContactCallRecordUtil.updatePathIfCallingRecord(context, mediaRecord, newPath);
                    CloudSyncRecorderDbUtil.updateDisplayName(oldPath, newPath, true, false);
                } else {
                    DebugUtil.e(TAG, "updateDB, " + FileUtils.getDisplayNameByPath(oldPath) + " to " + displayName + " failed, updateCount == 0");
                }
            } else {
                DebugUtil.e(TAG, "updateDB id = -1");
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateDB, the e is " + e);
        }
    }


    private String full2Core(String fullName) {
        String fileName = new File(fullName).getName();
        return fileName.substring(0, fileName.lastIndexOf('.'));
    }

    public String core2Full(String coreName, String fullName) {
        File theFile = new File(fullName);
        return theFile.getParent() + File.separator + coreName + fullName.substring(fullName.length() - NUM_4, fullName.length());
    }

    //to get clip result
    @SuppressLint("NewApi")
    private int clipPositive(Activity activity, final Record mediaRecord, String resultName) {
        if (resultName.trim().length() == 0) {
            return R.string.error_none_filename;
        }
        if (resultName.trim().startsWith(".")) {
            return R.string.notify_illegal_emoji_new;
        }
        final String displayName = mediaRecord.getDisplayName();
        final String suffix = displayName.substring(displayName.lastIndexOf("."));
        String toName = resultName + suffix;

        final long rowId = mediaRecord.getId();
        Uri mediaUri = MediaDBUtils.genUri(rowId);
        if (BaseUtil.isAndroidQOrLater()) {
            String relativePath = mediaRecord.getRelativePath();
            boolean newFileAlreadyExist = FileUtils.isFileExist(relativePath, toName);
            boolean oldFileExist = FileUtils.isFileExist(relativePath, displayName);
            DebugUtil.i(TAG, "clip rename relativePath: " + relativePath + ", newName: " + toName + ", oldName: " + displayName + ", newFileAlreadyExist: " + newFileAlreadyExist + ", oldFileExist: " + oldFileExist);

            if (!oldFileExist) {
                return R.string.record_file_not_exist;
            }
            if (newFileAlreadyExist) {
                return R.string.error_title;
            }

            boolean renameSuc = rename(activity,
                    mediaUri,
                    resultName,
                    suffix,
                    mediaRecord.getMimeType(),
                    false);
            if (!renameSuc) {
                return FAIL_WITH_SECURITY_EXCEPTION;
            }
            DebugUtil.i(TAG, "clip renameSuc " + renameSuc + ", rowId: " + rowId + ", originalName : " + displayName + ", newName " + resultName);
        } else {
            String originPath = mediaRecord.getData();
            File oldFile = new File(originPath);
            boolean isOldFileExist = oldFile.exists();
            File newFile = new File(oldFile.getParent(), resultName + suffix);
            String mRenameOutputStr = newFile.getAbsolutePath();
            boolean canRename = !new File(mRenameOutputStr).exists();
            if (!isOldFileExist) {
                return R.string.record_file_not_exist;
            }
            DebugUtil.i(TAG, "clip originPath: " + originPath + ", rowId: " + rowId + ", originalName : " + displayName + ", newName " + resultName + ", oldfile exist: " + isOldFileExist + ", newFileExist: " + canRename);
            boolean fileNameChanged = !originPath.toLowerCase().equalsIgnoreCase(mRenameOutputStr.toLowerCase());
            try {
                MediaDBUtils.updateRealSizeAndDurationAndName(mediaUri, mRenameOutputStr, fileNameChanged);
            } catch (Exception e) {
                DebugUtil.e(TAG, "clipPositive updateRealSizeAndDurationAndName error", e);
            }
            if (canRename) {
                canRename = new File(originPath).renameTo(new File(mRenameOutputStr));
            }
            if (!canRename && !(originPath.compareTo(mRenameOutputStr) == 0)) {
                return R.string.error_title;
            }
        }

        return -1;
    }

    public void release() {
        if (mOperatePermission != null) {
            mOperatePermission.release();
            mOperatePermission = null;
        }
    }
}