<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_buttonPanel"
    android:background="@color/coui_color_white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 使用ConstraintLayout，COUISeekBar在onMeasure无法拿到正确的宽度，包一层LinearLayout-->
    <LinearLayout
        android:id="@+id/ll_seekbar_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintTop_toTopOf="@id/tv_current"
        app:layout_constraintBottom_toBottomOf="@id/tv_current"
        app:layout_constraintStart_toEndOf="@id/tv_current"
        app:layout_constraintEnd_toStartOf="@id/tv_duration">

        <!--<include layout="@layout/layout_playback_seekbar" />-->

    </LinearLayout>

    <TextView
        android:id="@+id/tv_current"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/seekbar_time_margin"
        android:layout_marginBottom="@dimen/playback_tv_duration_marginBottom"
        android:contentDescription=""
        android:gravity="start|center_vertical"
        android:textColor="@color/coui_color_label_tertiary"
        android:textFontWeight="500"
        android:textSize="@dimen/seekbar_time_text_size"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        app:layout_constraintBottom_toTopOf="@id/middle_control"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="00:06" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/seekbar_time_margin"
        android:layout_marginEnd="@dimen/seekbar_time_margin"
        android:layout_marginBottom="@dimen/playback_tv_duration_marginBottom"
        android:contentDescription=""
        android:gravity="end|center_vertical"
        android:textColor="@color/coui_color_label_tertiary"
        android:textFontWeight="500"
        android:textSize="@dimen/seekbar_time_text_size"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        app:layout_constraintBottom_toTopOf="@id/middle_control"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="04:02" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/tool_center_guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/tool_right_guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="1"/>

    <RelativeLayout
        android:id="@+id/middle_control"
        android:layout_width="@dimen/circle_record_button_diam"
        android:layout_height="@dimen/circle_record_button_diam"
        android:layout_centerInParent="true"
        android:layout_marginBottom="@dimen/circle_playback_button_margin_bottom"
        android:importantForAccessibility="no"
        android:transitionName="sharedControlView"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tool_right_guide_line"
        app:layout_constraintStart_toStartOf="@id/tool_center_guide_line">

        <com.soundrecorder.playback.view.PlaybackAnimatedCircleButton
            android:id="@+id/red_circle_icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_pause_icon"
            android:transitionName="sharedRedCircleView"
            app:circle_color="@color/coui_color_container_theme_red"
            android:contentDescription="@string/talkback_play"
            app:circle_radius="@dimen/circle_record_button_radius" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/img_mark_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/play_btn_control_marginHorizontal"
        android:background="@drawable/sub_control_item_bg"
        android:src="@drawable/selector_playback_button_mark_new"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintStart_toStartOf="@id/tool_center_guide_line"
        app:layout_constraintTop_toTopOf="@id/middle_control" />

    <ImageView
        android:id="@+id/img_backward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/sub_control_item_bg"
        android:contentDescription="@string/back_three_seconds"
        android:src="@drawable/ic_backward"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintEnd_toStartOf="@id/middle_control"
        app:layout_constraintStart_toEndOf="@id/img_mark_add"
        app:layout_constraintTop_toTopOf="@id/middle_control" />

    <ImageView
        android:id="@+id/img_forward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/sub_control_item_bg"
        android:contentDescription="@string/fast_three_seconds"
        android:src="@drawable/ic_forward"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintEnd_toStartOf="@id/img_mark_list"
        app:layout_constraintStart_toEndOf="@id/middle_control"
        app:layout_constraintTop_toTopOf="@id/middle_control" />


    <ImageView
        android:id="@+id/img_mark_list"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/play_btn_control_marginHorizontal"
        android:background="@drawable/sub_control_item_bg"
        android:contentDescription="@string/cut"
        android:src="@drawable/ic_play_mark_list"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintEnd_toEndOf="@id/tool_right_guide_line"
        app:layout_constraintTop_toTopOf="@id/middle_control" />

    <include
        layout="@layout/include_activity_bottom_button"
        android:visibility="gone" />


</androidx.constraintlayout.widget.ConstraintLayout>


