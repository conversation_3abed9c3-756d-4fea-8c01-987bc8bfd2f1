/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditConstant
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord

import com.soundrecorder.wavemark.wave.view.WaveItemView

object EditConstant {

    /**
     * 最小裁切时长
     */
    const val MIN_CLIP_TIME = WaveItemView.TIME_GAP

    const val DURATION_60 = 60L
}