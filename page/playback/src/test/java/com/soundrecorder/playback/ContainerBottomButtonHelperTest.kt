/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ContainerBottomButtonHelperTest
 * Description:
 * Version: 1.0
 * Date: 2023/8/16
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/16 1.0 create
 */

package com.soundrecorder.playback

import android.os.Build
import androidx.core.view.isVisible
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.databinding.IncludeActivityBottomButtonBinding
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class ContainerBottomButtonHelperTest {
    private var activityController: ActivityController<PlaybackActivity>? = null
    private var activity: PlaybackActivity? = null
    private var bottomButtonBinding: IncludeActivityBottomButtonBinding? = null
    private val bottomButtonHelper = ContainerBottomButtonHelper()

    @Before
    fun setUp() {
        activityController = Robolectric.buildActivity(PlaybackActivity::class.java).create().start().resume()
        activity = activityController?.get()
        activity?.let {
            bottomButtonBinding =
                IncludeActivityBottomButtonBinding.bind(it.layoutInflater.inflate(R.layout.fragment_playback_container, null, false))
        }
    }

    @After
    fun tearDown() {
        activityController?.stop()
        activity = null
        activityController = null
    }

    @Test
    fun should_correct_when_setPlaybackBottomViewPosition() {
        // binding is null
        bottomButtonHelper.setPlaybackBottomViewPosition(null, false, true)
        Assert.assertNotNull(bottomButtonBinding)

        // view not visible
        bottomButtonBinding?.layoutMarkListActivity?.isVisible = false
        bottomButtonHelper.setPlaybackBottomViewPosition(bottomButtonBinding, false, true)

        // view is visible
        bottomButtonBinding?.layoutMarkListActivity?.isVisible = true
        bottomButtonHelper.setPlaybackBottomViewPosition(bottomButtonBinding, true, true)
    }

    @Test
    fun should_correct_when_setConvertBottomViewPosition() {
        // binding is null
        bottomButtonHelper.setConvertBottomViewPosition(null, false, false)
        Assert.assertNotNull(bottomButtonBinding)

        // view not visible
        bottomButtonBinding?.layoutConvertExportActivity?.isVisible = false
        bottomButtonHelper.setConvertBottomViewPosition(bottomButtonBinding, true, true)

        // view is visible,support roleSwitch
        bottomButtonBinding?.layoutConvertExportActivity?.isVisible = true
        bottomButtonHelper.setConvertBottomViewPosition(bottomButtonBinding, true, true)
        // view is visible,not support roleSwitch
        bottomButtonHelper.setConvertBottomViewPosition(bottomButtonBinding, false, true)
    }
}