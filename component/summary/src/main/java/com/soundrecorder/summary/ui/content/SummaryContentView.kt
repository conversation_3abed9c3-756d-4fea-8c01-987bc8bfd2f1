/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryContentView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePaddingRelative
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.scrollview.COUIScrollView
import com.coui.appcompat.textview.COUITextView
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.share.ShareAction
import com.soundrecorder.common.share.ShareSummaryCopy
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.exportfile.ExportDoc
import com.soundrecorder.summary.ui.content.SummaryAnimateTextView.TextAnimationListener
import com.soundrecorder.summary.ui.content.callback.ISummaryFunctionCallback
import com.soundrecorder.summary.ui.content.plugin.AgentClickPlugin
import com.soundrecorder.summary.ui.content.plugin.SummaryLinkResolver
import io.noties.markwon.Markwon
import io.noties.markwon.ext.tasklist.TaskListPlugin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SummaryContentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUIScrollView(context, attrs, defStyleAttr), OnClickListener {

    companion object {
        private const val TAG = "SummaryContentView"
        private const val LOADING_TIPS_DELAY = 2000L

        private const val ITEM_POSITION_0 = 0
        private const val ITEM_POSITION_1 = 1
        private const val ITEM_POSITION_2 = 2

        private const val SIZE_TWO = 2

        private const val TAG_NEXT = "next"
        private const val TAG_REFRESH = "refresh"
    }

    private lateinit var container: ConstraintLayout
    private lateinit var loadingView: EffectiveAnimationView
    private lateinit var content: SummaryAnimateTextView
    private lateinit var cardContainer: LinearLayout
    private lateinit var copyRight: COUITextView
    private lateinit var divider: View
    private lateinit var toolBar: RelativeLayout
    private lateinit var copy: AppCompatImageView
    private lateinit var export: AppCompatImageView
    private lateinit var scene: COUITextView
    private lateinit var previous: AppCompatImageView
    private lateinit var refresh: AppCompatImageView
    private lateinit var errorView: ConstraintLayout
    private lateinit var errorMsgText: COUITextView
    private lateinit var retry: COUITextView

    private val agentDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(context.resources, com.soundrecorder.summary.R.drawable.ic_agent_un_check, context.theme)
    }
    private val agentUnCheckDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(context.resources, com.soundrecorder.summary.R.drawable.ic_agent_check, context.theme)
    }

    private val outlineColor: Int by lazy {
        ResourcesCompat.getColor(context.resources, com.support.appcompat.R.color.coui_color_label_tertiary, context.theme)
    }

    private val fillColor: Int by lazy {
        ResourcesCompat.getColor(context.resources, com.support.appcompat.R.color.coui_color_container_theme_red, context.theme)
    }

    private var scenePopList: COUIPopupListWindow? = null
    private var exportPopList: COUIPopupListWindow? = null

    private val activity = context as Activity
    private val lifecycle = (context as? AppCompatActivity)?.lifecycleScope
    private var loadingJob: Job? = null
    private var summaryFunctionCallback: ISummaryFunctionCallback? = null
    private var summaryContentText: String = ""
    private var summaryOriginText: String = ""

    private var currentScene: Int = 0
    private var identifiedTheme: Int = 0

    private var isLoadingFinish = false


    private fun initChild() {
        initContainer()
        initLoading()
        initContent()
        initTools()
        initCardContainer()
        initOther()
        initErrorView()
    }

    private fun initContainer() {
        LayoutInflater.from(context)
            .inflate(com.soundrecorder.summary.R.layout.layout_summary_container_view, this, true)
        val paddingStart =
            context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp24)
        val paddingTop =
            context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp12)
        updatePaddingRelative(paddingStart, paddingTop, paddingStart, paddingTop)
        container = findViewById(com.soundrecorder.summary.R.id.container_view)
    }

    private fun initLoading() {
        loadingView = findViewById(com.soundrecorder.summary.R.id.summary_loading)
        loadingView.visible()
        loadingView.playAnimation()
        loadingJob = lifecycle?.launch {
            delay(LOADING_TIPS_DELAY)
            showLoadingTips()
        }
    }

    private fun showLoadingTips() {
        copyRight.setText(com.soundrecorder.base.R.string.summary_loading_tips)
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_loading
        copyRight.visible()
    }

    private fun initContent() {
        content = findViewById(com.soundrecorder.summary.R.id.summary_content)
        content.movementMethod = LinkMovementMethod.getInstance()
        content.highlightColor = Color.TRANSPARENT
    }

    private fun initTools() {
        toolBar = findViewById(com.soundrecorder.summary.R.id.summary_tool_bar)
        copy = findViewById(com.soundrecorder.summary.R.id.copy)
        export = findViewById(com.soundrecorder.summary.R.id.export)
        scene = findViewById(com.soundrecorder.summary.R.id.summary_scene)
        previous = findViewById(com.soundrecorder.summary.R.id.previous)
        previous.gone()
        refresh = findViewById(com.soundrecorder.summary.R.id.refresh)

        copy.setOnClickListener(this)
        export.setOnClickListener(this)
        scene.setOnClickListener(this)
        previous.setOnClickListener(this)
        refresh.setOnClickListener(this)
    }

    private fun initCardContainer() {
        cardContainer = findViewById(com.soundrecorder.summary.R.id.card_container)
    }

    private fun initOther() {
        copyRight = findViewById(com.soundrecorder.summary.R.id.copyright)
        divider = findViewById(com.soundrecorder.summary.R.id.divider_line)
    }

    private fun initErrorView() {
        errorView = findViewById(com.soundrecorder.summary.R.id.layout_error)
        errorMsgText = findViewById(com.soundrecorder.summary.R.id.error_msg_text)
        retry = findViewById(com.soundrecorder.summary.R.id.retry)
        retry.setOnClickListener(this)
        COUITextViewCompatUtil.setPressRippleDrawable(retry)
    }

    init {
        initChild()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        loadingJob?.cancel()
        loadingView.cancelAnimation()
        content.cancelAnimation()
        scenePopList?.dismiss()
    }

    override fun onClick(v: View?) {
        v ?: return
        if (isLoadingFinish.not()) {
            return
        }
        if (ClickUtils.isQuickClick()) {
            return
        }
        when (v) {
            copy -> copySummary()
            export -> showExportPopMenu()
            scene -> showScenePopMenu()
            previous -> clickPrevious()
            refresh -> clickRefreshOrNext()
            retry -> clickRetry()
            else -> DebugUtil.w(TAG, "click what ? v $v")
        }
    }

    private fun copySummary() {
        summaryFunctionCallback?.onClickCopy()
        lifecycle ?: return
        val shareTextContent = summaryOriginText
        if (summaryOriginText.isEmpty()) {
            DebugUtil.w(TAG, "why is empty? Check again carefully")
            return
        }
        ShareAction.share(
            activity,
            ShareTextContent(-1, false, "", 0, emptyList()),
            ShareSummaryCopy(shareTextContent),
            lifecycle,
            null
        )
    }

    private fun showExportPopMenu() {
        exportPopList?.dismiss()
        lifecycle?.launch {
            val isSupportExportDoc = withContext(Dispatchers.Default) {
                ExportDoc.isSupportExport(context)
            }
            val exportItemList = mutableListOf<PopupListItem>().apply {
                val builder = PopupListItem.Builder()
                if (isSupportExportDoc) {
                    builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_doc)
                        .setTitle(context.getString(
                            com.soundrecorder.common.R.string.summary_export_to,
                            context.getString(com.soundrecorder.common.R.string.word)
                        ))
                        .setGroupId(com.soundrecorder.common.R.id.group1)
                    add(builder.build())
                }
                builder.reset()

                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_pdf)
                    .setTitle(context.getString(
                        com.soundrecorder.common.R.string.summary_export_to,
                        context.getString(com.soundrecorder.common.R.string.pdf)
                    ))
                    .setGroupId(com.soundrecorder.common.R.id.group2)
                add(builder.build())


                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_note)
                    .setTitle(context.getString(com.soundrecorder.common.R.string.summary_export_to_note))
                    .setGroupId(com.soundrecorder.common.R.id.group3)
                add(builder.build())
            }

            exportPopList = COUIPopupListWindow(context).apply {
                this.itemList = exportItemList
                this.anchorView = export
                this.resetOffset()
                this.setOnItemClickListener { _, _, pos, _ ->
                    when {
                        itemList.size > SIZE_TWO -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToDoc()
                                ITEM_POSITION_1 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }
                        else -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }
                    }
                    this.dismiss()
                }
                this.show()
            }
        }
    }

    private fun exportToDoc() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_WORD)
    }

    private fun exportToPdf() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_PDF)
    }

    private fun exportToNote() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_NOTE)
    }

    private fun showScenePopMenu() {
        scenePopList?.dismiss()
        scenePopList = COUIPopupListWindow(context).apply {
            this.itemList = getSceneList()
            this.anchorView = scene
            this.resetOffset()
            this.setOnItemClickListener { _, _, position, _ ->
                val theme = when {
                    itemList.size > SIZE_TWO -> {
                        when (position) {
                            ITEM_POSITION_0 -> identifiedTheme
                            ITEM_POSITION_1 -> SummaryTheme.SAMPLE
                            else -> SummaryTheme.DETAIL
                        }
                    }
                    else -> {
                        when (position) {
                            ITEM_POSITION_0 -> SummaryTheme.SAMPLE
                            else -> SummaryTheme.DETAIL
                        }
                    }
                }
                currentScene = theme
                summaryFunctionCallback?.onClickScene(theme)
                scene.text = SummaryTheme.getTitle(context, currentScene)
                this.dismiss()
            }
        }
        scenePopList?.show()
    }

    private fun getSceneList(): List<PopupListItem> {
        return mutableListOf<PopupListItem>().apply {
            val builder = PopupListItem.Builder()
            if (identifiedTheme != SummaryTheme.DETAIL && identifiedTheme != SummaryTheme.SAMPLE) {
                builder.setTitle(SummaryTheme.getTitle(context, identifiedTheme))
                    .setId(com.soundrecorder.summary.R.id.current_scene)
                    .setGroupId(com.soundrecorder.common.R.id.group1)
                    .setIsChecked(identifiedTheme == currentScene)
                add(builder.build())
            }

            builder.reset()
            builder.setTitle(SummaryTheme.getTitle(context, SummaryTheme.SAMPLE))
                .setId(com.soundrecorder.summary.R.id.sample_scene)
                .setGroupId(com.soundrecorder.common.R.id.group2)
                .setIsChecked(currentScene == SummaryTheme.SAMPLE)
            add(builder.build())

            builder.reset()
            builder.setTitle(SummaryTheme.getTitle(context, SummaryTheme.DETAIL))
                .setId(com.soundrecorder.summary.R.id.detail_scene)
                .setGroupId(com.soundrecorder.common.R.id.group3)
                .setIsChecked(currentScene == SummaryTheme.DETAIL)
            add(builder.build())
        }
    }

    private fun clickPrevious() {
        summaryFunctionCallback?.onClickPrevious()
        refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
    }

    private fun clickRefreshOrNext() {
        if (refresh.tag == TAG_REFRESH) {
            summaryFunctionCallback?.onClickRefresh()
        } else {
            summaryFunctionCallback?.onClickNext()
        }
    }

    private fun clickRetry() {
        summaryFunctionCallback?.onClickRetry()
    }


    fun setSummaryFunctionCallback(callback: ISummaryFunctionCallback) {
        summaryFunctionCallback = callback
    }

    fun setSummaryContent(summaryModel: SummaryModel) {
        container.updateLayoutParams<LayoutParams> {
            height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        loadingView.cancelAnimation()
        loadingView.gone()
        copyRight.visible()
        divider.visible()
        toolBar.visible()
        val markdown = buildMarkDown()
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            summaryModel.summary,
            summaryModel.summaryTrace,
            summaryModel.entities,
            summaryModel.agentEvents
        )
        val spanned = markdown.toMarkdown(formatStream)
        content.text = spanned
        summaryContentText = spanned.toString()
        summaryOriginText = summaryModel.summary
        isLoadingFinish = true
    }

    /**
     * 检测是否是最新的摘要，如果是，视觉上会有一些不同的UI
     */
    fun checkCurrentState(isLastSummary: Boolean, isOnly: Boolean, isFirstSummary: Boolean) {
        when {
            isOnly -> {
                previous.gone()
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_refresh)
                refresh.tag = TAG_REFRESH
            }
            isLastSummary -> {
                previous.visible()
                previous.isEnabled = true
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_refresh)
                refresh.tag = TAG_REFRESH
            }
            isFirstSummary -> {
                previous.visible()
                previous.isEnabled = false
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }
            else -> {
                previous.visible()
                previous.isEnabled = true
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }
        }
    }

    fun onStartSummary() {
        lifecycle?.launch(Dispatchers.Main) {
            container.updateLayoutParams<LayoutParams> {
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            }
            loadingView.visible()
            loadingView.playAnimation()
            content.gone()
            cardContainer.gone()
            copyRight.gone()
            divider.gone()
            toolBar.gone()
            errorView.gone()
        }
    }

    fun updateStream(stream: String) {
        lifecycle?.launch(Dispatchers.Main) {
            loadingView.cancelAnimation()
            loadingView.gone()
            toolBar.gone()
            divider.gone()
            errorView.gone()
            showCopyright()
            content.visible()
            content.cancelAnimation()
            val markdown = buildMarkDown()
            val summaryAgent = SummaryDataParser.parseAgent(context, stream)
            val formatStream = SummaryStyleHelper.formatSummaryContent(
                context,
                stream,
                emptyList(),
                emptyList(),
                summaryAgent
            )
            val spanned = markdown.toMarkdown(formatStream)
            summaryContentText = formatStream
            summaryOriginText = stream
            content.setAnimateText(spanned, false)
            content.setAnimationListener(object : TextAnimationListener {
                override fun onAnimationEnd() {
                    content.text = spanned
                }

                override fun onAnimationUpdate(curReadyPos: Int) {
                    super.onAnimationUpdate(curReadyPos)
                    smoothScrollToVisibleText(curReadyPos)
                }
            })
        }
    }

    private fun smoothScrollToVisibleText(curReadyPos: Int) {
        val layout = content.layout ?: return
        val startLine = layout.getLineForOffset(0)
        val endLine = layout.getLineForOffset(curReadyPos)
        val height = layout.getLineBottom(endLine) - layout.getLineTop(startLine)
        val offset = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp12)
        val scrollDistance = height + paddingTop - measuredHeight
        if (scrollDistance > 0) {
            smoothCOUIScrollTo(0, scrollDistance + offset)
        }
    }

    private fun buildMarkDown(): Markwon {
        return Markwon.builder(context)
            .usePlugin(
                if (agentDrawable != null && agentUnCheckDrawable != null) {
                    TaskListPlugin.create(agentDrawable, agentUnCheckDrawable)
                } else {
                    TaskListPlugin.create(context)
                }
            )
            .usePlugin(AgentClickPlugin(context) { isDone, task ->
                agentClick(isDone, task)
            }).applyLinkResolver(SummaryLinkResolver(context) { view, entry ->
                linkClick(view, entry)
            })
            .build()
    }

    private fun agentClick(isDone: Boolean, task: String) {
        summaryFunctionCallback?.onClickAgent(isDone, task)
        summaryContentText = SummaryStyleHelper.updateStyleAfterTaskStateChange(summaryContentText, isDone, task)
    }

    private fun linkClick(view: View, entity: SummaryEntity) {
        summaryFunctionCallback?.onClickEntity(view, entity)
    }

    private fun showCopyright() {
        loadingJob?.cancel()
        copyRight.visible()
        copyRight.setText(com.soundrecorder.base.R.string.summary_copyright)
        copyRight.setCompoundDrawablesRelativeWithIntrinsicBounds(com.soundrecorder.summary.R.drawable.ic_copyright, 0, 0, 0)
        copyRight.compoundDrawablePadding =
            context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp4)
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_content
    }

    fun onFinishSummary(
        fullContent: String,
        summaryEntity: List<SummaryEntity>,
        theme: SummaryTheme
    ) {
        lifecycle?.launch(Dispatchers.Main) {
            container.updateLayoutParams<LayoutParams> {
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            }
            copyRight.visible()
            divider.visible()
            toolBar.visible()
            errorView.gone()
            updateScene(theme)
            DebugUtil.d(TAG, "onFinishSummary = $fullContent")
            val markdown = buildMarkDown()
            val summaryAgent = SummaryDataParser.parseAgent(context, fullContent)
            val formatStream = SummaryStyleHelper.formatSummaryContent(context, fullContent, emptyList(), summaryEntity, summaryAgent)
            val spanned = markdown.toMarkdown(formatStream)
            if (fullContent == summaryOriginText) {
                isLoadingFinish = true
            }
            summaryContentText = formatStream
            summaryOriginText = fullContent
            content.setAnimateText(spanned, false)

            content.setAnimationListener(object : TextAnimationListener {
                override fun onAnimationEnd() {
                    content.text = spanned
                    isLoadingFinish = true
                    fullScroll(View.FOCUS_DOWN)
                }

                override fun onAnimationUpdate(curReadyPos: Int) {
                    super.onAnimationUpdate(curReadyPos)
                    smoothScrollToVisibleText(curReadyPos)
                }
            })
        }
    }

    fun onError(canRetry: Boolean, errorMsg: String) {
        DebugUtil.d(TAG, "onError canRetry ")
        lifecycle?.launch(Dispatchers.Main) {
            loadingJob?.cancel()
            loadingView.cancelAnimation()
            loadingView.gone()
            toolBar.gone()
            divider.gone()
            errorView.gone()
            copyRight.gone()
            content.gone()
            container.updateLayoutParams<LayoutParams> {
                height = ViewGroup.LayoutParams.MATCH_PARENT
            }
            errorView.visible()
            errorMsgText.text = errorMsg
            if (canRetry) {
                retry.visible()
            } else {
                retry.gone()
            }
            isLoadingFinish = true
        }
    }

    fun updateSummaryTrace(summaryTrace: List<SummaryTrace>) {
        DebugUtil.d(TAG, "updateSummaryTrace summaryTrace = $summaryTrace:")
    }

    private fun updateScene(theme: SummaryTheme) {
        currentScene = theme.currentTheme
        identifiedTheme = theme.currentTheme
        scene.text = SummaryTheme.getTitle(context, currentScene)
    }

    fun getSummaryText(): String {
        return summaryContentText
    }

    fun getCurrentTheme(): Int {
        return currentScene
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        measureChildren(widthMeasureSpec, heightMeasureSpec)
        var totalHeight = 0
        container.children.forEach {
            if (it.isVisible) {
                totalHeight += it.measuredHeight
            }
        }
        totalHeight += context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp12) * 2
        var heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        /*
        * 如何是错误视图显示，则全屏显示
         */
        if (errorView.isVisible) {
            heightMode = MeasureSpec.EXACTLY
        }

        val measuredHeight = when (heightMode) {
            MeasureSpec.EXACTLY -> heightSize
            MeasureSpec.AT_MOST -> totalHeight.coerceAtMost(heightSize)
            MeasureSpec.UNSPECIFIED -> totalHeight
            else -> totalHeight
        }
        DebugUtil.d(TAG, "onMeasure measuredHeight = $measuredHeight")
        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), measuredHeight)
    }
}