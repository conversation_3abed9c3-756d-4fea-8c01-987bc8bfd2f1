<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/preference"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/coui_list_item_left_padding"
    android:paddingVertical="@dimen/dp4"
    android:paddingEnd="@dimen/dp4">

    <TextView
        style="@style/couiTextButtonM"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.0"
        android:textColor="@color/coui_color_label_primary"
        android:text="@string/group_my"
        android:textDirection="locale" />

    <TextView
        android:id="@+id/create_group_item"
        style="@style/couiTextButtonM"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:text="@string/create_new_group_tab"
        android:textColor="@color/coui_text_btn_text_color" />

</LinearLayout>
