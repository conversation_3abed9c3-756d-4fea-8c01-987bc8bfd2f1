/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlayWaveRecyclerView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import com.soundrecorder.playback.R
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.wavemark.wave.load.SoundFile
import com.soundrecorder.wavemark.wave.view.WaveItemView
import com.soundrecorder.wavemark.wave.view.WaveRecyclerView
import kotlin.math.abs

class PlayWaveRecyclerView(context: Context?, attrs: AttributeSet?) :
    WaveRecyclerView<WaveItemView>(context, attrs) {

    companion object {
        const val TAG = "PlayWaveRecyclerView"
    }

    override fun onInterceptTouchEvent(e: MotionEvent?): Boolean {
        if (mCanScrollHorizontally) {
            super.onInterceptTouchEvent(e)
        }
        return true
    }

    override fun onTouchEvent(e: MotionEvent): Boolean {
        checkMarkRegion(e)
        if (mCanScrollHorizontally) {
            when (e.action) {
                MotionEvent.ACTION_DOWN -> mTouchDownX = e.x
                MotionEvent.ACTION_UP -> {
                    val touchDeltaX = e.x - mTouchDownX
                    if (abs(touchDeltaX) > mSlop) {
                        DebugUtil.i(
                            TAG,
                            "onDragged invoked , mTouchDeltaX: $touchDeltaX, mSlop: $mSlop"
                        )
                        if (mDragListener != null) {
                            mDragListener.onDragged()
                        }
                    }
                }
            }
            return super.onTouchEvent(e)
        }
        return true
    }

    override fun createNewItemView(context: Context, parent: ViewGroup): WaveItemView {
        return LayoutInflater.from(context).inflate(R.layout.item_ruler_play, parent, false) as WaveItemView
    }

    override fun fixItemCount(totalCount: Int): Int {
        return totalCount
    }

    override fun onItemViewCreated(parent: ViewGroup, rulerView: WaveItemView) {
        //do nothing
    }

    override fun onBindItemView(rulerView: WaveItemView, position: Int) {
        rulerView.amplitudes = mAmplitudeValue
        rulerView.setDecodedAmplitudeList(mDecodedAmplitudeList)

        rulerView.setLineEnhanceRecording(isDirectOn)
//      rulerView.setPlayLineEnhanceRecording(isDirectOn)
        rulerView.directTime = directTime
        rulerView.duration = duration
    }

    fun setSoundFile(soundFile: SoundFile?) {
        mDecodedAmplitudeList = soundFile?.amplitudeList
    }

    override fun calculateCorrectOffset(currentTimeMillis: Long, offset: Int): Int {
        var calculateOffset = offset
        if (mTotalTime > 0 && currentTimeMillis == mTotalTime) {
            calculateOffset -= diffMoreThanLastItemRealWidth
        }
        return calculateOffset
    }
}