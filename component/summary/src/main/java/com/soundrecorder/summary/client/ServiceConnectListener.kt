/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ServiceConnectListener
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.client

import android.content.ComponentName
import com.soundrecorder.base.utils.DebugUtil

interface ServiceConnectListener {
    companion object {
        private const val TAG = "ServiceConnectListener"
    }

    fun onConnectSuccess()

    fun onConnectFailed(errMsg: String?) {
        DebugUtil.w(TAG, "onConnectFailed: $errMsg")
    }
    fun onDisconnected(name: ComponentName?) {
        DebugUtil.w(TAG, "onDisconnected: name = $name")
    }
}