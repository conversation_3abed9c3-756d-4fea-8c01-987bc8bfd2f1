/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.soundrecorder.miniapp.view.wave

import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.miniapp.view.wave.WaveViewUtil.getOneWaveWidth
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import kotlin.math.ceil

internal class WaveAdapter(private val iWaveItemViewDelegate: IWaveItemViewDelegate) : RecyclerView.Adapter<WaveViewHolder>() {
    private val mMarkTimeList = ArrayList<MarkDataBean>()

    override fun getItemViewType(position: Int): Int {
        return when (position) {
            0 -> VIEW_TYPE_HEADER
            itemCount - 1 -> VIEW_TYPE_FOOTER
            else -> VIEW_TYPE_WAVE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WaveViewHolder {
        val width: Int = when (viewType) {
            VIEW_TYPE_HEADER -> iWaveItemViewDelegate.halfWidth()
            VIEW_TYPE_FOOTER -> iWaveItemViewDelegate.halfWidth()
            else -> parent.context.getOneWaveWidth().toInt()
        }
        return WaveViewHolder(iWaveItemViewDelegate.createNewItemView(parent).apply {
            layoutParams = LayoutParams(width, LayoutParams.MATCH_PARENT)
        })
    }

    override fun onBindViewHolder(holder: WaveViewHolder, position: Int) {
        iWaveItemViewDelegate.onBindItemView(holder.waveView, position)
        holder.waveView.setCurViewIndex(position)
        if (mMarkTimeList.isNotEmpty()) {
            for (markData in mMarkTimeList) {
                val index = ceil(markData.correctTime / WAVE_ITEM_TIME).toInt()
                if (index == position) {
                    holder.waveView.setCurHasMark(true)
                    break
                } else {
                    holder.waveView.setCurHasMark(false)
                }
            }
        } else {
            holder.waveView.setCurHasMark(false)
        }
        holder.waveView.postInvalidate()
    }

    override fun getItemCount(): Int = Int.MAX_VALUE

    fun clearMarkTimeList() {
        mMarkTimeList.clear()
    }

    fun setMarkTimeList(markTimeList: List<MarkDataBean?>?) {
        mMarkTimeList.clear()
        //新的标记数组，只有三个数据，并且是从大到小排列的
        val newMarkList = markTimeList?.run {
            if (size <= MARK_LIST_SIZE) {
                this
            } else {
                subList(size - MARK_LIST_SIZE, size)
            }.reversed()
        }
        if (newMarkList != null && newMarkList.isNotEmpty()) {
            val currentTime = RecorderViewModelAction.getAmplitudeCurrentTime()
            for (markDataBean in newMarkList) {
                if (markDataBean != null && (currentTime - markDataBean.correctTime) <= WAVE_WHOLE_TIME) {
                    mMarkTimeList.add(markDataBean)
                }
            }
        }
    }

    companion object {
        const val VIEW_TYPE_HEADER = 0
        const val VIEW_TYPE_WAVE = 1
        const val VIEW_TYPE_FOOTER = 2
        const val MARK_LIST_SIZE = 3
        const val WAVE_WHOLE_TIME = 3500
        const val WAVE_ITEM_TIME = 70F
    }
}